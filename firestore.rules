rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Global users collection for tenant assignment and authentication
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Tenant-specific data under dentalcare collection
    match /dentalcare/{tenantId}/{document=**} {
      allow read, write: if request.auth != null 
        && isValidTenantUser(tenantId);
    }
    
    // Helper function to verify user belongs to tenant
    function isValidTenantUser(tenantId) {
      return exists(/databases/$(database)/documents/users/$(request.auth.uid))
        && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.tenantId == tenantId;
    }
    
    // Additional security for sensitive operations
    match /dentalcare/{tenantId}/users/{userId} {
      allow read: if request.auth != null && isValidTenantUser(tenantId);
      allow write: if request.auth != null && isValidTenantUser(tenantId)
        && (request.auth.uid == userId || hasAdminRole(tenantId));
    }
    
    match /dentalcare/{tenantId}/settings/{document} {
      allow read: if request.auth != null && isValidTenantUser(tenantId);
      allow write: if request.auth != null && isValidTenantUser(tenantId)
        && hasAdminRole(tenantId);
    }
    
    // Helper function to check admin role
    function hasAdminRole(tenantId) {
      let userDoc = get(/databases/$(database)/documents/dentalcare/$(tenantId)/users/$(request.auth.uid));
      return userDoc.data.role in ['admin', 'manager'];
    }
  }
}
