import { 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  onSnapshot
} from 'firebase/firestore';
import { Treatment } from '@/types';
import { TenantService } from './base/TenantService';

export class TreatmentService extends TenantService {

  /**
   * Create new treatment
   */
  async createTreatment(treatmentData: Omit<Treatment, 'id'>): Promise<string> {
    try {
      this.validateTenantAccess();
      
      const newTreatment: Omit<Treatment, 'id'> = {
        ...treatmentData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      const docRef = await addDoc(this.getCollection('treatments'), newTreatment);
      return docRef.id;
    } catch (error) {
      this.handleError(error, 'create treatment');
    }
  }

  /**
   * Get treatment by ID
   */
  async getTreatment(id: string): Promise<Treatment | null> {
    try {
      this.validateTenantAccess();
      const docRef = this.getDocument('treatments', id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as Treatment;
      }
      return null;
    } catch (error) {
      this.handleError(error, 'get treatment');
    }
  }

  /**
   * Get all treatments for current tenant
   */
  async getTreatments(): Promise<Treatment[]> {
    try {
      this.validateTenantAccess();
      const q = query(
        this.getCollection('treatments'),
        orderBy('name')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Treatment[];
    } catch (error) {
      this.handleError(error, 'get treatments');
    }
  }

  /**
   * Get treatments by category
   */
  async getTreatmentsByCategory(category: string): Promise<Treatment[]> {
    try {
      this.validateTenantAccess();
      const q = query(
        this.getCollection('treatments'),
        where('category', '==', category),
        orderBy('name')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Treatment[];
    } catch (error) {
      this.handleError(error, 'get treatments by category');
    }
  }

  /**
   * Search treatments by name or code
   */
  async searchTreatments(searchTerm: string): Promise<Treatment[]> {
    try {
      this.validateTenantAccess();
      // Get all treatments first (client-side filtering for now)
      const treatments = await this.getTreatments();
      
      const lowercaseSearch = searchTerm.toLowerCase();
      return treatments.filter((treatment: Treatment) => 
        treatment.name.toLowerCase().includes(lowercaseSearch) ||
        treatment.code.toLowerCase().includes(lowercaseSearch) ||
        treatment.category.toLowerCase().includes(lowercaseSearch)
      );
    } catch (error) {
      this.handleError(error, 'search treatments');
    }
  }

  /**
   * Update treatment
   */
  async updateTreatment(id: string, updates: Partial<Treatment>): Promise<void> {
    try {
      this.validateTenantAccess();
      const docRef = this.getDocument('treatments', id);
      await updateDoc(docRef, {
        ...updates,
        updatedAt: new Date().toISOString()
      });
    } catch (error) {
      this.handleError(error, 'update treatment');
    }
  }

  /**
   * Delete treatment
   */
  async deleteTreatment(id: string): Promise<void> {
    try {
      this.validateTenantAccess();
      const docRef = this.getDocument('treatments', id);
      await deleteDoc(docRef);
    } catch (error) {
      this.handleError(error, 'delete treatment');
    }
  }

  /**
   * Get treatment categories
   */
  async getTreatmentCategories(): Promise<string[]> {
    try {
      this.validateTenantAccess();
      const treatments = await this.getTreatments();
      const categories = new Set(treatments.map(t => t.category));
      return Array.from(categories).sort();
    } catch (error) {
      this.handleError(error, 'get treatment categories');
    }
  }

  /**
   * Get treatments by price range
   */
  async getTreatmentsByPriceRange(minPrice: number, maxPrice: number): Promise<Treatment[]> {
    try {
      this.validateTenantAccess();
      const treatments = await this.getTreatments();
      
      return treatments.filter(treatment => 
        treatment.price >= minPrice && treatment.price <= maxPrice
      );
    } catch (error) {
      this.handleError(error, 'get treatments by price range');
    }
  }

  /**
   * Get most popular treatments (based on usage in appointments)
   */
  async getPopularTreatments(limit: number = 10): Promise<Treatment[]> {
    try {
      this.validateTenantAccess();
      // This would require aggregating appointment data
      // For now, return all treatments sorted by name
      const treatments = await this.getTreatments();
      return treatments.slice(0, limit);
    } catch (error) {
      this.handleError(error, 'get popular treatments');
    }
  }

  /**
   * Bulk create treatments
   */
  async bulkCreateTreatments(treatmentsData: Omit<Treatment, 'id'>[]): Promise<string[]> {
    try {
      this.validateTenantAccess();
      const createdIds: string[] = [];
      
      for (const treatmentData of treatmentsData) {
        const id = await this.createTreatment(treatmentData);
        createdIds.push(id);
      }
      
      return createdIds;
    } catch (error) {
      this.handleError(error, 'bulk create treatments');
    }
  }

  /**
   * Real-time subscription to treatments
   */
  subscribeToTreatments(callback: (treatments: Treatment[]) => void) {
    this.validateTenantAccess();
    const q = query(
      this.getCollection('treatments'),
      orderBy('name')
    );

    return onSnapshot(q, (snapshot) => {
      const treatments = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Treatment[];
      callback(treatments);
    }, (error) => {
      console.error('Error in treatments subscription:', error);
    });
  }

  /**
   * Subscribe to treatments by category
   */
  subscribeToTreatmentsByCategory(category: string, callback: (treatments: Treatment[]) => void) {
    this.validateTenantAccess();
    const q = query(
      this.getCollection('treatments'),
      where('category', '==', category),
      orderBy('name')
    );

    return onSnapshot(q, (snapshot) => {
      const treatments = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Treatment[];
      callback(treatments);
    }, (error) => {
      console.error('Error in treatments by category subscription:', error);
    });
  }
}
