# Multi-Tenant DentalCare Implementation Guide

## Overview

This guide explains the multi-tenant architecture implemented for the DentalCare application, allowing multiple dental clinics to use the same Firebase project while maintaining complete data isolation.

## Architecture

### Database Structure

The new multi-tenant structure uses a hierarchical approach with `dentalcare` as the root collection:

```
dentalcare/{tenantId}/
├── users/{userId}
├── patients/{patientId}
├── appointments/{appointmentId}
├── treatments/{treatmentId}
├── inventory/{inventoryId}
└── settings/
    ├── clinic
    ├── billing
    └── preferences
```

### Key Components

1. **TenantContext** - Manages current tenant state
2. **TenantService** - Base class for tenant-aware operations
3. **Security Rules** - Enforces tenant-level data isolation
4. **Service Registry** - Manages tenant-specific service instances

## Security Rules

The Firestore security rules ensure complete tenant isolation:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Global users collection for tenant assignment
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Tenant-specific data
    match /dentalcare/{tenantId}/{document=**} {
      allow read, write: if request.auth != null 
        && isValidTenantUser(tenantId);
    }
    
    function isValidTenantUser(tenantId) {
      return exists(/databases/$(database)/documents/users/$(request.auth.uid))
        && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.tenantId == tenantId;
    }
  }
}
```

## Usage

### 1. Using Tenant Context

```typescript
import { useTenant } from '@/contexts/TenantContext';

function MyComponent() {
  const { tenant, tenantId, loading } = useTenant();
  
  if (loading) return <div>Loading...</div>;
  if (!tenant) return <div>No tenant selected</div>;
  
  return <div>Current tenant: {tenant.name}</div>;
}
```

### 2. Creating Tenant-Aware Services

```typescript
import { TenantService } from '@/services/base/TenantService';

export class MyService extends TenantService {
  async getData() {
    this.validateTenantAccess();
    const collection = this.getCollection('myData');
    // ... perform operations
  }
}
```

### 3. Using Service Registry

```typescript
import { TenantServiceRegistry } from '@/services/base/TenantService';
import { PatientService } from '@/services/patients';

// Get tenant-specific service instance
const patientService = TenantServiceRegistry.getService(
  PatientService, 
  tenantId, 
  'patients'
);
```

### 4. Creating Hooks

```typescript
export function usePatients() {
  const { tenantId } = useTenant();
  
  return useQuery({
    queryKey: ['patients', tenantId],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const service = TenantServiceRegistry.getService(PatientService, tenantId, 'patients');
      return service.getPatients();
    },
    enabled: !!tenantId,
  });
}
```

## Migration

### Running the Migration

To migrate existing single-tenant data to the new multi-tenant structure:

```typescript
import { DataMigration } from '@/utils/migration';

// Run migration (one-time operation)
await DataMigration.migrateToMultiTenant();
```

### Migration Process

1. **Users Migration**: Updates user profiles with `tenantId` and creates tenant mappings
2. **Data Migration**: Moves all collections to tenant-specific subcollections
3. **Settings Migration**: Creates tenant-specific settings documents

## Best Practices

### 1. Always Use Tenant Context

```typescript
// ✅ Good
const { tenantId } = useTenant();
const service = TenantServiceRegistry.getService(MyService, tenantId, 'myService');

// ❌ Bad
const service = new MyService(hardcodedTenantId);
```

### 2. Validate Tenant Access

```typescript
export class MyService extends TenantService {
  async myMethod() {
    this.validateTenantAccess(); // Always validate first
    // ... rest of method
  }
}
```

### 3. Handle Errors Properly

```typescript
export class MyService extends TenantService {
  async myMethod() {
    try {
      // ... operations
    } catch (error) {
      this.handleError(error, 'operation description');
    }
  }
}
```

### 4. Use Proper Query Keys

```typescript
// ✅ Include tenantId in query keys
queryKey: ['patients', tenantId]

// ❌ Don't use global query keys
queryKey: ['patients']
```

## Tenant Management

### Creating a New Tenant

```typescript
const { createTenant } = useTenant();

const newTenantId = await createTenant({
  name: 'New Dental Clinic',
  address: '123 Main St',
  phone: '555-0123',
  email: '<EMAIL>'
});
```

### Switching Tenants

```typescript
const { switchTenant } = useTenant();

await switchTenant('new-tenant-id');
```

## Testing

### Unit Tests

```typescript
describe('PatientService', () => {
  it('should create patient in correct tenant', async () => {
    const service = new PatientService('test-tenant-id');
    const patientId = await service.createPatient(patientData);
    
    // Verify patient was created in correct tenant collection
    expect(patientId).toBeDefined();
  });
});
```

### Integration Tests

```typescript
describe('Multi-tenant isolation', () => {
  it('should not allow cross-tenant data access', async () => {
    const tenant1Service = new PatientService('tenant-1');
    const tenant2Service = new PatientService('tenant-2');
    
    await tenant1Service.createPatient(patientData);
    const patients = await tenant2Service.getPatients();
    
    expect(patients).toHaveLength(0); // Should not see tenant-1 data
  });
});
```

## Monitoring

### Performance Monitoring

- Monitor query performance across tenants
- Track service instance creation/reuse
- Monitor Firebase usage per tenant

### Security Monitoring

- Audit tenant access patterns
- Monitor failed security rule evaluations
- Track cross-tenant access attempts

## Troubleshooting

### Common Issues

1. **"No tenant selected" errors**: Ensure TenantProvider wraps your app
2. **Permission denied**: Check security rules and user tenant assignment
3. **Data not found**: Verify correct tenant context is active

### Debug Tools

```typescript
// Enable debug logging
console.log('Current tenant:', tenantId);
console.log('Service registry:', TenantServiceRegistry);
```

## Deployment

### Firestore Rules Deployment

```bash
firebase deploy --only firestore:rules
```

### Environment Variables

```env
# Development
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id

# Production
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-prod-project-id
```

## Performance Considerations

1. **Service Instance Reuse**: The service registry caches instances per tenant
2. **Query Optimization**: Tenant-specific queries are automatically scoped
3. **Index Management**: Create composite indexes for tenant + field queries

## Security Considerations

1. **Complete Isolation**: No data leakage between tenants
2. **User Verification**: Security rules verify user belongs to tenant
3. **Admin Controls**: Separate admin permissions per tenant

This multi-tenant implementation provides complete data isolation while maintaining performance and scalability for your dental care SaaS platform.
