(()=>{var e={};e.id=64,e.ids=[64],e.modules={5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},2361:e=>{"use strict";e.exports=require("events")},7147:e=>{"use strict";e.exports=require("fs")},3685:e=>{"use strict";e.exports=require("http")},5158:e=>{"use strict";e.exports=require("http2")},1808:e=>{"use strict";e.exports=require("net")},2037:e=>{"use strict";e.exports=require("os")},1017:e=>{"use strict";e.exports=require("path")},7282:e=>{"use strict";e.exports=require("process")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},3837:e=>{"use strict";e.exports=require("util")},9796:e=>{"use strict";e.exports=require("zlib")},5086:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>u,tree:()=>d});var a=s(7096),r=s(6132),n=s(7284),i=s.n(n),l=s(2564),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);s.d(t,c);let d=["",{children:["appointments",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,9181)),"D:\\dentalcare.id\\src\\app\\appointments\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,2594)),"D:\\dentalcare.id\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9291,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\dentalcare.id\\src\\app\\appointments\\page.tsx"],m="/appointments/page",x={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/appointments/page",pathname:"/appointments",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8619:(e,t,s)=>{Promise.resolve().then(s.bind(s,8881))},8881:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>AppointmentsPage});var a=s(784),r=s(9885),n=s(9212),i=s(3416),l=s(9317),c=s(7537),d=s(1790),o=s(8750);function AppointmentCalendar({onSelectAppointment:e}){let[t,s]=(0,r.useState)(new Date),[n,m]=(0,r.useState)("day"),getStatusColor=e=>{switch(e){case"scheduled":default:return"bg-blue-100 border-blue-300 text-blue-800";case"confirmed":return"bg-green-100 border-green-300 text-green-800";case"in-progress":return"bg-yellow-100 border-yellow-300 text-yellow-800";case"completed":return"bg-gray-100 border-gray-300 text-gray-800";case"cancelled":return"bg-red-100 border-red-300 text-red-800"}},getStatusText=e=>{switch(e){case"scheduled":return"Terjadwal";case"confirmed":return"Dikonfirmasi";case"in-progress":return"Berlangsung";case"completed":return"Selesai";case"cancelled":return"Dibatalkan";default:return e}},navigateDate=e=>{let a=new Date(t);"day"===n?a.setDate(a.getDate()+("next"===e?1:-1)):"week"===n?a.setDate(a.getDate()+("next"===e?7:-7)):a.setMonth(a.getMonth()+("next"===e?1:-1)),s(a)},x=i.Q7.filter(e=>e.date===t.toISOString().split("T")[0]),u=["08:00","08:30","09:00","09:30","10:00","10:30","11:00","11:30","13:00","13:30","14:00","14:30","15:00","15:30","16:00","16:30","17:00"];return(0,a.jsxs)("div",{className:"card",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Jadwal Appointment"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("button",{onClick:()=>navigateDate("prev"),className:"p-2 hover:bg-gray-100 rounded-lg",children:a.jsx(l.Z,{className:"w-5 h-5"})}),a.jsx("span",{className:"text-lg font-medium min-w-[200px] text-center",children:t.toLocaleDateString("id-ID",{weekday:"long",year:"numeric",month:"long",day:"numeric"})}),a.jsx("button",{onClick:()=>navigateDate("next"),className:"p-2 hover:bg-gray-100 rounded-lg",children:a.jsx(c.Z,{className:"w-5 h-5"})})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("div",{className:"flex bg-gray-100 rounded-lg p-1",children:["day","week","month"].map(e=>a.jsx("button",{onClick:()=>m(e),className:`px-3 py-1 rounded-md text-sm font-medium transition-colors ${n===e?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:"day"===e?"Hari":"week"===e?"Minggu":"Bulan"},e))}),a.jsx("button",{className:"btn-primary",children:"+ Buat Appointment"})]})]}),"day"===n&&(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[a.jsx("div",{className:"col-span-2",children:a.jsx("div",{className:"space-y-4",children:u.map(e=>a.jsx("div",{className:"h-16 flex items-center justify-end pr-4 text-sm text-gray-500",children:e},e))})}),a.jsx("div",{className:"col-span-10",children:a.jsx("div",{className:"space-y-4",children:u.map(t=>{let s=x.find(e=>e.time===t);return a.jsx("div",{className:"h-16 border-l border-gray-200 pl-4 relative",children:s&&a.jsx("div",{onClick:()=>e(s),className:`absolute inset-0 ml-4 p-3 rounded-lg border-l-4 cursor-pointer hover:shadow-md transition-shadow ${getStatusColor(s.status)}`,children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium text-sm",children:s.patientName}),a.jsx("p",{className:"text-xs opacity-75",children:s.type})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("p",{className:"text-xs opacity-75",children:["Dr. ",s.doctorName]}),(0,a.jsxs)("div",{className:"flex items-center text-xs opacity-75",children:[a.jsx(d.Z,{className:"w-3 h-3 mr-1"}),s.duration,"m"]})]})]})})},t)})})})]}),("week"===n||"month"===n)&&a.jsx("div",{className:"space-y-4",children:x.length>0?x.map(t=>(0,a.jsxs)("div",{onClick:()=>e(t),className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx("div",{className:"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center",children:a.jsx(o.Z,{className:"w-5 h-5 text-primary-600"})})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium text-gray-900",children:t.patientName}),a.jsx("p",{className:"text-sm text-gray-600",children:t.type}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Dr. ",t.doctorName]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600 mb-2",children:[a.jsx(d.Z,{className:"w-4 h-4 mr-1"}),t.time," (",t.duration," menit)"]}),a.jsx("span",{className:`status-badge ${getStatusColor(t.status).replace("bg-","status-").replace(" border-"," ").replace(" text-"," ")}`,children:getStatusText(t.status)})]})]},t.id)):a.jsx("div",{className:"text-center py-8 text-gray-500",children:"Tidak ada appointment pada tanggal ini"})})]})}function AppointmentsPage(){let[e,t]=(0,r.useState)(null);return(0,a.jsxs)("div",{className:"flex-1 overflow-auto",children:[a.jsx(n.Z,{title:"Manajemen Jadwal",subtitle:"Kelola appointment dan jadwal dokter"}),(0,a.jsxs)("main",{className:"p-6",children:[a.jsx(AppointmentCalendar,{onSelectAppointment:t}),e&&a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-2xl w-full p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900",children:"Detail Appointment"}),a.jsx("button",{onClick:()=>t(null),className:"text-gray-400 hover:text-gray-600",children:"✕"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Pasien"}),a.jsx("p",{className:"text-lg font-medium",children:e.patientName})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Dokter"}),(0,a.jsxs)("p",{className:"text-lg font-medium",children:["Dr. ",e.doctorName]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Tanggal & Waktu"}),(0,a.jsxs)("p",{className:"text-lg font-medium",children:[new Date(e.date).toLocaleDateString("id-ID")," - ",e.time]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Durasi"}),(0,a.jsxs)("p",{className:"text-lg font-medium",children:[e.duration," menit"]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Jenis Treatment"}),a.jsx("p",{className:"text-lg font-medium",children:e.type})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Status"}),a.jsx("span",{className:`status-badge ${"scheduled"===e.status?"status-scheduled":"confirmed"===e.status?"status-confirmed":"in-progress"===e.status?"status-in-progress":"completed"===e.status?"status-completed":"status-cancelled"}`,children:"scheduled"===e.status?"Terjadwal":"confirmed"===e.status?"Dikonfirmasi":"in-progress"===e.status?"Berlangsung":"completed"===e.status?"Selesai":"Dibatalkan"})]})]}),e.notes&&(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Catatan"}),a.jsx("p",{className:"text-gray-900 bg-gray-50 p-3 rounded-lg",children:e.notes})]}),e.treatmentPlan&&e.treatmentPlan.length>0&&(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Rencana Treatment"}),a.jsx("div",{className:"flex flex-wrap gap-2 mt-2",children:e.treatmentPlan.map((e,t)=>a.jsx("span",{className:"bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm",children:e},t))})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[a.jsx("button",{className:"btn-secondary",children:"Edit"}),a.jsx("button",{className:"btn-danger",children:"Batalkan"}),a.jsx("button",{className:"btn-primary",children:"Mulai Treatment"})]})]})})]})]})}},9181:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>n,default:()=>c});var a=s(5153);let r=(0,a.createProxy)(String.raw`D:\dentalcare.id\src\app\appointments\page.tsx`),{__esModule:n,$$typeof:i}=r,l=r.default,c=l}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[172,684,138,29,212,416],()=>__webpack_exec__(5086));module.exports=s})();