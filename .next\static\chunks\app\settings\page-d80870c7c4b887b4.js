(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[938],{9631:function(e,t,a){Promise.resolve().then(a.bind(a,6034))},6034:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return SettingsPage}});var s=a(7437),r=a(2621),l=a(9267),n=a(9649),i=a(7965),c=a(2265);function CreditCardIcon({title:e,titleId:t,...a},s){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?c.createElement("title",{id:t},e):null,c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"}))}let d=c.forwardRef(CreditCardIcon);function ShieldCheckIcon({title:e,titleId:t,...a},s){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?c.createElement("title",{id:t},e):null,c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))}let o=c.forwardRef(ShieldCheckIcon);function GlobeAltIcon({title:e,titleId:t,...a},s){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?c.createElement("title",{id:t},e):null,c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418"}))}let m=c.forwardRef(GlobeAltIcon);function SettingsPage(){return(0,s.jsxs)("div",{className:"flex-1 overflow-auto",children:[(0,s.jsx)(r.Z,{title:"Pengaturan",subtitle:"Konfigurasi sistem dan preferensi klinik"}),(0,s.jsx)("main",{className:"p-6",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,s.jsxs)("div",{className:"card",children:[(0,s.jsxs)("div",{className:"flex items-center mb-6",children:[(0,s.jsx)(l.Z,{className:"w-6 h-6 text-primary-600 mr-3"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Informasi Klinik"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Nama Klinik"}),(0,s.jsx)("input",{type:"text",className:"input-field",defaultValue:"Klinik Gigi DentalCare"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Nomor Telepon"}),(0,s.jsx)("input",{type:"text",className:"input-field",defaultValue:"(021) 1234-5678"})]}),(0,s.jsxs)("div",{className:"md:col-span-2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Alamat"}),(0,s.jsx)("textarea",{className:"input-field",rows:3,defaultValue:"Jl. Sudirman No. 123, Jakarta Pusat 10110"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),(0,s.jsx)("input",{type:"email",className:"input-field",defaultValue:"<EMAIL>"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Website"}),(0,s.jsx)("input",{type:"url",className:"input-field",defaultValue:"https://dentalcare.id"})]})]}),(0,s.jsx)("div",{className:"mt-6",children:(0,s.jsx)("button",{className:"btn-primary",children:"Simpan Perubahan"})})]}),(0,s.jsxs)("div",{className:"card",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(n.Z,{className:"w-6 h-6 text-primary-600 mr-3"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Manajemen Pengguna"})]}),(0,s.jsx)("button",{className:"btn-primary",children:"+ Tambah Pengguna"})]}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Nama"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Email"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Role"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Status"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Aksi"})]})}),(0,s.jsxs)("tbody",{className:"bg-white divide-y divide-gray-200",children:[(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:"Dr. Sarah Putri"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"<EMAIL>"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"status-badge bg-purple-100 text-purple-800",children:"Dokter"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"status-badge status-active",children:"Aktif"})}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,s.jsx)("button",{className:"text-primary-600 hover:text-primary-900 mr-3",children:"Edit"}),(0,s.jsx)("button",{className:"text-red-600 hover:text-red-900",children:"Hapus"})]})]}),(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:"Siti Nurhaliza"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"<EMAIL>"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"status-badge bg-blue-100 text-blue-800",children:"Resepsionis"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"status-badge status-active",children:"Aktif"})}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,s.jsx)("button",{className:"text-primary-600 hover:text-primary-900 mr-3",children:"Edit"}),(0,s.jsx)("button",{className:"text-red-600 hover:text-red-900",children:"Hapus"})]})]})]})]})})]}),(0,s.jsxs)("div",{className:"card",children:[(0,s.jsxs)("div",{className:"flex items-center mb-6",children:[(0,s.jsx)(i.Z,{className:"w-6 h-6 text-primary-600 mr-3"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Pengaturan Notifikasi"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-gray-900",children:"Reminder Appointment"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Kirim reminder H-1 dan H-0 ke pasien"})]}),(0,s.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,s.jsx)("input",{type:"checkbox",className:"sr-only peer",defaultChecked:!0}),(0,s.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-gray-900",children:"Alert Stok Menipis"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Notifikasi ketika stok inventory menipis"})]}),(0,s.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,s.jsx)("input",{type:"checkbox",className:"sr-only peer",defaultChecked:!0}),(0,s.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-gray-900",children:"Laporan Harian"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Email laporan harian ke manajemen"})]}),(0,s.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,s.jsx)("input",{type:"checkbox",className:"sr-only peer"}),(0,s.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"})]})]})]})]}),(0,s.jsxs)("div",{className:"card",children:[(0,s.jsxs)("div",{className:"flex items-center mb-6",children:[(0,s.jsx)(d,{className:"w-6 h-6 text-primary-600 mr-3"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Pengaturan Pembayaran"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Metode Pembayaran Aktif"}),(0,s.jsx)("div",{className:"space-y-2",children:["Tunai","Kartu Debit/Kredit","Transfer Bank","QRIS","E-Wallet"].map(e=>(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",className:"rounded border-gray-300 text-primary-600 mr-2",defaultChecked:!0}),(0,s.jsx)("span",{className:"text-sm text-gray-700",children:e})]},e))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Pajak (%)"}),(0,s.jsx)("input",{type:"number",className:"input-field",defaultValue:"11",min:"0",max:"100"}),(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2 mt-4",children:"Diskon Maksimal (%)"}),(0,s.jsx)("input",{type:"number",className:"input-field",defaultValue:"20",min:"0",max:"100"})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"card",children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)(o,{className:"w-6 h-6 text-primary-600 mr-3"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Keamanan"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("button",{className:"w-full btn-secondary text-left",children:"Ubah Password"}),(0,s.jsx)("button",{className:"w-full btn-secondary text-left",children:"Aktivasi 2FA"}),(0,s.jsx)("button",{className:"w-full btn-secondary text-left",children:"Log Aktivitas"}),(0,s.jsx)("button",{className:"w-full btn-secondary text-left",children:"Backup Data"})]})]}),(0,s.jsxs)("div",{className:"card",children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)(m,{className:"w-6 h-6 text-primary-600 mr-3"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Preferensi"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Bahasa"}),(0,s.jsxs)("select",{className:"input-field",children:[(0,s.jsx)("option",{value:"id",children:"Bahasa Indonesia"}),(0,s.jsx)("option",{value:"en",children:"English"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Zona Waktu"}),(0,s.jsxs)("select",{className:"input-field",children:[(0,s.jsx)("option",{value:"Asia/Jakarta",children:"WIB (Jakarta)"}),(0,s.jsx)("option",{value:"Asia/Makassar",children:"WITA (Makassar)"}),(0,s.jsx)("option",{value:"Asia/Jayapura",children:"WIT (Jayapura)"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Format Tanggal"}),(0,s.jsxs)("select",{className:"input-field",children:[(0,s.jsx)("option",{value:"dd/mm/yyyy",children:"DD/MM/YYYY"}),(0,s.jsx)("option",{value:"mm/dd/yyyy",children:"MM/DD/YYYY"}),(0,s.jsx)("option",{value:"yyyy-mm-dd",children:"YYYY-MM-DD"})]})]})]})]})]})]})})]})}},7965:function(e,t,a){"use strict";var s=a(2265);function BellIcon({title:e,titleId:t,...a},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},a),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}))}let r=s.forwardRef(BellIcon);t.Z=r},9267:function(e,t,a){"use strict";var s=a(2265);function BuildingOfficeIcon({title:e,titleId:t,...a},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},a),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h1.5m-1.5 3h1.5m-1.5 3h1.5m3-6H15m-1.5 3H15m-1.5 3H15M9 21v-3.375c0-.621.504-1.125 1.125-1.125h3.75c.621 0 1.125.504 1.125 1.125V21"}))}let r=s.forwardRef(BuildingOfficeIcon);t.Z=r},7805:function(e,t,a){"use strict";var s=a(2265);function CheckIcon({title:e,titleId:t,...a},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},a),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))}let r=s.forwardRef(CheckIcon);t.Z=r},4164:function(e,t,a){"use strict";var s=a(2265);function ChevronDownIcon({title:e,titleId:t,...a},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},a),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m19.5 8.25-7.5 7.5-7.5-7.5"}))}let r=s.forwardRef(ChevronDownIcon);t.Z=r},4020:function(e,t,a){"use strict";var s=a(2265);function MagnifyingGlassIcon({title:e,titleId:t,...a},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},a),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))}let r=s.forwardRef(MagnifyingGlassIcon);t.Z=r},5255:function(e,t,a){"use strict";var s=a(2265);function PlusIcon({title:e,titleId:t,...a},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},a),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))}let r=s.forwardRef(PlusIcon);t.Z=r},9649:function(e,t,a){"use strict";var s=a(2265);function UserGroupIcon({title:e,titleId:t,...a},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},a),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"}))}let r=s.forwardRef(UserGroupIcon);t.Z=r}},function(e){e.O(0,[609,15,801,621,971,472,744],function(){return e(e.s=9631)}),_N_E=e.O()}]);