import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { InventoryService } from '@/services/inventory';
import { TenantServiceRegistry } from '@/services/base/TenantService';
import { useTenant } from '@/contexts/TenantContext';
import { InventoryItem } from '@/types';

/**
 * Get all inventory items for current tenant
 */
export function useInventoryItems() {
  const { tenantId } = useTenant();
  
  return useQuery({
    queryKey: ['inventory', tenantId],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const inventoryService = TenantServiceRegistry.getService(InventoryService, tenantId, 'inventory');
      return inventoryService.getInventoryItems();
    },
    enabled: !!tenantId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Get single inventory item by ID
 */
export function useInventoryItem(id: string) {
  const { tenantId } = useTenant();
  
  return useQuery({
    queryKey: ['inventory-item', tenantId, id],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const inventoryService = TenantServiceRegistry.getService(InventoryService, tenantId, 'inventory');
      return inventoryService.getInventoryItem(id);
    },
    enabled: !!id && !!tenantId,
  });
}

/**
 * Get inventory items by category
 */
export function useInventoryItemsByCategory(category: string) {
  const { tenantId } = useTenant();
  
  return useQuery({
    queryKey: ['inventory', 'category', tenantId, category],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const inventoryService = TenantServiceRegistry.getService(InventoryService, tenantId, 'inventory');
      return inventoryService.getInventoryItemsByCategory(category);
    },
    enabled: !!tenantId && !!category,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Get low stock items
 */
export function useLowStockItems() {
  const { tenantId } = useTenant();
  
  return useQuery({
    queryKey: ['inventory', 'low-stock', tenantId],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const inventoryService = TenantServiceRegistry.getService(InventoryService, tenantId, 'inventory');
      return inventoryService.getLowStockItems();
    },
    enabled: !!tenantId,
    staleTime: 2 * 60 * 1000, // 2 minutes - more frequent updates for critical data
  });
}

/**
 * Get expired items
 */
export function useExpiredItems() {
  const { tenantId } = useTenant();
  
  return useQuery({
    queryKey: ['inventory', 'expired', tenantId],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const inventoryService = TenantServiceRegistry.getService(InventoryService, tenantId, 'inventory');
      return inventoryService.getExpiredItems();
    },
    enabled: !!tenantId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Search inventory items
 */
export function useSearchInventoryItems(searchTerm: string) {
  const { tenantId } = useTenant();
  
  return useQuery({
    queryKey: ['inventory', 'search', tenantId, searchTerm],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const inventoryService = TenantServiceRegistry.getService(InventoryService, tenantId, 'inventory');
      return inventoryService.searchInventoryItems(searchTerm);
    },
    enabled: !!tenantId && searchTerm.length >= 2,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

/**
 * Get inventory categories
 */
export function useInventoryCategories() {
  const { tenantId } = useTenant();
  
  return useQuery({
    queryKey: ['inventory-categories', tenantId],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const inventoryService = TenantServiceRegistry.getService(InventoryService, tenantId, 'inventory');
      return inventoryService.getInventoryCategories();
    },
    enabled: !!tenantId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Create new inventory item
 */
export function useCreateInventoryItem() {
  const queryClient = useQueryClient();
  const { tenantId } = useTenant();

  return useMutation({
    mutationFn: (itemData: Omit<InventoryItem, 'id'>) => {
      if (!tenantId) throw new Error('No tenant selected');
      const inventoryService = TenantServiceRegistry.getService(InventoryService, tenantId, 'inventory');
      return inventoryService.createInventoryItem(itemData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['inventory', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['inventory-categories', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['inventory', 'low-stock', tenantId] });
    },
    onError: (error) => {
      console.error('Error creating inventory item:', error);
    }
  });
}

/**
 * Update inventory item
 */
export function useUpdateInventoryItem() {
  const queryClient = useQueryClient();
  const { tenantId } = useTenant();

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<InventoryItem> }) => {
      if (!tenantId) throw new Error('No tenant selected');
      const inventoryService = TenantServiceRegistry.getService(InventoryService, tenantId, 'inventory');
      return inventoryService.updateInventoryItem(id, updates);
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['inventory', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['inventory-item', tenantId, id] });
      queryClient.invalidateQueries({ queryKey: ['inventory', 'low-stock', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['inventory', 'expired', tenantId] });
    },
    onError: (error) => {
      console.error('Error updating inventory item:', error);
    }
  });
}

/**
 * Delete inventory item
 */
export function useDeleteInventoryItem() {
  const queryClient = useQueryClient();
  const { tenantId } = useTenant();

  return useMutation({
    mutationFn: (id: string) => {
      if (!tenantId) throw new Error('No tenant selected');
      const inventoryService = TenantServiceRegistry.getService(InventoryService, tenantId, 'inventory');
      return inventoryService.deleteInventoryItem(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['inventory', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['inventory-categories', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['inventory', 'low-stock', tenantId] });
    },
    onError: (error) => {
      console.error('Error deleting inventory item:', error);
    }
  });
}

/**
 * Restock inventory item
 */
export function useRestockItem() {
  const queryClient = useQueryClient();
  const { tenantId } = useTenant();

  return useMutation({
    mutationFn: ({ id, quantity, newExpiryDate }: { id: string; quantity: number; newExpiryDate?: string }) => {
      if (!tenantId) throw new Error('No tenant selected');
      const inventoryService = TenantServiceRegistry.getService(InventoryService, tenantId, 'inventory');
      return inventoryService.restockItem(id, quantity, newExpiryDate);
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['inventory', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['inventory-item', tenantId, id] });
      queryClient.invalidateQueries({ queryKey: ['inventory', 'low-stock', tenantId] });
    },
    onError: (error) => {
      console.error('Error restocking item:', error);
    }
  });
}

/**
 * Use inventory item (reduce stock)
 */
export function useUseItem() {
  const queryClient = useQueryClient();
  const { tenantId } = useTenant();

  return useMutation({
    mutationFn: ({ id, quantity }: { id: string; quantity: number }) => {
      if (!tenantId) throw new Error('No tenant selected');
      const inventoryService = TenantServiceRegistry.getService(InventoryService, tenantId, 'inventory');
      return inventoryService.useItem(id, quantity);
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['inventory', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['inventory-item', tenantId, id] });
      queryClient.invalidateQueries({ queryKey: ['inventory', 'low-stock', tenantId] });
    },
    onError: (error) => {
      console.error('Error using item:', error);
    }
  });
}

/**
 * Real-time inventory items subscription
 */
export function useRealTimeInventoryItems() {
  const { tenantId } = useTenant();
  const [items, setItems] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!tenantId) {
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    const inventoryService = TenantServiceRegistry.getService(InventoryService, tenantId, 'inventory');
    const unsubscribe = inventoryService.subscribeToInventoryItems(
      (updatedItems: InventoryItem[]) => {
        setItems(updatedItems);
        setLoading(false);
      }
    );

    return () => {
      unsubscribe();
    };
  }, [tenantId]);

  return { items, loading, error };
}

/**
 * Real-time low stock items subscription
 */
export function useRealTimeLowStockItems() {
  const { tenantId } = useTenant();
  const [items, setItems] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!tenantId) {
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    const inventoryService = TenantServiceRegistry.getService(InventoryService, tenantId, 'inventory');
    const unsubscribe = inventoryService.subscribeToLowStockItems(
      (updatedItems: InventoryItem[]) => {
        setItems(updatedItems);
        setLoading(false);
      }
    );

    return () => {
      unsubscribe();
    };
  }, [tenantId]);

  return { items, loading, error };
}
