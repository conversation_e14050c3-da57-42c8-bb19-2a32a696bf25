import { 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  onSnapshot
} from 'firebase/firestore';
import { InventoryItem } from '@/types';
import { TenantService } from './base/TenantService';

export class InventoryService extends TenantService {

  /**
   * Create new inventory item
   */
  async createInventoryItem(itemData: Omit<InventoryItem, 'id'>): Promise<string> {
    try {
      this.validateTenantAccess();
      
      const newItem: Omit<InventoryItem, 'id'> = {
        ...itemData,
        status: this.calculateStatus(itemData.currentStock, itemData.minStock, itemData.expiryDate),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      const docRef = await addDoc(this.getCollection('inventory'), newItem);
      return docRef.id;
    } catch (error) {
      this.handleError(error, 'create inventory item');
    }
  }

  /**
   * Get inventory item by ID
   */
  async getInventoryItem(id: string): Promise<InventoryItem | null> {
    try {
      this.validateTenantAccess();
      const docRef = this.getDocument('inventory', id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as InventoryItem;
      }
      return null;
    } catch (error) {
      this.handleError(error, 'get inventory item');
    }
  }

  /**
   * Get all inventory items for current tenant
   */
  async getInventoryItems(): Promise<InventoryItem[]> {
    try {
      this.validateTenantAccess();
      const q = query(
        this.getCollection('inventory'),
        orderBy('name')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as InventoryItem[];
    } catch (error) {
      this.handleError(error, 'get inventory items');
    }
  }

  /**
   * Get inventory items by category
   */
  async getInventoryItemsByCategory(category: string): Promise<InventoryItem[]> {
    try {
      this.validateTenantAccess();
      const q = query(
        this.getCollection('inventory'),
        where('category', '==', category),
        orderBy('name')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as InventoryItem[];
    } catch (error) {
      this.handleError(error, 'get inventory items by category');
    }
  }

  /**
   * Get low stock items
   */
  async getLowStockItems(): Promise<InventoryItem[]> {
    try {
      this.validateTenantAccess();
      const items = await this.getInventoryItems();
      
      return items.filter(item => 
        item.status === 'low-stock' || item.status === 'out-of-stock'
      );
    } catch (error) {
      this.handleError(error, 'get low stock items');
    }
  }

  /**
   * Get expired items
   */
  async getExpiredItems(): Promise<InventoryItem[]> {
    try {
      this.validateTenantAccess();
      const items = await this.getInventoryItems();
      
      return items.filter(item => item.status === 'expired');
    } catch (error) {
      this.handleError(error, 'get expired items');
    }
  }

  /**
   * Search inventory items
   */
  async searchInventoryItems(searchTerm: string): Promise<InventoryItem[]> {
    try {
      this.validateTenantAccess();
      const items = await this.getInventoryItems();
      
      const lowercaseSearch = searchTerm.toLowerCase();
      return items.filter((item: InventoryItem) => 
        item.name.toLowerCase().includes(lowercaseSearch) ||
        item.category.toLowerCase().includes(lowercaseSearch) ||
        item.supplier.toLowerCase().includes(lowercaseSearch)
      );
    } catch (error) {
      this.handleError(error, 'search inventory items');
    }
  }

  /**
   * Update inventory item
   */
  async updateInventoryItem(id: string, updates: Partial<InventoryItem>): Promise<void> {
    try {
      this.validateTenantAccess();
      
      // Recalculate status if stock or expiry date changed
      let updatedData = { ...updates };
      if (updates.currentStock !== undefined || updates.minStock !== undefined || updates.expiryDate !== undefined) {
        const currentItem = await this.getInventoryItem(id);
        if (currentItem) {
          const currentStock = updates.currentStock ?? currentItem.currentStock;
          const minStock = updates.minStock ?? currentItem.minStock;
          const expiryDate = updates.expiryDate ?? currentItem.expiryDate;
          
          updatedData.status = this.calculateStatus(currentStock, minStock, expiryDate);
        }
      }
      
      const docRef = this.getDocument('inventory', id);
      await updateDoc(docRef, {
        ...updatedData,
        updatedAt: new Date().toISOString()
      });
    } catch (error) {
      this.handleError(error, 'update inventory item');
    }
  }

  /**
   * Delete inventory item
   */
  async deleteInventoryItem(id: string): Promise<void> {
    try {
      this.validateTenantAccess();
      const docRef = this.getDocument('inventory', id);
      await deleteDoc(docRef);
    } catch (error) {
      this.handleError(error, 'delete inventory item');
    }
  }

  /**
   * Restock inventory item
   */
  async restockItem(id: string, quantity: number, newExpiryDate?: string): Promise<void> {
    try {
      this.validateTenantAccess();
      const currentItem = await this.getInventoryItem(id);
      
      if (!currentItem) {
        throw new Error('Inventory item not found');
      }
      
      const newStock = currentItem.currentStock + quantity;
      const updates: Partial<InventoryItem> = {
        currentStock: newStock,
        lastRestocked: new Date().toISOString(),
        status: this.calculateStatus(newStock, currentItem.minStock, newExpiryDate || currentItem.expiryDate)
      };
      
      if (newExpiryDate) {
        updates.expiryDate = newExpiryDate;
      }
      
      await this.updateInventoryItem(id, updates);
    } catch (error) {
      this.handleError(error, 'restock item');
    }
  }

  /**
   * Use inventory item (reduce stock)
   */
  async useItem(id: string, quantity: number): Promise<void> {
    try {
      this.validateTenantAccess();
      const currentItem = await this.getInventoryItem(id);
      
      if (!currentItem) {
        throw new Error('Inventory item not found');
      }
      
      if (currentItem.currentStock < quantity) {
        throw new Error('Insufficient stock');
      }
      
      const newStock = currentItem.currentStock - quantity;
      const updates: Partial<InventoryItem> = {
        currentStock: newStock,
        status: this.calculateStatus(newStock, currentItem.minStock, currentItem.expiryDate)
      };
      
      await this.updateInventoryItem(id, updates);
    } catch (error) {
      this.handleError(error, 'use item');
    }
  }

  /**
   * Get inventory categories
   */
  async getInventoryCategories(): Promise<string[]> {
    try {
      this.validateTenantAccess();
      const items = await this.getInventoryItems();
      const categories = new Set(items.map(item => item.category));
      return Array.from(categories).sort();
    } catch (error) {
      this.handleError(error, 'get inventory categories');
    }
  }

  /**
   * Calculate item status based on stock and expiry
   */
  private calculateStatus(currentStock: number, minStock: number, expiryDate?: string): InventoryItem['status'] {
    // Check if expired
    if (expiryDate) {
      const expiry = new Date(expiryDate);
      const now = new Date();
      if (expiry < now) {
        return 'expired';
      }
    }
    
    // Check stock levels
    if (currentStock === 0) {
      return 'out-of-stock';
    } else if (currentStock <= minStock) {
      return 'low-stock';
    } else {
      return 'in-stock';
    }
  }

  /**
   * Real-time subscription to inventory items
   */
  subscribeToInventoryItems(callback: (items: InventoryItem[]) => void) {
    this.validateTenantAccess();
    const q = query(
      this.getCollection('inventory'),
      orderBy('name')
    );

    return onSnapshot(q, (snapshot) => {
      const items = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as InventoryItem[];
      callback(items);
    }, (error) => {
      console.error('Error in inventory subscription:', error);
    });
  }

  /**
   * Subscribe to low stock items
   */
  subscribeToLowStockItems(callback: (items: InventoryItem[]) => void) {
    this.validateTenantAccess();
    const q = query(
      this.getCollection('inventory'),
      where('status', 'in', ['low-stock', 'out-of-stock']),
      orderBy('name')
    );

    return onSnapshot(q, (snapshot) => {
      const items = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as InventoryItem[];
      callback(items);
    }, (error) => {
      console.error('Error in low stock subscription:', error);
    });
  }
}
