"use strict";exports.id=713,exports.ids=[713],exports.modules={9503:(t,e,s)=>{s.d(e,{Z:()=>n});var r=s(9885);function ExclamationTriangleIcon({title:t,titleId:e,...s},i){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":e},s),t?r.createElement("title",{id:e},t):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))}let i=r.forwardRef(ExclamationTriangleIcon),n=i},4944:(t,e,s)=>{s.d(e,{D:()=>useMutation});var r=s(9885),i=s(5226),n=s(8602),u=s(6995),a=s(3224),o=class extends u.l{#t;#e=void 0;#s;#r;constructor(t,e){super(),this.#t=t,this.setOptions(e),this.bindMethods(),this.#i()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){let e=this.options;this.options=this.#t.defaultMutationOptions(t),(0,a.VS)(this.options,e)||this.#t.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#s,observer:this}),e?.mutationKey&&this.options.mutationKey&&(0,a.Ym)(e.mutationKey)!==(0,a.Ym)(this.options.mutationKey)?this.reset():this.#s?.state.status==="pending"&&this.#s.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#s?.removeObserver(this)}onMutationUpdate(t){this.#i(),this.#n(t)}getCurrentResult(){return this.#e}reset(){this.#s?.removeObserver(this),this.#s=void 0,this.#i(),this.#n()}mutate(t,e){return this.#r=e,this.#s?.removeObserver(this),this.#s=this.#t.getMutationCache().build(this.#t,this.options),this.#s.addObserver(this),this.#s.execute(t)}#i(){let t=this.#s?.state??(0,i.R)();this.#e={...t,isPending:"pending"===t.status,isSuccess:"success"===t.status,isError:"error"===t.status,isIdle:"idle"===t.status,mutate:this.mutate,reset:this.reset}}#n(t){n.Vr.batch(()=>{if(this.#r&&this.hasListeners()){let e=this.#e.variables,s=this.#e.context;t?.type==="success"?(this.#r.onSuccess?.(t.data,e,s),this.#r.onSettled?.(t.data,null,e,s)):t?.type==="error"&&(this.#r.onError?.(t.error,e,s),this.#r.onSettled?.(void 0,t.error,e,s))}this.listeners.forEach(t=>{t(this.#e)})})}},h=s(4070);function useMutation(t,e){let s=(0,h.NL)(e),[i]=r.useState(()=>new o(s,t));r.useEffect(()=>{i.setOptions(t)},[i,t]);let u=r.useSyncExternalStore(r.useCallback(t=>i.subscribe(n.Vr.batchCalls(t)),[i]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),c=r.useCallback((t,e)=>{i.mutate(t,e).catch(a.ZT)},[i]);if(u.error&&(0,a.L3)(i.options.throwOnError,[u.error]))throw u.error;return{...u,mutate:c,mutateAsync:u.mutate}}},1778:(t,e,s)=>{s.d(e,{a:()=>useQuery});var r=s(2596),i=s(8602),n=s(9422),u=s(6995),a=s(6106),o=s(3224),h=class extends u.l{constructor(t,e){super(),this.options=e,this.#t=t,this.#u=null,this.#a=(0,a.O)(),this.options.experimental_prefetchInRender||this.#a.reject(Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(e)}#t;#o=void 0;#h=void 0;#e=void 0;#c;#l;#a;#u;#d;#p;#f;#y;#R;#b;#m=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#o.addObserver(this),shouldFetchOnMount(this.#o,this.options)?this.#v():this.updateResult(),this.#O())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return shouldFetchOn(this.#o,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return shouldFetchOn(this.#o,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#g(),this.#Q(),this.#o.removeObserver(this)}setOptions(t){let e=this.options,s=this.#o;if(this.options=this.#t.defaultQueryOptions(t),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,o.Nc)(this.options.enabled,this.#o))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#C(),this.#o.setOptions(this.options),e._defaulted&&!(0,o.VS)(this.options,e)&&this.#t.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#o,observer:this});let r=this.hasListeners();r&&shouldFetchOptionally(this.#o,s,this.options,e)&&this.#v(),this.updateResult(),r&&(this.#o!==s||(0,o.Nc)(this.options.enabled,this.#o)!==(0,o.Nc)(e.enabled,this.#o)||(0,o.KC)(this.options.staleTime,this.#o)!==(0,o.KC)(e.staleTime,this.#o))&&this.#I();let i=this.#S();r&&(this.#o!==s||(0,o.Nc)(this.options.enabled,this.#o)!==(0,o.Nc)(e.enabled,this.#o)||i!==this.#b)&&this.#E(i)}getOptimisticResult(t){let e=this.#t.getQueryCache().build(this.#t,t),s=this.createResult(e,t);return shouldAssignObserverCurrentProperties(this,s)&&(this.#e=s,this.#l=this.options,this.#c=this.#o.state),s}getCurrentResult(){return this.#e}trackResult(t,e){return new Proxy(t,{get:(t,s)=>(this.trackProp(s),e?.(s),Reflect.get(t,s))})}trackProp(t){this.#m.add(t)}getCurrentQuery(){return this.#o}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){let e=this.#t.defaultQueryOptions(t),s=this.#t.getQueryCache().build(this.#t,e);return s.fetch().then(()=>this.createResult(s,e))}fetch(t){return this.#v({...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#e))}#v(t){this.#C();let e=this.#o.fetch(this.options,t);return t?.throwOnError||(e=e.catch(o.ZT)),e}#I(){this.#g();let t=(0,o.KC)(this.options.staleTime,this.#o);if(o.sk||this.#e.isStale||!(0,o.PN)(t))return;let e=(0,o.Kp)(this.#e.dataUpdatedAt,t);this.#y=setTimeout(()=>{this.#e.isStale||this.updateResult()},e+1)}#S(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#o):this.options.refetchInterval)??!1}#E(t){this.#Q(),this.#b=t,!o.sk&&!1!==(0,o.Nc)(this.options.enabled,this.#o)&&(0,o.PN)(this.#b)&&0!==this.#b&&(this.#R=setInterval(()=>{(this.options.refetchIntervalInBackground||r.j.isFocused())&&this.#v()},this.#b))}#O(){this.#I(),this.#E(this.#S())}#g(){this.#y&&(clearTimeout(this.#y),this.#y=void 0)}#Q(){this.#R&&(clearInterval(this.#R),this.#R=void 0)}createResult(t,e){let s;let r=this.#o,i=this.options,u=this.#e,h=this.#c,c=this.#l,l=t!==r,d=l?t.state:this.#h,{state:p}=t,f={...p},y=!1;if(e._optimisticResults){let s=this.hasListeners(),u=!s&&shouldFetchOnMount(t,e),a=s&&shouldFetchOptionally(t,r,e,i);(u||a)&&(f={...f,...(0,n.z)(p.data,t.options)}),"isRestoring"===e._optimisticResults&&(f.fetchStatus="idle")}let{error:R,errorUpdatedAt:b,status:m}=f;s=f.data;let v=!1;if(void 0!==e.placeholderData&&void 0===s&&"pending"===m){let t;u?.isPlaceholderData&&e.placeholderData===c?.placeholderData?(t=u.data,v=!0):t="function"==typeof e.placeholderData?e.placeholderData(this.#f?.state.data,this.#f):e.placeholderData,void 0!==t&&(m="success",s=(0,o.oE)(u?.data,t,e),y=!0)}if(e.select&&void 0!==s&&!v){if(u&&s===h?.data&&e.select===this.#d)s=this.#p;else try{this.#d=e.select,s=e.select(s),s=(0,o.oE)(u?.data,s,e),this.#p=s,this.#u=null}catch(t){this.#u=t}}this.#u&&(R=this.#u,s=this.#p,b=Date.now(),m="error");let O="fetching"===f.fetchStatus,g="pending"===m,Q="error"===m,C=g&&O,I=void 0!==s,S={status:m,fetchStatus:f.fetchStatus,isPending:g,isSuccess:"success"===m,isError:Q,isInitialLoading:C,isLoading:C,data:s,dataUpdatedAt:f.dataUpdatedAt,error:R,errorUpdatedAt:b,failureCount:f.fetchFailureCount,failureReason:f.fetchFailureReason,errorUpdateCount:f.errorUpdateCount,isFetched:f.dataUpdateCount>0||f.errorUpdateCount>0,isFetchedAfterMount:f.dataUpdateCount>d.dataUpdateCount||f.errorUpdateCount>d.errorUpdateCount,isFetching:O,isRefetching:O&&!g,isLoadingError:Q&&!I,isPaused:"paused"===f.fetchStatus,isPlaceholderData:y,isRefetchError:Q&&I,isStale:isStale(t,e),refetch:this.refetch,promise:this.#a,isEnabled:!1!==(0,o.Nc)(e.enabled,t)};if(this.options.experimental_prefetchInRender){let finalizeThenableIfPossible=t=>{"error"===S.status?t.reject(S.error):void 0!==S.data&&t.resolve(S.data)},recreateThenable=()=>{let t=this.#a=S.promise=(0,a.O)();finalizeThenableIfPossible(t)},e=this.#a;switch(e.status){case"pending":t.queryHash===r.queryHash&&finalizeThenableIfPossible(e);break;case"fulfilled":("error"===S.status||S.data!==e.value)&&recreateThenable();break;case"rejected":("error"!==S.status||S.error!==e.reason)&&recreateThenable()}}return S}updateResult(){let t=this.#e,e=this.createResult(this.#o,this.options);this.#c=this.#o.state,this.#l=this.options,void 0!==this.#c.data&&(this.#f=this.#o),(0,o.VS)(e,t)||(this.#e=e,this.#n({listeners:(()=>{if(!t)return!0;let{notifyOnChangeProps:e}=this.options,s="function"==typeof e?e():e;if("all"===s||!s&&!this.#m.size)return!0;let r=new Set(s??this.#m);return this.options.throwOnError&&r.add("error"),Object.keys(this.#e).some(e=>{let s=this.#e[e]!==t[e];return s&&r.has(e)})})()}))}#C(){let t=this.#t.getQueryCache().build(this.#t,this.options);if(t===this.#o)return;let e=this.#o;this.#o=t,this.#h=t.state,this.hasListeners()&&(e?.removeObserver(this),t.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#O()}#n(t){i.Vr.batch(()=>{t.listeners&&this.listeners.forEach(t=>{t(this.#e)}),this.#t.getQueryCache().notify({query:this.#o,type:"observerResultsUpdated"})})}};function shouldLoadOnMount(t,e){return!1!==(0,o.Nc)(e.enabled,t)&&void 0===t.state.data&&!("error"===t.state.status&&!1===e.retryOnMount)}function shouldFetchOnMount(t,e){return shouldLoadOnMount(t,e)||void 0!==t.state.data&&shouldFetchOn(t,e,e.refetchOnMount)}function shouldFetchOn(t,e,s){if(!1!==(0,o.Nc)(e.enabled,t)&&"static"!==(0,o.KC)(e.staleTime,t)){let r="function"==typeof s?s(t):s;return"always"===r||!1!==r&&isStale(t,e)}return!1}function shouldFetchOptionally(t,e,s,r){return(t!==e||!1===(0,o.Nc)(r.enabled,t))&&(!s.suspense||"error"!==t.state.status)&&isStale(t,s)}function isStale(t,e){return!1!==(0,o.Nc)(e.enabled,t)&&t.isStaleByTime((0,o.KC)(e.staleTime,t))}function shouldAssignObserverCurrentProperties(t,e){return!(0,o.VS)(t.getCurrentResult(),e)}var c=s(9885),l=s(4070);function createValue(){let t=!1;return{clearReset:()=>{t=!1},reset:()=>{t=!0},isReset:()=>t}}s(784);var d=c.createContext(createValue()),useQueryErrorResetBoundary=()=>c.useContext(d),ensurePreventErrorBoundaryRetry=(t,e)=>{(t.suspense||t.throwOnError||t.experimental_prefetchInRender)&&!e.isReset()&&(t.retryOnMount=!1)},useClearResetErrorBoundary=t=>{c.useEffect(()=>{t.clearReset()},[t])},getHasError=({result:t,errorResetBoundary:e,throwOnError:s,query:r,suspense:i})=>t.isError&&!e.isReset()&&!t.isFetching&&r&&(i&&void 0===t.data||(0,o.L3)(s,[t.error,r])),p=c.createContext(!1),useIsRestoring=()=>c.useContext(p);p.Provider;var ensureSuspenseTimers=t=>{if(t.suspense){let clamp=t=>"static"===t?t:Math.max(t??1e3,1e3),e=t.staleTime;t.staleTime="function"==typeof e?(...t)=>clamp(e(...t)):clamp(e),"number"==typeof t.gcTime&&(t.gcTime=Math.max(t.gcTime,1e3))}},willFetch=(t,e)=>t.isLoading&&t.isFetching&&!e,shouldSuspend=(t,e)=>t?.suspense&&e.isPending,fetchOptimistic=(t,e,s)=>e.fetchOptimistic(t).catch(()=>{s.clearReset()});function useBaseQuery(t,e,s){let r=useIsRestoring(),n=useQueryErrorResetBoundary(),u=(0,l.NL)(s),a=u.defaultQueryOptions(t);u.getDefaultOptions().queries?._experimental_beforeQuery?.(a),a._optimisticResults=r?"isRestoring":"optimistic",ensureSuspenseTimers(a),ensurePreventErrorBoundaryRetry(a,n),useClearResetErrorBoundary(n);let h=!u.getQueryCache().get(a.queryHash),[d]=c.useState(()=>new e(u,a)),p=d.getOptimisticResult(a),f=!r&&!1!==t.subscribed;if(c.useSyncExternalStore(c.useCallback(t=>{let e=f?d.subscribe(i.Vr.batchCalls(t)):o.ZT;return d.updateResult(),e},[d,f]),()=>d.getCurrentResult(),()=>d.getCurrentResult()),c.useEffect(()=>{d.setOptions(a)},[a,d]),shouldSuspend(a,p))throw fetchOptimistic(a,d,n);if(getHasError({result:p,errorResetBoundary:n,throwOnError:a.throwOnError,query:u.getQueryCache().get(a.queryHash),suspense:a.suspense}))throw p.error;if(u.getDefaultOptions().queries?._experimental_afterQuery?.(a,p),a.experimental_prefetchInRender&&!o.sk&&willFetch(p,r)){let t=h?fetchOptimistic(a,d,n):u.getQueryCache().get(a.queryHash)?.promise;t?.catch(o.ZT).finally(()=>{d.updateResult()})}return a.notifyOnChangeProps?p:d.trackResult(p)}function useQuery(t,e){return useBaseQuery(t,h,e)}}};