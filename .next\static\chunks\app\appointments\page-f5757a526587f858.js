(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[64],{8257:function(e,t,a){Promise.resolve().then(a.bind(a,553))},553:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return AppointmentsPage}});var n=a(7437),s=a(2265),r=a(2621),i=a(5658);function ChevronLeftIcon({title:e,titleId:t,...a},n){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},a),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 19.5 8.25 12l7.5-7.5"}))}let l=s.forwardRef(ChevronLeftIcon);function ChevronRightIcon({title:e,titleId:t,...a},n){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},a),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))}let c=s.forwardRef(ChevronRightIcon);var o=a(2929),d=a(1809);function AppointmentCalendar(e){let{onSelectAppointment:t}=e,[a,r]=(0,s.useState)(new Date),[m,u]=(0,s.useState)("day"),getStatusColor=e=>{switch(e){case"scheduled":default:return"bg-blue-100 border-blue-300 text-blue-800";case"confirmed":return"bg-green-100 border-green-300 text-green-800";case"in-progress":return"bg-yellow-100 border-yellow-300 text-yellow-800";case"completed":return"bg-gray-100 border-gray-300 text-gray-800";case"cancelled":return"bg-red-100 border-red-300 text-red-800"}},getStatusText=e=>{switch(e){case"scheduled":return"Terjadwal";case"confirmed":return"Dikonfirmasi";case"in-progress":return"Berlangsung";case"completed":return"Selesai";case"cancelled":return"Dibatalkan";default:return e}},navigateDate=e=>{let t=new Date(a);"day"===m?t.setDate(t.getDate()+("next"===e?1:-1)):"week"===m?t.setDate(t.getDate()+("next"===e?7:-7)):t.setMonth(t.getMonth()+("next"===e?1:-1)),r(t)},x=i.Q7.filter(e=>e.date===a.toISOString().split("T")[0]),g=["08:00","08:30","09:00","09:30","10:00","10:30","11:00","11:30","13:00","13:30","14:00","14:30","15:00","15:30","16:00","16:30","17:00"];return(0,n.jsxs)("div",{className:"card",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Jadwal Appointment"}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("button",{onClick:()=>navigateDate("prev"),className:"p-2 hover:bg-gray-100 rounded-lg",children:(0,n.jsx)(l,{className:"w-5 h-5"})}),(0,n.jsx)("span",{className:"text-lg font-medium min-w-[200px] text-center",children:a.toLocaleDateString("id-ID",{weekday:"long",year:"numeric",month:"long",day:"numeric"})}),(0,n.jsx)("button",{onClick:()=>navigateDate("next"),className:"p-2 hover:bg-gray-100 rounded-lg",children:(0,n.jsx)(c,{className:"w-5 h-5"})})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("div",{className:"flex bg-gray-100 rounded-lg p-1",children:["day","week","month"].map(e=>(0,n.jsx)("button",{onClick:()=>u(e),className:"px-3 py-1 rounded-md text-sm font-medium transition-colors ".concat(m===e?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"),children:"day"===e?"Hari":"week"===e?"Minggu":"Bulan"},e))}),(0,n.jsx)("button",{className:"btn-primary",children:"+ Buat Appointment"})]})]}),"day"===m&&(0,n.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[(0,n.jsx)("div",{className:"col-span-2",children:(0,n.jsx)("div",{className:"space-y-4",children:g.map(e=>(0,n.jsx)("div",{className:"h-16 flex items-center justify-end pr-4 text-sm text-gray-500",children:e},e))})}),(0,n.jsx)("div",{className:"col-span-10",children:(0,n.jsx)("div",{className:"space-y-4",children:g.map(e=>{let a=x.find(t=>t.time===e);return(0,n.jsx)("div",{className:"h-16 border-l border-gray-200 pl-4 relative",children:a&&(0,n.jsx)("div",{onClick:()=>t(a),className:"absolute inset-0 ml-4 p-3 rounded-lg border-l-4 cursor-pointer hover:shadow-md transition-shadow ".concat(getStatusColor(a.status)),children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"font-medium text-sm",children:a.patientName}),(0,n.jsx)("p",{className:"text-xs opacity-75",children:a.type})]}),(0,n.jsxs)("div",{className:"text-right",children:[(0,n.jsxs)("p",{className:"text-xs opacity-75",children:["Dr. ",a.doctorName]}),(0,n.jsxs)("div",{className:"flex items-center text-xs opacity-75",children:[(0,n.jsx)(o.Z,{className:"w-3 h-3 mr-1"}),a.duration,"m"]})]})]})})},e)})})})]}),("week"===m||"month"===m)&&(0,n.jsx)("div",{className:"space-y-4",children:x.length>0?x.map(e=>(0,n.jsxs)("div",{onClick:()=>t(e),className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)("div",{className:"flex-shrink-0",children:(0,n.jsx)("div",{className:"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center",children:(0,n.jsx)(d.Z,{className:"w-5 h-5 text-primary-600"})})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"font-medium text-gray-900",children:e.patientName}),(0,n.jsx)("p",{className:"text-sm text-gray-600",children:e.type}),(0,n.jsxs)("p",{className:"text-sm text-gray-500",children:["Dr. ",e.doctorName]})]})]}),(0,n.jsxs)("div",{className:"text-right",children:[(0,n.jsxs)("div",{className:"flex items-center text-sm text-gray-600 mb-2",children:[(0,n.jsx)(o.Z,{className:"w-4 h-4 mr-1"}),e.time," (",e.duration," menit)"]}),(0,n.jsx)("span",{className:"status-badge ".concat(getStatusColor(e.status).replace("bg-","status-").replace(" border-"," ").replace(" text-"," ")),children:getStatusText(e.status)})]})]},e.id)):(0,n.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Tidak ada appointment pada tanggal ini"})})]})}function AppointmentsPage(){let[e,t]=(0,s.useState)(null);return(0,n.jsxs)("div",{className:"flex-1 overflow-auto",children:[(0,n.jsx)(r.Z,{title:"Manajemen Jadwal",subtitle:"Kelola appointment dan jadwal dokter"}),(0,n.jsxs)("main",{className:"p-6",children:[(0,n.jsx)(AppointmentCalendar,{onSelectAppointment:t}),e&&(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg max-w-2xl w-full p-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Detail Appointment"}),(0,n.jsx)("button",{onClick:()=>t(null),className:"text-gray-400 hover:text-gray-600",children:"✕"})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Pasien"}),(0,n.jsx)("p",{className:"text-lg font-medium",children:e.patientName})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Dokter"}),(0,n.jsxs)("p",{className:"text-lg font-medium",children:["Dr. ",e.doctorName]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Tanggal & Waktu"}),(0,n.jsxs)("p",{className:"text-lg font-medium",children:[new Date(e.date).toLocaleDateString("id-ID")," - ",e.time]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Durasi"}),(0,n.jsxs)("p",{className:"text-lg font-medium",children:[e.duration," menit"]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Jenis Treatment"}),(0,n.jsx)("p",{className:"text-lg font-medium",children:e.type})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Status"}),(0,n.jsx)("span",{className:"status-badge ".concat("scheduled"===e.status?"status-scheduled":"confirmed"===e.status?"status-confirmed":"in-progress"===e.status?"status-in-progress":"completed"===e.status?"status-completed":"status-cancelled"),children:"scheduled"===e.status?"Terjadwal":"confirmed"===e.status?"Dikonfirmasi":"in-progress"===e.status?"Berlangsung":"completed"===e.status?"Selesai":"Dibatalkan"})]})]}),e.notes&&(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Catatan"}),(0,n.jsx)("p",{className:"text-gray-900 bg-gray-50 p-3 rounded-lg",children:e.notes})]}),e.treatmentPlan&&e.treatmentPlan.length>0&&(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Rencana Treatment"}),(0,n.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:e.treatmentPlan.map((e,t)=>(0,n.jsx)("span",{className:"bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm",children:e},t))})]})]}),(0,n.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,n.jsx)("button",{className:"btn-secondary",children:"Edit"}),(0,n.jsx)("button",{className:"btn-danger",children:"Batalkan"}),(0,n.jsx)("button",{className:"btn-primary",children:"Mulai Treatment"})]})]})})]})]})}},5658:function(e,t,a){"use strict";a.d(t,{Q7:function(){return n},pM:function(){return s},sP:function(){return r}});let n=[{id:"1",patientId:"1",patientName:"Budi Santoso",doctorId:"doc1",doctorName:"Dr. Sarah Putri",date:"2024-01-20",time:"09:00",duration:60,type:"Konsultasi",status:"scheduled",notes:"Keluhan sakit gigi geraham kiri",treatmentPlan:["Konsultasi","Scaling"]},{id:"2",patientId:"2",patientName:"Sari Dewi",doctorId:"doc1",doctorName:"Dr. Sarah Putri",date:"2024-01-20",time:"10:30",duration:90,type:"Scaling",status:"confirmed",treatmentPlan:["Scaling","Fluoride Treatment"]},{id:"3",patientId:"3",patientName:"Andi Wijaya",doctorId:"doc2",doctorName:"Dr. Ahmad Rahman",date:"2024-01-20",time:"14:00",duration:120,type:"Crown",status:"in-progress",treatmentPlan:["Crown Preparation","Temporary Crown"]}],s=[{id:"1",code:"CONS001",name:"Konsultasi",category:"Konsultasi",price:15e4,duration:30,description:"Konsultasi dan pemeriksaan gigi"},{id:"2",code:"SCAL001",name:"Scaling",category:"Preventif",price:3e5,duration:60,description:"Pembersihan karang gigi"},{id:"3",code:"FILL001",name:"Tambal Gigi",category:"Restoratif",price:25e4,duration:45,description:"Penambalan gigi dengan komposit"},{id:"4",code:"CROW001",name:"Crown",category:"Prostetik",price:25e5,duration:120,description:"Pemasangan mahkota gigi"}],r=[{id:"1",name:"Komposit Resin",category:"Bahan Tambal",currentStock:15,minStock:10,unit:"tube",price:45e4,supplier:"PT Dental Supply",expiryDate:"2025-06-15",lastRestocked:"2024-01-01",status:"in-stock"},{id:"2",name:"Anestesi Lidocaine",category:"Obat",currentStock:5,minStock:20,unit:"vial",price:25e3,supplier:"PT Pharma Dental",expiryDate:"2024-12-31",lastRestocked:"2023-12-15",status:"low-stock"},{id:"3",name:"Sarung Tangan Latex",category:"Disposable",currentStock:0,minStock:50,unit:"box",price:85e3,supplier:"PT Medical Supply",lastRestocked:"2023-11-20",status:"out-of-stock"}]},7965:function(e,t,a){"use strict";var n=a(2265);function BellIcon({title:e,titleId:t,...a},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}))}let s=n.forwardRef(BellIcon);t.Z=s},9267:function(e,t,a){"use strict";var n=a(2265);function BuildingOfficeIcon({title:e,titleId:t,...a},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h1.5m-1.5 3h1.5m-1.5 3h1.5m3-6H15m-1.5 3H15m-1.5 3H15M9 21v-3.375c0-.621.504-1.125 1.125-1.125h3.75c.621 0 1.125.504 1.125 1.125V21"}))}let s=n.forwardRef(BuildingOfficeIcon);t.Z=s},7805:function(e,t,a){"use strict";var n=a(2265);function CheckIcon({title:e,titleId:t,...a},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))}let s=n.forwardRef(CheckIcon);t.Z=s},4164:function(e,t,a){"use strict";var n=a(2265);function ChevronDownIcon({title:e,titleId:t,...a},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m19.5 8.25-7.5 7.5-7.5-7.5"}))}let s=n.forwardRef(ChevronDownIcon);t.Z=s},2929:function(e,t,a){"use strict";var n=a(2265);function ClockIcon({title:e,titleId:t,...a},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}let s=n.forwardRef(ClockIcon);t.Z=s},4020:function(e,t,a){"use strict";var n=a(2265);function MagnifyingGlassIcon({title:e,titleId:t,...a},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))}let s=n.forwardRef(MagnifyingGlassIcon);t.Z=s},5255:function(e,t,a){"use strict";var n=a(2265);function PlusIcon({title:e,titleId:t,...a},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))}let s=n.forwardRef(PlusIcon);t.Z=s},1809:function(e,t,a){"use strict";var n=a(2265);function UserIcon({title:e,titleId:t,...a},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))}let s=n.forwardRef(UserIcon);t.Z=s}},function(e){e.O(0,[609,15,801,621,971,472,744],function(){return e(e.s=8257)}),_N_E=e.O()}]);