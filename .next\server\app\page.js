(()=>{var e={};e.id=931,e.ids=[931],e.modules={5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},2361:e=>{"use strict";e.exports=require("events")},7147:e=>{"use strict";e.exports=require("fs")},3685:e=>{"use strict";e.exports=require("http")},5158:e=>{"use strict";e.exports=require("http2")},1808:e=>{"use strict";e.exports=require("net")},2037:e=>{"use strict";e.exports=require("os")},1017:e=>{"use strict";e.exports=require("path")},7282:e=>{"use strict";e.exports=require("process")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},3837:e=>{"use strict";e.exports=require("util")},9796:e=>{"use strict";e.exports=require("zlib")},8587:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>o,routeModule:()=>m,tree:()=>d});var a=s(7096),r=s(6132),i=s(7284),n=s.n(i),l=s(2564),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);s.d(t,c);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,8203)),"D:\\dentalcare.id\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,2594)),"D:\\dentalcare.id\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9291,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\dentalcare.id\\src\\app\\page.tsx"],u="/page",x={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},158:(e,t,s)=>{Promise.resolve().then(s.bind(s,1772))},1772:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>Dashboard});var a=s(784),r=s(9212);let i={blue:"bg-blue-500",green:"bg-green-500",yellow:"bg-yellow-500",red:"bg-red-500",purple:"bg-purple-500"};function StatsCard({title:e,value:t,icon:s,trend:r,color:n}){return a.jsx("div",{className:"card",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx("div",{className:`w-12 h-12 ${i[n]} rounded-lg flex items-center justify-center text-white`,children:s})}),(0,a.jsxs)("div",{className:"ml-4 flex-1",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:e}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:t}),r&&(0,a.jsxs)("p",{className:`text-sm ${r.isPositive?"text-green-600":"text-red-600"}`,children:[r.isPositive?"+":"-",Math.abs(r.value),"%",a.jsx("span",{className:"text-gray-500 ml-1",children:"dari bulan lalu"})]})]})]})})}var n=s(3416),l=s(8750),c=s(1790);function TodayAppointments(){let e=n.Q7.filter(e=>"2024-01-20"===e.date),getStatusColor=e=>{switch(e){case"scheduled":default:return"status-scheduled";case"confirmed":return"status-confirmed";case"in-progress":return"status-in-progress";case"completed":return"status-completed";case"cancelled":return"status-cancelled"}},getStatusText=e=>{switch(e){case"scheduled":return"Terjadwal";case"confirmed":return"Dikonfirmasi";case"in-progress":return"Berlangsung";case"completed":return"Selesai";case"cancelled":return"Dibatalkan";default:return e}};return(0,a.jsxs)("div",{className:"card",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Jadwal Hari Ini"}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[e.length," appointment"]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[e.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx("div",{className:"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center",children:a.jsx(l.Z,{className:"w-5 h-5 text-primary-600"})})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium text-gray-900",children:e.patientName}),a.jsx("p",{className:"text-sm text-gray-600",children:e.type}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Dr. ",e.doctorName]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600 mb-2",children:[a.jsx(c.Z,{className:"w-4 h-4 mr-1"}),e.time," (",e.duration," menit)"]}),a.jsx("span",{className:`status-badge ${getStatusColor(e.status)}`,children:getStatusText(e.status)})]})]},e.id)),0===e.length&&a.jsx("div",{className:"text-center py-8 text-gray-500",children:"Tidak ada appointment hari ini"})]})]})}var d=s(9885),o=s(9904),u=s(2373),x=s(4751);function useRealTimeDashboard(){let{tenantId:e}=(0,x.S)(),[t,s]=(0,d.useState)({todayAppointments:0,todayRevenue:0,totalPatients:0,monthlyRevenue:0,pendingPayments:0,lowStockItems:0});return(0,d.useEffect)(()=>{if(!e)return;let t=new Date().toISOString().split("T")[0],a=new Date().toISOString().slice(0,7),r=(0,o.IO)((0,o.hJ)(u.db,"dentalcare",e,"appointments"),(0,o.ar)("date","==",t)),i=(0,o.cf)(r,e=>{s(t=>({...t,todayAppointments:e.size}))}),n=(0,o.IO)((0,o.hJ)(u.db,"dentalcare",e,"patients")),l=(0,o.cf)(n,e=>{s(t=>({...t,totalPatients:e.size}))}),c=(0,o.IO)((0,o.hJ)(u.db,"dentalcare",e,"invoices"),(0,o.ar)("status","in",["draft","sent","overdue"])),d=(0,o.cf)(c,e=>{s(t=>({...t,pendingPayments:e.size}))}),fetchMonthlyRevenue=async()=>{try{let r=(0,o.IO)((0,o.hJ)(u.db,"dentalcare",e,"invoices"),(0,o.ar)("status","==","paid"),(0,o.ar)("date",">=",a+"-01"),(0,o.ar)("date","<",getNextMonth(a)+"-01")),i=await (0,o.PL)(r),n=0,l=0;i.docs.forEach(e=>{let s=e.data();n+=s.total||0,s.date===t&&(l+=s.total||0)}),s(e=>({...e,monthlyRevenue:n,todayRevenue:l}))}catch(e){console.error("Error fetching monthly revenue:",e)}};fetchMonthlyRevenue();let x=(0,o.IO)((0,o.hJ)(u.db,"dentalcare",e,"inventory")),m=(0,o.cf)(x,e=>{let t=0;e.docs.forEach(e=>{let s=e.data();s.currentStock<=s.minStock&&t++}),s(e=>({...e,lowStockItems:t}))});return()=>{i(),l(),d(),m()}},[e]),t}function getNextMonth(e){let[t,s]=e.split("-").map(Number);return`${12===s?t+1:t}-${(12===s?1:s+1).toString().padStart(2,"0")}`}var m=s(534),p=s(1879),h=s(5636),g=s(7863),j=s(9448),v=s(9503);function Dashboard(){let{data:e=[]}=(0,m.w5)(),t=useRealTimeDashboard(),formatCurrency=e=>new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0}).format(e);return(0,a.jsxs)("div",{className:"flex-1 overflow-auto",children:[a.jsx(r.Z,{title:"Dashboard",subtitle:"Selamat datang di sistem manajemen klinik gigi"}),(0,a.jsxs)("main",{className:"p-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8",children:[a.jsx(StatsCard,{title:"Appointment Hari Ini",value:t.todayAppointments,icon:a.jsx(p.Z,{className:"w-6 h-6"}),color:"blue",trend:{value:12,isPositive:!0}}),a.jsx(StatsCard,{title:"Pendapatan Hari Ini",value:formatCurrency(t.todayRevenue),icon:a.jsx(h.Z,{className:"w-6 h-6"}),color:"green",trend:{value:8,isPositive:!0}}),a.jsx(StatsCard,{title:"Total Pasien",value:e.length,icon:a.jsx(g.Z,{className:"w-6 h-6"}),color:"purple",trend:{value:15,isPositive:!0}}),a.jsx(StatsCard,{title:"Pendapatan Bulan Ini",value:formatCurrency(t.monthlyRevenue),icon:a.jsx(j.Z,{className:"w-6 h-6"}),color:"blue",trend:{value:5,isPositive:!0}}),a.jsx(StatsCard,{title:"Pembayaran Tertunda",value:t.pendingPayments,icon:a.jsx(c.Z,{className:"w-6 h-6"}),color:"yellow"}),a.jsx(StatsCard,{title:"Stok Menipis",value:t.lowStockItems,icon:a.jsx(v.Z,{className:"w-6 h-6"}),color:"red"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[a.jsx("div",{className:"lg:col-span-2",children:a.jsx(TodayAppointments,{})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"card",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Aksi Cepat"}),(0,a.jsxs)("div",{className:"space-y-3",children:[a.jsx("button",{className:"w-full btn-primary text-left",children:"+ Tambah Pasien Baru"}),a.jsx("button",{className:"w-full btn-secondary text-left",children:"+ Buat Appointment"}),a.jsx("button",{className:"w-full btn-secondary text-left",children:"\uD83D\uDCCB Lihat Jadwal Hari Ini"}),a.jsx("button",{className:"w-full btn-secondary text-left",children:"\uD83D\uDCB0 Buat Invoice"})]})]}),(0,a.jsxs)("div",{className:"card",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Aktivitas Terbaru"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 text-sm",children:[a.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),a.jsx("span",{className:"text-gray-600",children:"Pembayaran dari Budi Santoso diterima"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 text-sm",children:[a.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),a.jsx("span",{className:"text-gray-600",children:"Appointment baru dari Sari Dewi"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 text-sm",children:[a.jsx("div",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),a.jsx("span",{className:"text-gray-600",children:"Stok Anestesi Lidocaine menipis"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 text-sm",children:[a.jsx("div",{className:"w-2 h-2 bg-purple-500 rounded-full"}),a.jsx("span",{className:"text-gray-600",children:"Treatment selesai untuk Andi Wijaya"})]})]})]})]})]})]})]})}},8203:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>i,default:()=>c});var a=s(5153);let r=(0,a.createProxy)(String.raw`D:\dentalcare.id\src\app\page.tsx`),{__esModule:i,$$typeof:n}=r,l=r.default,c=l}};var t=require("../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[172,684,713,706,29,212,416,534],()=>__webpack_exec__(8587));module.exports=s})();