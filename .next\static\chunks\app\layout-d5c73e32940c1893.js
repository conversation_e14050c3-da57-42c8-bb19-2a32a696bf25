(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[185],{7935:function(e,t,a){Promise.resolve().then(a.t.bind(a,2489,23)),Promise.resolve().then(a.bind(a,7292))},7292:function(e,t,a){"use strict";a.r(t),a.d(t,{Providers:function(){return Providers}});var r=a(7437),n=a(8038),i=a(3549),s=a(5495),l=a(8908);let o=new l.S({defaultOptions:{queries:{staleTime:3e5,gcTime:6e5,retry:(e,t)=>(null==t?void 0:t.code)!=="permission-denied"&&(null==t?void 0:t.code)!=="unauthenticated"&&e<3,refetchOnWindowFocus:!1},mutations:{retry:!1}}});var d=a(2265),c=a(4033),m=a(1543),u=a(9367),h=a(4086),p=a(8081),g=a(6831);async function setupDemoData(e){try{console.log("Setting up demo data for multi-tenant structure...");let t=e||"demo-tenant-".concat(Date.now());await (0,h.pl)((0,h.JU)(g.db,"dentalcare",t,"settings","clinic"),{id:t,name:"Klinik Gigi DentalCare Demo",address:"Jl. Sudirman No. 123, Jakarta Pusat",phone:"(021) 1234-5678",email:"<EMAIL>",settings:{timezone:"Asia/Jakarta",currency:"IDR",dateFormat:"DD/MM/YYYY",businessHours:{start:"08:00",end:"17:00",days:["monday","tuesday","wednesday","thursday","friday","saturday"]}},createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()});let a=[{email:"<EMAIL>",password:"demo123",profile:{name:"Dr. Sarah Putri",role:"doctor",tenantId:t,permissions:["read_patients","write_patients","manage_treatments","read_appointments","write_appointments"]}},{email:"<EMAIL>",password:"demo123",profile:{name:"Siti Nurhaliza",role:"receptionist",tenantId:t,permissions:["read_patients","write_patients","manage_appointments","manage_billing"]}},{email:"<EMAIL>",password:"demo123",profile:{name:"Ahmad Rahman",role:"admin",tenantId:t,permissions:["full_access"]}}];for(let e of a)try{let a=await (0,p.Xb)(g.I,e.email,e.password),r={id:a.user.uid,email:e.email,...e.profile,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};await (0,h.pl)((0,h.JU)(g.db,"users",a.user.uid),r),await (0,h.pl)((0,h.JU)(g.db,"dentalcare",t,"users",a.user.uid),r),console.log("Created user: ".concat(e.email))}catch(t){"auth/email-already-in-use"===t.code?console.log("User ".concat(e.email," already exists")):console.error("Error creating user ".concat(e.email,":"),t)}let r=[{medicalRecordNumber:"RM2024001",name:"Budi Santoso",email:"<EMAIL>",phone:"081234567890",dateOfBirth:"1985-03-15",address:"Jl. Sudirman No. 123, Jakarta",nik:"3171234567890001",gender:"male",emergencyContact:{name:"Siti Santoso",phone:"081234567891",relationship:"Istri"},medicalHistory:{allergies:["Penisilin"],medications:["Paracetamol"],conditions:["Hipertensi"]},clinicalImages:[],dentalChart:initializeDentalChart(),lastVisit:"2024-01-15",totalVisits:5,status:"active",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{medicalRecordNumber:"RM2024002",name:"Sari Dewi",email:"<EMAIL>",phone:"081234567892",dateOfBirth:"1990-07-22",address:"Jl. Thamrin No. 456, Jakarta",nik:"3171234567890002",gender:"female",emergencyContact:{name:"Ahmad Dewi",phone:"081234567893",relationship:"Suami"},medicalHistory:{allergies:[],medications:[],conditions:[]},clinicalImages:[],dentalChart:initializeDentalChart(),lastVisit:"2024-01-10",totalVisits:3,status:"active",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{medicalRecordNumber:"RM2024003",name:"Andi Wijaya",email:"<EMAIL>",phone:"081234567894",dateOfBirth:"1988-11-08",address:"Jl. Gatot Subroto No. 789, Jakarta",nik:"3171234567890003",gender:"male",emergencyContact:{name:"Maya Wijaya",phone:"081234567895",relationship:"Istri"},medicalHistory:{allergies:["Sulfa"],medications:[],conditions:["Diabetes"]},clinicalImages:[],dentalChart:initializeDentalChart(),lastVisit:"2024-01-08",totalVisits:8,status:"active",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}];for(let e of r)await (0,h.ET)((0,h.hJ)(g.db,"dentalcare",t,"patients"),e);for(let e of[{code:"CONS001",name:"Konsultasi",category:"Konsultasi",price:15e4,duration:30,description:"Konsultasi dan pemeriksaan gigi"},{code:"SCAL001",name:"Scaling",category:"Preventif",price:3e5,duration:60,description:"Pembersihan karang gigi"},{code:"FILL001",name:"Tambal Gigi",category:"Restoratif",price:25e4,duration:45,description:"Penambalan gigi dengan komposit"},{code:"CROW001",name:"Crown",category:"Prostetik",price:25e5,duration:120,description:"Pemasangan mahkota gigi"}])await (0,h.ET)((0,h.hJ)(g.db,"dentalcare",t,"treatments"),e);for(let e of[{name:"Komposit Resin",category:"Bahan Tambal",currentStock:15,minStock:10,unit:"tube",price:45e4,supplier:"PT Dental Supply",expiryDate:"2025-06-15",lastRestocked:"2024-01-01",status:"in-stock"},{name:"Anestesi Lidocaine",category:"Obat",currentStock:5,minStock:20,unit:"vial",price:25e3,supplier:"PT Pharma Dental",expiryDate:"2024-12-31",lastRestocked:"2023-12-15",status:"low-stock"},{name:"Sarung Tangan Latex",category:"Disposable",currentStock:0,minStock:50,unit:"box",price:85e3,supplier:"PT Medical Supply",lastRestocked:"2023-11-20",status:"out-of-stock"}])await (0,h.ET)((0,h.hJ)(g.db,"dentalcare",t,"inventory"),e);return console.log("Demo data setup completed for tenant: ".concat(t)),{success:!0,tenantId:t}}catch(e){return console.error("Error setting up demo data:",e),{success:!1,tenantId:null}}}function initializeDentalChart(){let e=[];for(let t=1;t<=32;t++)e.push({toothNumber:t,condition:"healthy",notes:"",updatedAt:new Date().toISOString()});return e}function LoginForm(){let[e,t]=(0,d.useState)(""),[a,n]=(0,d.useState)(""),[s,l]=(0,d.useState)(!1),[o,h]=(0,d.useState)(!1),[p,g]=(0,d.useState)(""),[x,f]=(0,d.useState)(!1),{signIn:y}=(0,i.a)(),w=(0,c.useRouter)(),handleSubmit=async t=>{t.preventDefault(),h(!0),g("");try{await y(e,a),w.push("/")}catch(e){g(getErrorMessage(e.message))}finally{h(!1)}},getErrorMessage=e=>{switch(e){case"auth/user-not-found":return"Email tidak terdaftar";case"auth/wrong-password":return"Password salah";case"auth/invalid-email":return"Format email tidak valid";case"auth/user-disabled":return"Akun telah dinonaktifkan";case"auth/too-many-requests":return"Terlalu banyak percobaan login. Coba lagi nanti";default:return"Terjadi kesalahan saat login"}},handleDemoLogin=(e,a)=>{t(e),n(a)},handleSetupDemo=async()=>{f(!0),g("");try{let e=await setupDemoData();e?alert("Demo data berhasil dibuat! Silakan login dengan akun demo."):g("Gagal membuat demo data")}catch(e){g("Error: "+e.message)}finally{f(!1)}};return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-primary-100 py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto h-16 w-16 bg-primary-600 rounded-xl flex items-center justify-center mb-4",children:(0,r.jsx)("span",{className:"text-white font-bold text-2xl",children:"DC"})}),(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900",children:"Login ke DentalCare"}),(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Sistem Manajemen Klinik Gigi"})]}),(0,r.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-4",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-green-900 mb-2",children:"Setup Demo Data:"}),(0,r.jsx)("button",{onClick:handleSetupDemo,disabled:x,className:"w-full bg-green-600 hover:bg-green-700 text-white text-sm py-2 px-4 rounded disabled:opacity-50",children:x?"Setting up...":"Create Demo Data & Users"}),(0,r.jsx)("p",{className:"text-xs text-green-700 mt-1",children:"Klik ini untuk membuat data demo dan user untuk testing"})]}),(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:"Demo Accounts:"}),(0,r.jsx)("div",{className:"space-y-1",children:[{email:"<EMAIL>",password:"demo123",role:"Dokter"},{email:"<EMAIL>",password:"demo123",role:"Resepsionis"},{email:"<EMAIL>",password:"demo123",role:"Admin"}].map((e,t)=>(0,r.jsxs)("button",{onClick:()=>handleDemoLogin(e.email,e.password),className:"block w-full text-left text-xs text-blue-700 hover:text-blue-900 hover:bg-blue-100 px-2 py-1 rounded",children:[e.role,": ",e.email]},t))})]}),(0,r.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:handleSubmit,children:[p&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg",children:p}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),(0,r.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"input-field",placeholder:"Masukkan email Anda",value:e,onChange:e=>t(e.target.value)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{id:"password",name:"password",type:s?"text":"password",autoComplete:"current-password",required:!0,className:"input-field pr-10",placeholder:"Masukkan password Anda",value:a,onChange:e=>n(e.target.value)}),(0,r.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>l(!s),children:s?(0,r.jsx)(m.Z,{className:"h-5 w-5 text-gray-400"}):(0,r.jsx)(u.Z,{className:"h-5 w-5 text-gray-400"})})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-900",children:"Ingat saya"})]}),(0,r.jsx)("div",{className:"text-sm",children:(0,r.jsx)("a",{href:"#",className:"font-medium text-primary-600 hover:text-primary-500",children:"Lupa password?"})})]}),(0,r.jsx)("button",{type:"submit",disabled:o,className:"w-full btn-primary flex justify-center items-center",children:o?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Logging in..."]}):"Login"})]}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"\xa9 2024 DentalCare. All rights reserved."})})]})})}var x=a(1396),f=a.n(x),y=a(4992),w=a(9649),b=a(4103),S=a(1296),j=a(6551),v=a(654),N=a(9010),D=a(1809);let k=[{name:"Dashboard",href:"/",icon:y.Z},{name:"Pasien",href:"/patients",icon:w.Z},{name:"Jadwal",href:"/appointments",icon:b.Z},{name:"Treatment",href:"/treatments",icon:S.Z},{name:"Inventory",href:"/inventory",icon:j.Z},{name:"Laporan",href:"/reports",icon:v.Z},{name:"Pengaturan",href:"/settings",icon:N.Z}];function Sidebar(){let e=(0,c.usePathname)();return(0,r.jsxs)("div",{className:"flex flex-col w-64 bg-white border-r border-gray-200 h-screen",children:[(0,r.jsx)("div",{className:"flex items-center justify-center h-16 px-4 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"DC"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"DentalCare"})]})}),(0,r.jsx)("nav",{className:"flex-1 px-4 py-6 space-y-2",children:k.map(t=>{let a=e===t.href;return(0,r.jsxs)(f(),{href:t.href,className:"sidebar-link ".concat(a?"active":""),children:[(0,r.jsx)(t.icon,{className:"w-5 h-5 mr-3"}),t.name]},t.name)})}),(0,r.jsx)("div",{className:"p-4 border-t border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center",children:(0,r.jsx)(D.Z,{className:"w-6 h-6 text-gray-600"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:"Dr. Sarah Putri"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 truncate",children:"Dokter Gigi"})]})]})})]})}function AppLayout(e){let{children:t}=e,{user:a,profile:n,loading:s}=(0,i.a)();return((0,c.usePathname)(),s)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading..."})]})}):a&&n?(0,r.jsxs)("div",{className:"flex h-screen bg-gray-50",children:[(0,r.jsx)(Sidebar,{}),(0,r.jsx)("div",{className:"flex-1 flex flex-col overflow-hidden",children:t})]}):(0,r.jsx)(LoginForm,{})}function Providers(e){let{children:t}=e;return(0,r.jsx)(n.aH,{client:o,children:(0,r.jsx)(i.H,{children:(0,r.jsx)(s.z,{children:(0,r.jsx)(AppLayout,{children:t})})})})}},3549:function(e,t,a){"use strict";a.d(t,{H:function(){return AuthProvider},a:function(){return useAuth}});var r=a(7437),n=a(2265),i=a(8081),s=a(4086),l=a(6831);let o=(0,n.createContext)(null);function AuthProvider(e){let{children:t}=e,[a,d]=(0,n.useState)(null),[c,m]=(0,n.useState)(null),[u,h]=(0,n.useState)(!0);(0,n.useEffect)(()=>{let e=(0,i.Aj)(l.I,async e=>{if(d(e),e)try{let t=await (0,s.QT)((0,s.JU)(l.db,"users",e.uid));if(t.exists()){let a=t.data();m({id:e.uid,email:e.email||"",...a})}else{let t={id:e.uid,name:e.displayName||"User",email:e.email||"",role:"receptionist",tenantId:"tenant_".concat(e.uid),permissions:["read_patients","manage_appointments"],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};await (0,s.pl)((0,s.JU)(l.db,"users",e.uid),t),m(t)}}catch(e){console.error("Error fetching user profile:",e)}else m(null);h(!1)});return e},[]);let signIn=async(e,t)=>{try{await (0,i.e5)(l.I,e,t)}catch(e){throw Error(e.message)}},signUp=async(e,t,a)=>{try{let r=await (0,i.Xb)(l.I,e,t),n=r.user,o={id:n.uid,email:n.email||e,...a,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};await (0,s.pl)((0,s.JU)(l.db,"users",n.uid),o),m(o)}catch(e){throw Error(e.message)}},logout=async()=>{try{await (0,i.w7)(l.I)}catch(e){throw Error(e.message)}},updateProfile=async e=>{if(!a||!c)throw Error("No user logged in");try{let t={...c,...e,updatedAt:new Date().toISOString()};await (0,s.pl)((0,s.JU)(l.db,"users",a.uid),t,{merge:!0}),m(t)}catch(e){throw Error(e.message)}};return(0,r.jsx)(o.Provider,{value:{user:a,profile:c,loading:u,signIn,signUp,logout,updateProfile},children:t})}let useAuth=()=>{let e=(0,n.useContext)(o);if(!e)throw Error("useAuth must be used within AuthProvider");return e}},5495:function(e,t,a){"use strict";a.d(t,{S:function(){return useTenant},z:function(){return TenantProvider}});var r=a(7437),n=a(2265),i=a(4086),s=a(6831),l=a(3549);let o=(0,n.createContext)(null);function TenantProvider(e){let{children:t}=e,[a,d]=(0,n.useState)(null),[c,m]=(0,n.useState)(null),[u,h]=(0,n.useState)(!0),{user:p,profile:g}=(0,l.a)();(0,n.useEffect)(()=>{let loadTenant=async()=>{if(!p||!(null==g?void 0:g.tenantId)){d(null),m(null),h(!1);return}try{let e=await (0,i.QT)((0,i.JU)(s.db,"dentalcare",g.tenantId,"settings","clinic"));if(e.exists()){let t=e.data();d({...t,id:g.tenantId}),m(g.tenantId)}else{let e={id:g.tenantId,name:"Klinik Gigi",address:"",phone:"",email:g.email,settings:{timezone:"Asia/Jakarta",currency:"IDR",dateFormat:"DD/MM/YYYY",businessHours:{start:"08:00",end:"17:00",days:["monday","tuesday","wednesday","thursday","friday","saturday"]}},createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};await (0,i.pl)((0,i.JU)(s.db,"dentalcare",g.tenantId,"settings","clinic"),e),d(e),m(g.tenantId)}}catch(e){console.error("Error loading tenant:",e)}finally{h(!1)}};loadTenant()},[p,g]);let switchTenant=async e=>{if(!p)throw Error("No user logged in");try{h(!0),await (0,i.pl)((0,i.JU)(s.db,"users",p.uid),{tenantId:e,updatedAt:new Date().toISOString()},{merge:!0});let t=await (0,i.QT)((0,i.JU)(s.db,"dentalcare",e,"settings","clinic"));if(t.exists()){let a=t.data();d({...a,id:e}),m(e)}}catch(e){throw console.error("Error switching tenant:",e),Error("Failed to switch tenant")}finally{h(!1)}},updateTenant=async e=>{if(!c||!a)throw Error("No tenant selected");try{let t={...a,...e,updatedAt:new Date().toISOString()};await (0,i.pl)((0,i.JU)(s.db,"dentalcare",c,"settings","clinic"),t,{merge:!0}),d(t)}catch(e){throw console.error("Error updating tenant:",e),Error("Failed to update tenant")}},createTenant=async e=>{if(!p)throw Error("No user logged in");try{let t="tenant_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),a={...e,id:t,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return await (0,i.pl)((0,i.JU)(s.db,"dentalcare",t,"settings","clinic"),a),await (0,i.pl)((0,i.JU)(s.db,"users",p.uid),{tenantId:t,updatedAt:new Date().toISOString()},{merge:!0}),d(a),m(t),t}catch(e){throw console.error("Error creating tenant:",e),Error("Failed to create tenant")}};return(0,r.jsx)(o.Provider,{value:{tenant:a,tenantId:c,loading:u,switchTenant,updateTenant,createTenant},children:t})}let useTenant=()=>{let e=(0,n.useContext)(o);if(!e)throw Error("useTenant must be used within TenantProvider");return e}},6831:function(e,t,a){"use strict";a.d(t,{I:function(){return o},db:function(){return l}});var r=a(994),n=a(4086),i=a(8081);let s=(0,r.ZF)({apiKey:"AIzaSyDv9u4NhGvNFVVsou_IrXMO__rlfXfCKIk",authDomain:"widigital-d6110.firebaseapp.com",projectId:"widigital-d6110",storageBucket:"widigital-d6110.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:0d8752f8175569f67d6825"}),l=(0,n.ad)(s),o=(0,i.v0)(s)},2489:function(){}},function(e){e.O(0,[609,15,801,273,982,971,472,744],function(){return e(e.s=7935)}),_N_E=e.O()}]);