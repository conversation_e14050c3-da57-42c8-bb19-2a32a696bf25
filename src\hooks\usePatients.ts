import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { patientService } from '@/services/patients';
import { useAuth } from '@/contexts/AuthContext';
import { Patient } from '@/types';

/**
 * Get all patients for current clinic
 */
export function usePatients() {
  const { profile } = useAuth();
  
  return useQuery({
    queryKey: ['patients', profile?.clinicId],
    queryFn: () => patientService.getPatientsByClinic(profile!.clinicId),
    enabled: !!profile?.clinicId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Get single patient by ID
 */
export function usePatient(id: string) {
  return useQuery({
    queryKey: ['patient', id],
    queryFn: () => patientService.getPatient(id),
    enabled: !!id,
  });
}

/**
 * Search patients
 */
export function useSearchPatients(searchTerm: string) {
  const { profile } = useAuth();
  
  return useQuery({
    queryKey: ['patients', 'search', profile?.clinicId, searchTerm],
    queryFn: () => patientService.searchPatients(profile!.clinicId, searchTerm),
    enabled: !!profile?.clinicId && searchTerm.length >= 2,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

/**
 * Create new patient
 */
export function useCreatePatient() {
  const queryClient = useQueryClient();
  const { profile } = useAuth();

  return useMutation({
    mutationFn: (patientData: Omit<Patient, 'id' | 'medicalRecordNumber' | 'clinicId'>) => 
      patientService.createPatient({ 
        ...patientData, 
        clinicId: profile!.clinicId 
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['patients'] });
    },
    onError: (error) => {
      console.error('Error creating patient:', error);
    }
  });
}

/**
 * Update patient
 */
export function useUpdatePatient() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<Patient> }) =>
      patientService.updatePatient(id, updates),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['patients'] });
      queryClient.invalidateQueries({ queryKey: ['patient', id] });
    },
    onError: (error) => {
      console.error('Error updating patient:', error);
    }
  });
}

/**
 * Delete patient
 */
export function useDeletePatient() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => patientService.deletePatient(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['patients'] });
    },
    onError: (error) => {
      console.error('Error deleting patient:', error);
    }
  });
}

/**
 * Add clinical image to patient
 */
export function useAddClinicalImage() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ 
      patientId, 
      imageData 
    }: { 
      patientId: string; 
      imageData: {
        type: 'intraoral' | 'extraoral' | 'xray';
        image: string;
        description?: string;
      }
    }) => patientService.addClinicalImage(patientId, imageData),
    onSuccess: (_, { patientId }) => {
      queryClient.invalidateQueries({ queryKey: ['patient', patientId] });
      queryClient.invalidateQueries({ queryKey: ['patients'] });
    },
    onError: (error) => {
      console.error('Error adding clinical image:', error);
    }
  });
}

/**
 * Update dental chart
 */
export function useUpdateDentalChart() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ 
      patientId, 
      toothNumber, 
      condition, 
      notes 
    }: { 
      patientId: string; 
      toothNumber: number; 
      condition: Patient['dentalChart'][0]['condition'];
      notes?: string;
    }) => patientService.updateDentalChart(patientId, toothNumber, condition, notes),
    onSuccess: (_, { patientId }) => {
      queryClient.invalidateQueries({ queryKey: ['patient', patientId] });
    },
    onError: (error) => {
      console.error('Error updating dental chart:', error);
    }
  });
}

/**
 * Real-time patients subscription
 */
export function useRealTimePatients() {
  const { profile } = useAuth();
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!profile?.clinicId) {
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    const unsubscribe = patientService.subscribeToPatients(
      profile.clinicId,
      (updatedPatients) => {
        setPatients(updatedPatients);
        setLoading(false);
      }
    );

    return () => {
      unsubscribe();
    };
  }, [profile?.clinicId]);

  return { patients, loading, error };
}
