import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { PatientService } from '@/services/patients';
import { TenantServiceRegistry } from '@/services/base/TenantService';
import { useTenant } from '@/contexts/TenantContext';
import { Patient } from '@/types';

/**
 * Get all patients for current tenant
 */
export function usePatients() {
  const { tenantId } = useTenant();

  return useQuery({
    queryKey: ['patients', tenantId],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const patientService = TenantServiceRegistry.getService(PatientService, tenantId, 'patients');
      return patientService.getPatients();
    },
    enabled: !!tenantId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Get single patient by ID
 */
export function usePatient(id: string) {
  const { tenantId } = useTenant();

  return useQuery({
    queryKey: ['patient', tenantId, id],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const patientService = TenantServiceRegistry.getService(PatientService, tenantId, 'patients');
      return patientService.getPatient(id);
    },
    enabled: !!id && !!tenantId,
  });
}

/**
 * Search patients
 */
export function useSearchPatients(searchTerm: string) {
  const { tenantId } = useTenant();

  return useQuery({
    queryKey: ['patients', 'search', tenantId, searchTerm],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const patientService = TenantServiceRegistry.getService(PatientService, tenantId, 'patients');
      return patientService.searchPatients(searchTerm);
    },
    enabled: !!tenantId && searchTerm.length >= 2,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

/**
 * Create new patient
 */
export function useCreatePatient() {
  const queryClient = useQueryClient();
  const { tenantId } = useTenant();

  return useMutation({
    mutationFn: (patientData: Omit<Patient, 'id' | 'medicalRecordNumber'>) => {
      if (!tenantId) throw new Error('No tenant selected');
      const patientService = TenantServiceRegistry.getService(PatientService, tenantId, 'patients');
      return patientService.createPatient(patientData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['patients', tenantId] });
    },
    onError: (error) => {
      console.error('Error creating patient:', error);
    }
  });
}

/**
 * Update patient
 */
export function useUpdatePatient() {
  const queryClient = useQueryClient();
  const { tenantId } = useTenant();

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<Patient> }) => {
      if (!tenantId) throw new Error('No tenant selected');
      const patientService = TenantServiceRegistry.getService(PatientService, tenantId, 'patients');
      return patientService.updatePatient(id, updates);
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['patients', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['patient', tenantId, id] });
    },
    onError: (error) => {
      console.error('Error updating patient:', error);
    }
  });
}

/**
 * Delete patient
 */
export function useDeletePatient() {
  const queryClient = useQueryClient();
  const { tenantId } = useTenant();

  return useMutation({
    mutationFn: (id: string) => {
      if (!tenantId) throw new Error('No tenant selected');
      const patientService = TenantServiceRegistry.getService(PatientService, tenantId, 'patients');
      return patientService.deletePatient(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['patients', tenantId] });
    },
    onError: (error) => {
      console.error('Error deleting patient:', error);
    }
  });
}

/**
 * Add clinical image to patient
 */
export function useAddClinicalImage() {
  const queryClient = useQueryClient();
  const { tenantId } = useTenant();

  return useMutation({
    mutationFn: ({
      patientId,
      imageData
    }: {
      patientId: string;
      imageData: {
        type: 'intraoral' | 'extraoral' | 'xray';
        image: string;
        description?: string;
      }
    }) => {
      if (!tenantId) throw new Error('No tenant selected');
      const patientService = TenantServiceRegistry.getService(PatientService, tenantId, 'patients');
      return patientService.addClinicalImage(patientId, imageData);
    },
    onSuccess: (_, { patientId }) => {
      queryClient.invalidateQueries({ queryKey: ['patient', tenantId, patientId] });
      queryClient.invalidateQueries({ queryKey: ['patients', tenantId] });
    },
    onError: (error) => {
      console.error('Error adding clinical image:', error);
    }
  });
}

/**
 * Update dental chart
 */
export function useUpdateDentalChart() {
  const queryClient = useQueryClient();
  const { tenantId } = useTenant();

  return useMutation({
    mutationFn: ({
      patientId,
      toothNumber,
      condition,
      notes
    }: {
      patientId: string;
      toothNumber: number;
      condition: 'healthy' | 'caries' | 'filled' | 'crown' | 'missing' | 'root_canal';
      notes?: string;
    }) => {
      if (!tenantId) throw new Error('No tenant selected');
      const patientService = TenantServiceRegistry.getService(PatientService, tenantId, 'patients');
      return patientService.updateDentalChart(patientId, toothNumber, condition, notes);
    },
    onSuccess: (_, { patientId }) => {
      queryClient.invalidateQueries({ queryKey: ['patient', tenantId, patientId] });
    },
    onError: (error) => {
      console.error('Error updating dental chart:', error);
    }
  });
}

/**
 * Real-time patients subscription
 */
export function useRealTimePatients() {
  const { tenantId } = useTenant();
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!tenantId) {
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    const patientService = TenantServiceRegistry.getService(PatientService, tenantId, 'patients');
    const unsubscribe = patientService.subscribeToPatients(
      (updatedPatients: Patient[]) => {
        setPatients(updatedPatients);
        setLoading(false);
      }
    );

    return () => {
      unsubscribe();
    };
  }, [tenantId]);

  return { patients, loading, error };
}
