"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[273],{1809:function(t,e,i){var n=i(2265);function UserIcon({title:t,titleId:e,...i},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":e},i),t?n.createElement("title",{id:e},t):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))}let s=n.forwardRef(UserIcon);e.Z=s},9198:function(t,e,i){i.d(e,{j:function(){return r}});var n=i(2996),s=i(300),r=new class extends n.l{#t;#e;#i;constructor(){super(),this.#i=t=>{if(!s.sk&&window.addEventListener){let listener=()=>t();return window.addEventListener("visibilitychange",listener,!1),()=>{window.removeEventListener("visibilitychange",listener)}}}}onSubscribe(){this.#e||this.setEventListener(this.#i)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#i=t,this.#e?.(),this.#e=t(t=>{"boolean"==typeof t?this.setFocused(t):this.onFocus()})}setFocused(t){let e=this.#t!==t;e&&(this.#t=t,this.onFocus())}onFocus(){let t=this.isFocused();this.listeners.forEach(e=>{e(t)})}isFocused(){return"boolean"==typeof this.#t?this.#t:globalThis.document?.visibilityState!=="hidden"}}},7470:function(t,e,i){i.d(e,{R:function(){return getDefaultState},m:function(){return o}});var n=i(7987),s=i(9024),r=i(1640),o=class extends s.F{#n;#s;#r;constructor(t){super(),this.mutationId=t.mutationId,this.#s=t.mutationCache,this.#n=[],this.state=t.state||getDefaultState(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){this.#n.includes(t)||(this.#n.push(t),this.clearGcTimeout(),this.#s.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.#n=this.#n.filter(e=>e!==t),this.scheduleGc(),this.#s.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.#n.length||("pending"===this.state.status?this.scheduleGc():this.#s.remove(this))}continue(){return this.#r?.continue()??this.execute(this.state.variables)}async execute(t){let onContinue=()=>{this.#o({type:"continue"})};this.#r=(0,r.Mz)({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(Error("No mutationFn found")),onFail:(t,e)=>{this.#o({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#o({type:"pause"})},onContinue,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#s.canRun(this)});let e="pending"===this.state.status,i=!this.#r.canStart();try{if(e)onContinue();else{this.#o({type:"pending",variables:t,isPaused:i}),await this.#s.config.onMutate?.(t,this);let e=await this.options.onMutate?.(t);e!==this.state.context&&this.#o({type:"pending",context:e,variables:t,isPaused:i})}let n=await this.#r.start();return await this.#s.config.onSuccess?.(n,t,this.state.context,this),await this.options.onSuccess?.(n,t,this.state.context),await this.#s.config.onSettled?.(n,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(n,null,t,this.state.context),this.#o({type:"success",data:n}),n}catch(e){try{throw await this.#s.config.onError?.(e,t,this.state.context,this),await this.options.onError?.(e,t,this.state.context),await this.#s.config.onSettled?.(void 0,e,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,e,t,this.state.context),e}finally{this.#o({type:"error",error:e})}}finally{this.#s.runNext(this)}}#o(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),n.Vr.batch(()=>{this.#n.forEach(e=>{e.onMutationUpdate(t)}),this.#s.notify({mutation:this,type:"updated",action:t})})}};function getDefaultState(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},7987:function(t,e,i){i.d(e,{Vr:function(){return n}});var defaultScheduler=t=>setTimeout(t,0),n=function(){let t=[],e=0,notifyFn=t=>{t()},batchNotifyFn=t=>{t()},i=defaultScheduler,schedule=n=>{e?t.push(n):i(()=>{notifyFn(n)})},flush=()=>{let e=t;t=[],e.length&&i(()=>{batchNotifyFn(()=>{e.forEach(t=>{notifyFn(t)})})})};return{batch:t=>{let i;e++;try{i=t()}finally{--e||flush()}return i},batchCalls:t=>(...e)=>{schedule(()=>{t(...e)})},schedule,setNotifyFunction:t=>{notifyFn=t},setBatchNotifyFunction:t=>{batchNotifyFn=t},setScheduler:t=>{i=t}}}()},436:function(t,e,i){i.d(e,{N:function(){return r}});var n=i(2996),s=i(300),r=new class extends n.l{#a=!0;#e;#i;constructor(){super(),this.#i=t=>{if(!s.sk&&window.addEventListener){let onlineListener=()=>t(!0),offlineListener=()=>t(!1);return window.addEventListener("online",onlineListener,!1),window.addEventListener("offline",offlineListener,!1),()=>{window.removeEventListener("online",onlineListener),window.removeEventListener("offline",offlineListener)}}}}onSubscribe(){this.#e||this.setEventListener(this.#i)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#i=t,this.#e?.(),this.#e=t(this.setOnline.bind(this))}setOnline(t){let e=this.#a!==t;e&&(this.#a=t,this.listeners.forEach(e=>{e(t)}))}isOnline(){return this.#a}}},3002:function(t,e,i){i.d(e,{A:function(){return a},z:function(){return fetchState}});var n=i(300),s=i(7987),r=i(1640),o=i(9024),a=class extends o.F{#u;#c;#h;#l;#r;#d;#f;constructor(t){super(),this.#f=!1,this.#d=t.defaultOptions,this.setOptions(t.options),this.observers=[],this.#l=t.client,this.#h=this.#l.getQueryCache(),this.queryKey=t.queryKey,this.queryHash=t.queryHash,this.#u=getDefaultState(this.options),this.state=t.state??this.#u,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#r?.promise}setOptions(t){this.options={...this.#d,...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#h.remove(this)}setData(t,e){let i=(0,n.oE)(this.state.data,t,this.options);return this.#o({data:i,type:"success",dataUpdatedAt:e?.updatedAt,manual:e?.manual}),i}setState(t,e){this.#o({type:"setState",state:t,setStateOptions:e})}cancel(t){let e=this.#r?.promise;return this.#r?.cancel(t),e?e.then(n.ZT).catch(n.ZT):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#u)}isActive(){return this.observers.some(t=>!1!==(0,n.Nc)(t.options.enabled,this))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===n.CN||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0&&this.observers.some(t=>"static"===(0,n.KC)(t.options.staleTime,this))}isStale(){return this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):void 0===this.state.data||this.state.isInvalidated}isStaleByTime(t=0){return void 0===this.state.data||"static"!==t&&(!!this.state.isInvalidated||!(0,n.Kp)(this.state.dataUpdatedAt,t))}onFocus(){let t=this.observers.find(t=>t.shouldFetchOnWindowFocus());t?.refetch({cancelRefetch:!1}),this.#r?.continue()}onOnline(){let t=this.observers.find(t=>t.shouldFetchOnReconnect());t?.refetch({cancelRefetch:!1}),this.#r?.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),this.#h.notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(e=>e!==t),this.observers.length||(this.#r&&(this.#f?this.#r.cancel({revert:!0}):this.#r.cancelRetry()),this.scheduleGc()),this.#h.notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#o({type:"invalidate"})}fetch(t,e){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&e?.cancelRefetch)this.cancel({silent:!0});else if(this.#r)return this.#r.continueRetry(),this.#r.promise}if(t&&this.setOptions(t),!this.options.queryFn){let t=this.observers.find(t=>t.options.queryFn);t&&this.setOptions(t.options)}let i=new AbortController,addSignalProperty=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(this.#f=!0,i.signal)})},fetchFn=()=>{let t=(0,n.cG)(this.options,e),i=(()=>{let t={client:this.#l,queryKey:this.queryKey,meta:this.meta};return addSignalProperty(t),t})();return(this.#f=!1,this.options.persister)?this.options.persister(t,i,this):t(i)},s=(()=>{let t={fetchOptions:e,options:this.options,queryKey:this.queryKey,client:this.#l,state:this.state,fetchFn};return addSignalProperty(t),t})();this.options.behavior?.onFetch(s,this),this.#c=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==s.fetchOptions?.meta)&&this.#o({type:"fetch",meta:s.fetchOptions?.meta});let onError=t=>{(0,r.DV)(t)&&t.silent||this.#o({type:"error",error:t}),(0,r.DV)(t)||(this.#h.config.onError?.(t,this),this.#h.config.onSettled?.(this.state.data,t,this)),this.scheduleGc()};return this.#r=(0,r.Mz)({initialPromise:e?.initialPromise,fn:s.fetchFn,abort:i.abort.bind(i),onSuccess:t=>{if(void 0===t){onError(Error(`${this.queryHash} data is undefined`));return}try{this.setData(t)}catch(t){onError(t);return}this.#h.config.onSuccess?.(t,this),this.#h.config.onSettled?.(t,this.state.error,this),this.scheduleGc()},onError,onFail:(t,e)=>{this.#o({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#o({type:"pause"})},onContinue:()=>{this.#o({type:"continue"})},retry:s.options.retry,retryDelay:s.options.retryDelay,networkMode:s.options.networkMode,canRun:()=>!0}),this.#r.start()}#o(t){this.state=(e=>{switch(t.type){case"failed":return{...e,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...e,fetchStatus:"paused"};case"continue":return{...e,fetchStatus:"fetching"};case"fetch":return{...e,...fetchState(e.data,this.options),fetchMeta:t.meta??null};case"success":return this.#c=void 0,{...e,data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let i=t.error;if((0,r.DV)(i)&&i.revert&&this.#c)return{...this.#c,fetchStatus:"idle"};return{...e,error:i,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,fetchFailureReason:i,fetchStatus:"idle",status:"error"};case"invalidate":return{...e,isInvalidated:!0};case"setState":return{...e,...t.state}}})(this.state),s.Vr.batch(()=>{this.observers.forEach(t=>{t.onQueryUpdate()}),this.#h.notify({query:this,type:"updated",action:t})})}};function fetchState(t,e){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:(0,r.Kw)(e.networkMode)?"fetching":"paused",...void 0===t&&{error:null,status:"pending"}}}function getDefaultState(t){let e="function"==typeof t.initialData?t.initialData():t.initialData,i=void 0!==e,n=i?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:i?n??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:i?"success":"pending",fetchStatus:"idle"}}},9024:function(t,e,i){i.d(e,{F:function(){return s}});var n=i(300),s=class{#p;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,n.PN)(this.gcTime)&&(this.#p=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(n.sk?1/0:3e5))}clearGcTimeout(){this.#p&&(clearTimeout(this.#p),this.#p=void 0)}}},1640:function(t,e,i){i.d(e,{DV:function(){return isCancelledError},Kw:function(){return canFetch},Mz:function(){return createRetryer}});var n=i(9198),s=i(436),r=i(8684),o=i(300);function defaultRetryDelay(t){return Math.min(1e3*2**t,3e4)}function canFetch(t){return(t??"online")!=="online"||s.N.isOnline()}var a=class extends Error{constructor(t){super("CancelledError"),this.revert=t?.revert,this.silent=t?.silent}};function isCancelledError(t){return t instanceof a}function createRetryer(t){let e,i=!1,u=0,c=!1,h=(0,r.O)(),canContinue=()=>n.j.isFocused()&&("always"===t.networkMode||s.N.isOnline())&&t.canRun(),canStart=()=>canFetch(t.networkMode)&&t.canRun(),resolve=i=>{c||(c=!0,t.onSuccess?.(i),e?.(),h.resolve(i))},reject=i=>{c||(c=!0,t.onError?.(i),e?.(),h.reject(i))},pause=()=>new Promise(i=>{e=t=>{(c||canContinue())&&i(t)},t.onPause?.()}).then(()=>{e=void 0,c||t.onContinue?.()}),run=()=>{let e;if(c)return;let n=0===u?t.initialPromise:void 0;try{e=n??t.fn()}catch(t){e=Promise.reject(t)}Promise.resolve(e).then(resolve).catch(e=>{if(c)return;let n=t.retry??(o.sk?0:3),s=t.retryDelay??defaultRetryDelay,r="function"==typeof s?s(u,e):s,a=!0===n||"number"==typeof n&&u<n||"function"==typeof n&&n(u,e);if(i||!a){reject(e);return}u++,t.onFail?.(u,e),(0,o._v)(r).then(()=>canContinue()?void 0:pause()).then(()=>{i?reject(e):run()})})};return{promise:h,cancel:e=>{c||(reject(new a(e)),t.abort?.())},continue:()=>(e?.(),h),cancelRetry:()=>{i=!0},continueRetry:()=>{i=!1},canStart,start:()=>(canStart()?run():pause().then(run),h)}}},2996:function(t,e,i){i.d(e,{l:function(){return n}});var n=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},8684:function(t,e,i){i.d(e,{O:function(){return pendingThenable}});function pendingThenable(){let t,e;let i=new Promise((i,n)=>{t=i,e=n});function finalize(t){Object.assign(i,t),delete i.resolve,delete i.reject}return i.status="pending",i.catch(()=>{}),i.resolve=e=>{finalize({status:"fulfilled",value:e}),t(e)},i.reject=t=>{finalize({status:"rejected",reason:t}),e(t)},i}},300:function(t,e,i){i.d(e,{CN:function(){return s},Ht:function(){return addToStart},KC:function(){return resolveStaleTime},Kp:function(){return timeUntilStale},L3:function(){return shouldThrowError},Nc:function(){return resolveEnabled},PN:function(){return isValidTimeout},Rm:function(){return hashQueryKeyByOptions},SE:function(){return functionalUpdate},VS:function(){return shallowEqualObjects},VX:function(){return addToEnd},X7:function(){return matchMutation},Ym:function(){return hashKey},ZT:function(){return noop},_v:function(){return sleep},_x:function(){return matchQuery},cG:function(){return ensureQueryFn},oE:function(){return replaceData},sk:function(){return n},to:function(){return partialMatchKey}});var n="undefined"==typeof window||"Deno"in globalThis;function noop(){}function functionalUpdate(t,e){return"function"==typeof t?t(e):t}function isValidTimeout(t){return"number"==typeof t&&t>=0&&t!==1/0}function timeUntilStale(t,e){return Math.max(t+(e||0)-Date.now(),0)}function resolveStaleTime(t,e){return"function"==typeof t?t(e):t}function resolveEnabled(t,e){return"function"==typeof t?t(e):t}function matchQuery(t,e){let{type:i="all",exact:n,fetchStatus:s,predicate:r,queryKey:o,stale:a}=t;if(o){if(n){if(e.queryHash!==hashQueryKeyByOptions(o,e.options))return!1}else if(!partialMatchKey(e.queryKey,o))return!1}if("all"!==i){let t=e.isActive();if("active"===i&&!t||"inactive"===i&&t)return!1}return("boolean"!=typeof a||e.isStale()===a)&&(!s||s===e.state.fetchStatus)&&(!r||!!r(e))}function matchMutation(t,e){let{exact:i,status:n,predicate:s,mutationKey:r}=t;if(r){if(!e.options.mutationKey)return!1;if(i){if(hashKey(e.options.mutationKey)!==hashKey(r))return!1}else if(!partialMatchKey(e.options.mutationKey,r))return!1}return(!n||e.state.status===n)&&(!s||!!s(e))}function hashQueryKeyByOptions(t,e){let i=e?.queryKeyHashFn||hashKey;return i(t)}function hashKey(t){return JSON.stringify(t,(t,e)=>isPlainObject(e)?Object.keys(e).sort().reduce((t,i)=>(t[i]=e[i],t),{}):e)}function partialMatchKey(t,e){return t===e||typeof t==typeof e&&!!t&&!!e&&"object"==typeof t&&"object"==typeof e&&Object.keys(e).every(i=>partialMatchKey(t[i],e[i]))}function replaceEqualDeep(t,e){if(t===e)return t;let i=isPlainArray(t)&&isPlainArray(e);if(i||isPlainObject(t)&&isPlainObject(e)){let n=i?t:Object.keys(t),s=n.length,r=i?e:Object.keys(e),o=r.length,a=i?[]:{},u=new Set(n),c=0;for(let n=0;n<o;n++){let s=i?n:r[n];(!i&&u.has(s)||i)&&void 0===t[s]&&void 0===e[s]?(a[s]=void 0,c++):(a[s]=replaceEqualDeep(t[s],e[s]),a[s]===t[s]&&void 0!==t[s]&&c++)}return s===o&&c===s?t:a}return e}function shallowEqualObjects(t,e){if(!e||Object.keys(t).length!==Object.keys(e).length)return!1;for(let i in t)if(t[i]!==e[i])return!1;return!0}function isPlainArray(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function isPlainObject(t){if(!hasObjectPrototype(t))return!1;let e=t.constructor;if(void 0===e)return!0;let i=e.prototype;return!!(hasObjectPrototype(i)&&i.hasOwnProperty("isPrototypeOf"))&&Object.getPrototypeOf(t)===Object.prototype}function hasObjectPrototype(t){return"[object Object]"===Object.prototype.toString.call(t)}function sleep(t){return new Promise(e=>{setTimeout(e,t)})}function replaceData(t,e,i){return"function"==typeof i.structuralSharing?i.structuralSharing(t,e):!1!==i.structuralSharing?replaceEqualDeep(t,e):e}function addToEnd(t,e,i=0){let n=[...t,e];return i&&n.length>i?n.slice(1):n}function addToStart(t,e,i=0){let n=[e,...t];return i&&n.length>i?n.slice(0,-1):n}var s=Symbol();function ensureQueryFn(t,e){return!t.queryFn&&e?.initialPromise?()=>e.initialPromise:t.queryFn&&t.queryFn!==s?t.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${t.queryHash}'`))}function shouldThrowError(t,e){return"function"==typeof t?t(...e):!!t}},8038:function(t,e,i){i.d(e,{NL:function(){return useQueryClient},aH:function(){return QueryClientProvider}});var n=i(2265),s=i(7437),r=n.createContext(void 0),useQueryClient=t=>{let e=n.useContext(r);if(t)return t;if(!e)throw Error("No QueryClient set, use QueryClientProvider to set one");return e},QueryClientProvider=({client:t,children:e})=>(n.useEffect(()=>(t.mount(),()=>{t.unmount()}),[t]),(0,s.jsx)(r.Provider,{value:t,children:e}))}}]);