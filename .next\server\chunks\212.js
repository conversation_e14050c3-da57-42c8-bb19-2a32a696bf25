"use strict";exports.id=212,exports.ids=[212],exports.modules={9212:(e,s,a)=>{a.d(s,{Z:()=>Header});var r=a(784),t=a(6198),n=a(5050),i=a(9885),l=a(4751),c=a(8157),d=a(7254),o=a(4129),x=a(4918),m=a(990);function TenantSwitcher({className:e=""}){let{tenant:s,tenantId:a,loading:t,switchTenant:n,createTenant:u}=(0,l.S)(),{profile:h}=(0,c.a)(),[p,y]=(0,i.useState)(!1),[g,f]=(0,i.useState)(!1),[b,j]=(0,i.useState)(""),N=a?[{id:a,name:s?.name||"Current Clinic"}]:[],handleSwitchTenant=async e=>{if(e===a){y(!1);return}try{await n(e),y(!1)}catch(e){console.error("Failed to switch tenant:",e)}},handleCreateTenant=async()=>{if(b.trim())try{f(!0),await u({name:b.trim(),address:"",phone:"",email:h?.email||""}),j(""),y(!1)}catch(e){console.error("Failed to create tenant:",e)}finally{f(!1)}};return t?(0,r.jsxs)("div",{className:`flex items-center space-x-2 ${e}`,children:[r.jsx("div",{className:"w-8 h-8 bg-gray-200 rounded-lg animate-pulse"}),r.jsx("div",{className:"w-32 h-4 bg-gray-200 rounded animate-pulse"})]}):(0,r.jsxs)("div",{className:`relative ${e}`,children:[(0,r.jsxs)("button",{onClick:()=>y(!p),className:"flex items-center space-x-3 w-full px-3 py-2 text-left bg-white border border-gray-200 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",children:[r.jsx("div",{className:"flex-shrink-0",children:r.jsx(d.Z,{className:"w-5 h-5 text-gray-400"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[r.jsx("p",{className:"text-sm font-medium text-gray-900 truncate",children:s?.name||"Select Clinic"}),r.jsx("p",{className:"text-xs text-gray-500 truncate",children:s?.address||"No address set"})]}),r.jsx(o.Z,{className:`w-4 h-4 text-gray-400 transition-transform ${p?"transform rotate-180":""}`})]}),p&&r.jsx("div",{className:"absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg",children:(0,r.jsxs)("div",{className:"py-1",children:[N.map(e=>(0,r.jsxs)("button",{onClick:()=>handleSwitchTenant(e.id),className:"flex items-center w-full px-3 py-2 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50",children:[r.jsx("div",{className:"flex-1",children:r.jsx("p",{className:"text-sm font-medium text-gray-900",children:e.name})}),e.id===a&&r.jsx(x.Z,{className:"w-4 h-4 text-primary-600"})]},e.id)),r.jsx("div",{className:"border-t border-gray-100 mt-1 pt-1",children:(0,r.jsxs)("div",{className:"px-3 py-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("input",{type:"text",placeholder:"New clinic name",value:b,onChange:e=>j(e.target.value),className:"flex-1 px-2 py-1 text-sm border border-gray-200 rounded focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500",onKeyPress:e=>{"Enter"===e.key&&handleCreateTenant()}}),r.jsx("button",{onClick:handleCreateTenant,disabled:!b.trim()||g,className:"flex items-center justify-center w-8 h-8 text-white bg-primary-600 rounded hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed",children:g?r.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}):r.jsx(m.Z,{className:"w-4 h-4"})})]}),r.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Create a new clinic"})]})})]})}),p&&r.jsx("div",{className:"fixed inset-0 z-40",onClick:()=>y(!1)})]})}function Header({title:e,subtitle:s}){return r.jsx("header",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:e}),s&&r.jsx("p",{className:"text-sm text-gray-600 mt-1",children:s})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx(TenantSwitcher,{className:"w-64"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx(t.Z,{className:"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),r.jsx("input",{type:"text",placeholder:"Cari pasien, appointment...",className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent w-80"})]}),(0,r.jsxs)("button",{className:"relative p-2 text-gray-400 hover:text-gray-600 transition-colors",children:[r.jsx(n.Z,{className:"w-6 h-6"}),r.jsx("span",{className:"absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"})]}),r.jsx("div",{className:"text-sm text-gray-600",children:new Date().toLocaleDateString("id-ID",{weekday:"long",year:"numeric",month:"long",day:"numeric"})})]})]})})}}};