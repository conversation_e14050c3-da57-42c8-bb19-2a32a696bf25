import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { TreatmentService } from '@/services/treatments';
import { TenantServiceRegistry } from '@/services/base/TenantService';
import { useTenant } from '@/contexts/TenantContext';
import { Treatment } from '@/types';

/**
 * Get all treatments for current tenant
 */
export function useTreatments() {
  const { tenantId } = useTenant();
  
  return useQuery({
    queryKey: ['treatments', tenantId],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const treatmentService = TenantServiceRegistry.getService(TreatmentService, tenantId, 'treatments');
      return treatmentService.getTreatments();
    },
    enabled: !!tenantId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Get single treatment by ID
 */
export function useTreatment(id: string) {
  const { tenantId } = useTenant();
  
  return useQuery({
    queryKey: ['treatment', tenantId, id],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const treatmentService = TenantServiceRegistry.getService(TreatmentService, tenantId, 'treatments');
      return treatmentService.getTreatment(id);
    },
    enabled: !!id && !!tenantId,
  });
}

/**
 * Get treatments by category
 */
export function useTreatmentsByCategory(category: string) {
  const { tenantId } = useTenant();
  
  return useQuery({
    queryKey: ['treatments', 'category', tenantId, category],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const treatmentService = TenantServiceRegistry.getService(TreatmentService, tenantId, 'treatments');
      return treatmentService.getTreatmentsByCategory(category);
    },
    enabled: !!tenantId && !!category,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Search treatments
 */
export function useSearchTreatments(searchTerm: string) {
  const { tenantId } = useTenant();
  
  return useQuery({
    queryKey: ['treatments', 'search', tenantId, searchTerm],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const treatmentService = TenantServiceRegistry.getService(TreatmentService, tenantId, 'treatments');
      return treatmentService.searchTreatments(searchTerm);
    },
    enabled: !!tenantId && searchTerm.length >= 2,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

/**
 * Get treatment categories
 */
export function useTreatmentCategories() {
  const { tenantId } = useTenant();
  
  return useQuery({
    queryKey: ['treatment-categories', tenantId],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const treatmentService = TenantServiceRegistry.getService(TreatmentService, tenantId, 'treatments');
      return treatmentService.getTreatmentCategories();
    },
    enabled: !!tenantId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Get treatments by price range
 */
export function useTreatmentsByPriceRange(minPrice: number, maxPrice: number) {
  const { tenantId } = useTenant();
  
  return useQuery({
    queryKey: ['treatments', 'price-range', tenantId, minPrice, maxPrice],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const treatmentService = TenantServiceRegistry.getService(TreatmentService, tenantId, 'treatments');
      return treatmentService.getTreatmentsByPriceRange(minPrice, maxPrice);
    },
    enabled: !!tenantId && minPrice >= 0 && maxPrice > minPrice,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Create new treatment
 */
export function useCreateTreatment() {
  const queryClient = useQueryClient();
  const { tenantId } = useTenant();

  return useMutation({
    mutationFn: (treatmentData: Omit<Treatment, 'id'>) => {
      if (!tenantId) throw new Error('No tenant selected');
      const treatmentService = TenantServiceRegistry.getService(TreatmentService, tenantId, 'treatments');
      return treatmentService.createTreatment(treatmentData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['treatments', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['treatment-categories', tenantId] });
    },
    onError: (error) => {
      console.error('Error creating treatment:', error);
    }
  });
}

/**
 * Update treatment
 */
export function useUpdateTreatment() {
  const queryClient = useQueryClient();
  const { tenantId } = useTenant();

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<Treatment> }) => {
      if (!tenantId) throw new Error('No tenant selected');
      const treatmentService = TenantServiceRegistry.getService(TreatmentService, tenantId, 'treatments');
      return treatmentService.updateTreatment(id, updates);
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['treatments', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['treatment', tenantId, id] });
      queryClient.invalidateQueries({ queryKey: ['treatment-categories', tenantId] });
    },
    onError: (error) => {
      console.error('Error updating treatment:', error);
    }
  });
}

/**
 * Delete treatment
 */
export function useDeleteTreatment() {
  const queryClient = useQueryClient();
  const { tenantId } = useTenant();

  return useMutation({
    mutationFn: (id: string) => {
      if (!tenantId) throw new Error('No tenant selected');
      const treatmentService = TenantServiceRegistry.getService(TreatmentService, tenantId, 'treatments');
      return treatmentService.deleteTreatment(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['treatments', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['treatment-categories', tenantId] });
    },
    onError: (error) => {
      console.error('Error deleting treatment:', error);
    }
  });
}

/**
 * Bulk create treatments
 */
export function useBulkCreateTreatments() {
  const queryClient = useQueryClient();
  const { tenantId } = useTenant();

  return useMutation({
    mutationFn: (treatmentsData: Omit<Treatment, 'id'>[]) => {
      if (!tenantId) throw new Error('No tenant selected');
      const treatmentService = TenantServiceRegistry.getService(TreatmentService, tenantId, 'treatments');
      return treatmentService.bulkCreateTreatments(treatmentsData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['treatments', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['treatment-categories', tenantId] });
    },
    onError: (error) => {
      console.error('Error bulk creating treatments:', error);
    }
  });
}

/**
 * Real-time treatments subscription
 */
export function useRealTimeTreatments() {
  const { tenantId } = useTenant();
  const [treatments, setTreatments] = useState<Treatment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!tenantId) {
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    const treatmentService = TenantServiceRegistry.getService(TreatmentService, tenantId, 'treatments');
    const unsubscribe = treatmentService.subscribeToTreatments(
      (updatedTreatments: Treatment[]) => {
        setTreatments(updatedTreatments);
        setLoading(false);
      }
    );

    return () => {
      unsubscribe();
    };
  }, [tenantId]);

  return { treatments, loading, error };
}

/**
 * Real-time treatments by category subscription
 */
export function useRealTimeTreatmentsByCategory(category: string) {
  const { tenantId } = useTenant();
  const [treatments, setTreatments] = useState<Treatment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!tenantId || !category) {
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    const treatmentService = TenantServiceRegistry.getService(TreatmentService, tenantId, 'treatments');
    const unsubscribe = treatmentService.subscribeToTreatmentsByCategory(
      category,
      (updatedTreatments: Treatment[]) => {
        setTreatments(updatedTreatments);
        setLoading(false);
      }
    );

    return () => {
      unsubscribe();
    };
  }, [tenantId, category]);

  return { treatments, loading, error };
}
