'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { useAuth } from './AuthContext';

export interface TenantInfo {
  id: string;
  name: string;
  address: string;
  phone: string;
  email: string;
  settings?: {
    timezone?: string;
    currency?: string;
    dateFormat?: string;
    businessHours?: {
      start: string;
      end: string;
      days: string[];
    };
  };
  createdAt: string;
  updatedAt: string;
}

interface TenantContextType {
  tenant: TenantInfo | null;
  tenantId: string | null;
  loading: boolean;
  switchTenant: (tenantId: string) => Promise<void>;
  updateTenant: (updates: Partial<TenantInfo>) => Promise<void>;
  createTenant: (tenantData: Omit<TenantInfo, 'id' | 'createdAt' | 'updatedAt'>) => Promise<string>;
}

const TenantContext = createContext<TenantContextType | null>(null);

export function TenantProvider({ children }: { children: React.ReactNode }) {
  const [tenant, setTenant] = useState<TenantInfo | null>(null);
  const [tenantId, setTenantId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const { user, profile } = useAuth();

  useEffect(() => {
    const loadTenant = async () => {
      if (!user || !profile?.tenantId) {
        setTenant(null);
        setTenantId(null);
        setLoading(false);
        return;
      }

      try {
        const tenantDoc = await getDoc(doc(db, 'dentalcare', profile.tenantId, 'settings', 'clinic'));
        
        if (tenantDoc.exists()) {
          const tenantData = tenantDoc.data() as TenantInfo;
          setTenant({ ...tenantData, id: profile.tenantId });
          setTenantId(profile.tenantId);
        } else {
          // Create default tenant settings if they don't exist
          const defaultTenant: TenantInfo = {
            id: profile.tenantId,
            name: 'Klinik Gigi',
            address: '',
            phone: '',
            email: profile.email,
            settings: {
              timezone: 'Asia/Jakarta',
              currency: 'IDR',
              dateFormat: 'DD/MM/YYYY',
              businessHours: {
                start: '08:00',
                end: '17:00',
                days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
              }
            },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };

          await setDoc(doc(db, 'dentalcare', profile.tenantId, 'settings', 'clinic'), defaultTenant);
          setTenant(defaultTenant);
          setTenantId(profile.tenantId);
        }
      } catch (error) {
        console.error('Error loading tenant:', error);
      } finally {
        setLoading(false);
      }
    };

    loadTenant();
  }, [user, profile]);

  const switchTenant = async (newTenantId: string) => {
    if (!user) throw new Error('No user logged in');
    
    try {
      setLoading(true);
      
      // Update user's tenantId in global users collection
      await setDoc(doc(db, 'users', user.uid), {
        tenantId: newTenantId,
        updatedAt: new Date().toISOString()
      }, { merge: true });

      // Load new tenant data
      const tenantDoc = await getDoc(doc(db, 'dentalcare', newTenantId, 'settings', 'clinic'));
      
      if (tenantDoc.exists()) {
        const tenantData = tenantDoc.data() as TenantInfo;
        setTenant({ ...tenantData, id: newTenantId });
        setTenantId(newTenantId);
      }
    } catch (error) {
      console.error('Error switching tenant:', error);
      throw new Error('Failed to switch tenant');
    } finally {
      setLoading(false);
    }
  };

  const updateTenant = async (updates: Partial<TenantInfo>) => {
    if (!tenantId || !tenant) throw new Error('No tenant selected');
    
    try {
      const updatedTenant = {
        ...tenant,
        ...updates,
        updatedAt: new Date().toISOString()
      };
      
      await setDoc(doc(db, 'dentalcare', tenantId, 'settings', 'clinic'), updatedTenant, { merge: true });
      setTenant(updatedTenant);
    } catch (error) {
      console.error('Error updating tenant:', error);
      throw new Error('Failed to update tenant');
    }
  };

  const createTenant = async (tenantData: Omit<TenantInfo, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> => {
    if (!user) throw new Error('No user logged in');
    
    try {
      // Generate tenant ID (could be UUID or custom format)
      const newTenantId = `tenant_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const newTenant: TenantInfo = {
        ...tenantData,
        id: newTenantId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      // Create tenant settings
      await setDoc(doc(db, 'dentalcare', newTenantId, 'settings', 'clinic'), newTenant);
      
      // Update user's tenantId
      await setDoc(doc(db, 'users', user.uid), {
        tenantId: newTenantId,
        updatedAt: new Date().toISOString()
      }, { merge: true });
      
      setTenant(newTenant);
      setTenantId(newTenantId);
      
      return newTenantId;
    } catch (error) {
      console.error('Error creating tenant:', error);
      throw new Error('Failed to create tenant');
    }
  };

  return (
    <TenantContext.Provider value={{
      tenant,
      tenantId,
      loading,
      switchTenant,
      updateTenant,
      createTenant
    }}>
      {children}
    </TenantContext.Provider>
  );
}

export const useTenant = () => {
  const context = useContext(TenantContext);
  if (!context) {
    throw new Error('useTenant must be used within TenantProvider');
  }
  return context;
};
