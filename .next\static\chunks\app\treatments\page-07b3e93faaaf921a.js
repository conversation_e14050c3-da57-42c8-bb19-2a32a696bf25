(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[774],{6459:function(e,t,a){Promise.resolve().then(a.bind(a,110))},110:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return TreatmentsPage}});var r=a(7437),n=a(2265),s=a(2621),i=a(5658),l=a(5255),o=a(4020),c=a(2125);function TrashIcon({title:e,titleId:t,...a},r){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},a),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))}let d=n.forwardRef(TrashIcon);function TreatmentsPage(){let[e,t]=(0,n.useState)(""),[a,m]=(0,n.useState)("all"),[u]=(0,n.useState)(i.pM),x=["all",...Array.from(new Set(u.map(e=>e.category)))],h=u.filter(t=>{let r=t.name.toLowerCase().includes(e.toLowerCase())||t.code.toLowerCase().includes(e.toLowerCase()),n="all"===a||t.category===a;return r&&n}),formatCurrency=e=>new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0}).format(e),getCategoryColor=e=>({Konsultasi:"bg-blue-100 text-blue-800",Preventif:"bg-green-100 text-green-800",Restoratif:"bg-yellow-100 text-yellow-800",Prostetik:"bg-purple-100 text-purple-800",Bedah:"bg-red-100 text-red-800"})[e]||"bg-gray-100 text-gray-800";return(0,r.jsxs)("div",{className:"flex-1 overflow-auto",children:[(0,r.jsx)(s.Z,{title:"Manajemen Treatment",subtitle:"Kelola katalog treatment dan tarif"}),(0,r.jsx)("main",{className:"p-6",children:(0,r.jsxs)("div",{className:"card",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Katalog Treatment"}),(0,r.jsxs)("button",{className:"btn-primary flex items-center",children:[(0,r.jsx)(l.Z,{className:"w-4 h-4 mr-2"}),"Tambah Treatment"]})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(o.Z,{className:"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,r.jsx)("input",{type:"text",placeholder:"Cari treatment berdasarkan nama atau kode...",className:"input-field pl-10",value:e,onChange:e=>t(e.target.value)})]}),(0,r.jsxs)("select",{className:"input-field w-full sm:w-48",value:a,onChange:e=>m(e.target.value),children:[(0,r.jsx)("option",{value:"all",children:"Semua Kategori"}),x.filter(e=>"all"!==e).map(e=>(0,r.jsx)("option",{value:e,children:e},e))]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:h.map(e=>(0,r.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"flex items-center space-x-2 mb-2",children:(0,r.jsx)("span",{className:"status-badge ".concat(getCategoryColor(e.category)),children:e.category})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:e.name}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 mb-2",children:["Kode: ",e.code]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{className:"text-gray-400 hover:text-gray-600",children:(0,r.jsx)(c.Z,{className:"w-4 h-4"})}),(0,r.jsx)("button",{className:"text-gray-400 hover:text-red-600",children:(0,r.jsx)(d,{className:"w-4 h-4"})})]})]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:e.description}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Tarif:"}),(0,r.jsx)("span",{className:"text-lg font-bold text-primary-600",children:formatCurrency(e.price)})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Durasi:"}),(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:[e.duration," menit"]})]})]}),(0,r.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,r.jsx)("button",{className:"w-full btn-secondary text-sm",children:"Gunakan untuk Appointment"})})]},e.id))}),0===h.length&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-400 mb-4",children:(0,r.jsx)("svg",{className:"w-12 h-12 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Tidak ada treatment ditemukan"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Coba ubah filter pencarian atau tambah treatment baru"}),(0,r.jsx)("button",{className:"btn-primary",children:"Tambah Treatment Baru"})]}),h.length>0&&(0,r.jsx)("div",{className:"mt-8 pt-6 border-t border-gray-200",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:h.length}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Total Treatment"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:x.length-1}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Kategori"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:formatCurrency(Math.min(...h.map(e=>e.price)))}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Tarif Terendah"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:formatCurrency(Math.max(...h.map(e=>e.price)))}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Tarif Tertinggi"})]})]})})]})})]})}},5658:function(e,t,a){"use strict";a.d(t,{Q7:function(){return r},pM:function(){return n},sP:function(){return s}});let r=[{id:"1",patientId:"1",patientName:"Budi Santoso",doctorId:"doc1",doctorName:"Dr. Sarah Putri",date:"2024-01-20",time:"09:00",duration:60,type:"Konsultasi",status:"scheduled",notes:"Keluhan sakit gigi geraham kiri",treatmentPlan:["Konsultasi","Scaling"]},{id:"2",patientId:"2",patientName:"Sari Dewi",doctorId:"doc1",doctorName:"Dr. Sarah Putri",date:"2024-01-20",time:"10:30",duration:90,type:"Scaling",status:"confirmed",treatmentPlan:["Scaling","Fluoride Treatment"]},{id:"3",patientId:"3",patientName:"Andi Wijaya",doctorId:"doc2",doctorName:"Dr. Ahmad Rahman",date:"2024-01-20",time:"14:00",duration:120,type:"Crown",status:"in-progress",treatmentPlan:["Crown Preparation","Temporary Crown"]}],n=[{id:"1",code:"CONS001",name:"Konsultasi",category:"Konsultasi",price:15e4,duration:30,description:"Konsultasi dan pemeriksaan gigi"},{id:"2",code:"SCAL001",name:"Scaling",category:"Preventif",price:3e5,duration:60,description:"Pembersihan karang gigi"},{id:"3",code:"FILL001",name:"Tambal Gigi",category:"Restoratif",price:25e4,duration:45,description:"Penambalan gigi dengan komposit"},{id:"4",code:"CROW001",name:"Crown",category:"Prostetik",price:25e5,duration:120,description:"Pemasangan mahkota gigi"}],s=[{id:"1",name:"Komposit Resin",category:"Bahan Tambal",currentStock:15,minStock:10,unit:"tube",price:45e4,supplier:"PT Dental Supply",expiryDate:"2025-06-15",lastRestocked:"2024-01-01",status:"in-stock"},{id:"2",name:"Anestesi Lidocaine",category:"Obat",currentStock:5,minStock:20,unit:"vial",price:25e3,supplier:"PT Pharma Dental",expiryDate:"2024-12-31",lastRestocked:"2023-12-15",status:"low-stock"},{id:"3",name:"Sarung Tangan Latex",category:"Disposable",currentStock:0,minStock:50,unit:"box",price:85e3,supplier:"PT Medical Supply",lastRestocked:"2023-11-20",status:"out-of-stock"}]},7965:function(e,t,a){"use strict";var r=a(2265);function BellIcon({title:e,titleId:t,...a},n){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},a),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}))}let n=r.forwardRef(BellIcon);t.Z=n},9267:function(e,t,a){"use strict";var r=a(2265);function BuildingOfficeIcon({title:e,titleId:t,...a},n){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},a),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h1.5m-1.5 3h1.5m-1.5 3h1.5m3-6H15m-1.5 3H15m-1.5 3H15M9 21v-3.375c0-.621.504-1.125 1.125-1.125h3.75c.621 0 1.125.504 1.125 1.125V21"}))}let n=r.forwardRef(BuildingOfficeIcon);t.Z=n},7805:function(e,t,a){"use strict";var r=a(2265);function CheckIcon({title:e,titleId:t,...a},n){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},a),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))}let n=r.forwardRef(CheckIcon);t.Z=n},4164:function(e,t,a){"use strict";var r=a(2265);function ChevronDownIcon({title:e,titleId:t,...a},n){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},a),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m19.5 8.25-7.5 7.5-7.5-7.5"}))}let n=r.forwardRef(ChevronDownIcon);t.Z=n},4020:function(e,t,a){"use strict";var r=a(2265);function MagnifyingGlassIcon({title:e,titleId:t,...a},n){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},a),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))}let n=r.forwardRef(MagnifyingGlassIcon);t.Z=n},2125:function(e,t,a){"use strict";var r=a(2265);function PencilIcon({title:e,titleId:t,...a},n){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},a),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))}let n=r.forwardRef(PencilIcon);t.Z=n},5255:function(e,t,a){"use strict";var r=a(2265);function PlusIcon({title:e,titleId:t,...a},n){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},a),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))}let n=r.forwardRef(PlusIcon);t.Z=n}},function(e){e.O(0,[609,15,801,621,971,472,744],function(){return e(e.s=6459)}),_N_E=e.O()}]);