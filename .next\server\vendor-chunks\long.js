/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/long";
exports.ids = ["vendor-chunks/long"];
exports.modules = {

/***/ "(ssr)/./node_modules/long/umd/index.js":
/*!****************************************!*\
  !*** ./node_modules/long/umd/index.js ***!
  \****************************************/
/***/ (function(module, exports) {

eval("var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;// GENERATED FILE. DO NOT EDIT.\n(function (global, factory) {\n  function preferDefault(exports) {\n    return exports.default || exports;\n  }\n  if (true) {\n    !(__WEBPACK_AMD_DEFINE_ARRAY__ = [], __WEBPACK_AMD_DEFINE_RESULT__ = (function () {\n      var exports = {};\n      factory(exports);\n      return preferDefault(exports);\n    }).apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n  } else {}\n})(\n  typeof globalThis !== \"undefined\"\n    ? globalThis\n    : typeof self !== \"undefined\"\n      ? self\n      : this,\n  function (_exports) {\n    \"use strict\";\n\n    Object.defineProperty(_exports, \"__esModule\", {\n      value: true,\n    });\n    _exports.default = void 0;\n    /**\n     * @license\n     * Copyright 2009 The Closure Library Authors\n     * Copyright 2020 Daniel Wirtz / The long.js Authors.\n     *\n     * Licensed under the Apache License, Version 2.0 (the \"License\");\n     * you may not use this file except in compliance with the License.\n     * You may obtain a copy of the License at\n     *\n     *     http://www.apache.org/licenses/LICENSE-2.0\n     *\n     * Unless required by applicable law or agreed to in writing, software\n     * distributed under the License is distributed on an \"AS IS\" BASIS,\n     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n     * See the License for the specific language governing permissions and\n     * limitations under the License.\n     *\n     * SPDX-License-Identifier: Apache-2.0\n     */\n\n    // WebAssembly optimizations to do native i64 multiplication and divide\n    var wasm = null;\n    try {\n      wasm = new WebAssembly.Instance(\n        new WebAssembly.Module(\n          new Uint8Array([\n            // \\0asm\n            0, 97, 115, 109,\n            // version 1\n            1, 0, 0, 0,\n            // section \"type\"\n            1, 13, 2,\n            // 0, () => i32\n            96, 0, 1, 127,\n            // 1, (i32, i32, i32, i32) => i32\n            96, 4, 127, 127, 127, 127, 1, 127,\n            // section \"function\"\n            3, 7, 6,\n            // 0, type 0\n            0,\n            // 1, type 1\n            1,\n            // 2, type 1\n            1,\n            // 3, type 1\n            1,\n            // 4, type 1\n            1,\n            // 5, type 1\n            1,\n            // section \"global\"\n            6, 6, 1,\n            // 0, \"high\", mutable i32\n            127, 1, 65, 0, 11,\n            // section \"export\"\n            7, 50, 6,\n            // 0, \"mul\"\n            3, 109, 117, 108, 0, 1,\n            // 1, \"div_s\"\n            5, 100, 105, 118, 95, 115, 0, 2,\n            // 2, \"div_u\"\n            5, 100, 105, 118, 95, 117, 0, 3,\n            // 3, \"rem_s\"\n            5, 114, 101, 109, 95, 115, 0, 4,\n            // 4, \"rem_u\"\n            5, 114, 101, 109, 95, 117, 0, 5,\n            // 5, \"get_high\"\n            8, 103, 101, 116, 95, 104, 105, 103, 104, 0, 0,\n            // section \"code\"\n            10, 191, 1, 6,\n            // 0, \"get_high\"\n            4, 0, 35, 0, 11,\n            // 1, \"mul\"\n            36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173,\n            32, 3, 173, 66, 32, 134, 132, 126, 34, 4, 66, 32, 135, 167, 36, 0,\n            32, 4, 167, 11,\n            // 2, \"div_s\"\n            36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173,\n            32, 3, 173, 66, 32, 134, 132, 127, 34, 4, 66, 32, 135, 167, 36, 0,\n            32, 4, 167, 11,\n            // 3, \"div_u\"\n            36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173,\n            32, 3, 173, 66, 32, 134, 132, 128, 34, 4, 66, 32, 135, 167, 36, 0,\n            32, 4, 167, 11,\n            // 4, \"rem_s\"\n            36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173,\n            32, 3, 173, 66, 32, 134, 132, 129, 34, 4, 66, 32, 135, 167, 36, 0,\n            32, 4, 167, 11,\n            // 5, \"rem_u\"\n            36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173,\n            32, 3, 173, 66, 32, 134, 132, 130, 34, 4, 66, 32, 135, 167, 36, 0,\n            32, 4, 167, 11,\n          ]),\n        ),\n        {},\n      ).exports;\n    } catch {\n      // no wasm support :(\n    }\n\n    /**\n     * Constructs a 64 bit two's-complement integer, given its low and high 32 bit values as *signed* integers.\n     *  See the from* functions below for more convenient ways of constructing Longs.\n     * @exports Long\n     * @class A Long class for representing a 64 bit two's-complement integer value.\n     * @param {number} low The low (signed) 32 bits of the long\n     * @param {number} high The high (signed) 32 bits of the long\n     * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n     * @constructor\n     */\n    function Long(low, high, unsigned) {\n      /**\n       * The low 32 bits as a signed value.\n       * @type {number}\n       */\n      this.low = low | 0;\n\n      /**\n       * The high 32 bits as a signed value.\n       * @type {number}\n       */\n      this.high = high | 0;\n\n      /**\n       * Whether unsigned or not.\n       * @type {boolean}\n       */\n      this.unsigned = !!unsigned;\n    }\n\n    // The internal representation of a long is the two given signed, 32-bit values.\n    // We use 32-bit pieces because these are the size of integers on which\n    // Javascript performs bit-operations.  For operations like addition and\n    // multiplication, we split each number into 16 bit pieces, which can easily be\n    // multiplied within Javascript's floating-point representation without overflow\n    // or change in sign.\n    //\n    // In the algorithms below, we frequently reduce the negative case to the\n    // positive case by negating the input(s) and then post-processing the result.\n    // Note that we must ALWAYS check specially whether those values are MIN_VALUE\n    // (-2^63) because -MIN_VALUE == MIN_VALUE (since 2^63 cannot be represented as\n    // a positive number, it overflows back into a negative).  Not handling this\n    // case would often result in infinite recursion.\n    //\n    // Common constant values ZERO, ONE, NEG_ONE, etc. are defined below the from*\n    // methods on which they depend.\n\n    /**\n     * An indicator used to reliably determine if an object is a Long or not.\n     * @type {boolean}\n     * @const\n     * @private\n     */\n    Long.prototype.__isLong__;\n    Object.defineProperty(Long.prototype, \"__isLong__\", {\n      value: true,\n    });\n\n    /**\n     * @function\n     * @param {*} obj Object\n     * @returns {boolean}\n     * @inner\n     */\n    function isLong(obj) {\n      return (obj && obj[\"__isLong__\"]) === true;\n    }\n\n    /**\n     * @function\n     * @param {*} value number\n     * @returns {number}\n     * @inner\n     */\n    function ctz32(value) {\n      var c = Math.clz32(value & -value);\n      return value ? 31 - c : c;\n    }\n\n    /**\n     * Tests if the specified object is a Long.\n     * @function\n     * @param {*} obj Object\n     * @returns {boolean}\n     */\n    Long.isLong = isLong;\n\n    /**\n     * A cache of the Long representations of small integer values.\n     * @type {!Object}\n     * @inner\n     */\n    var INT_CACHE = {};\n\n    /**\n     * A cache of the Long representations of small unsigned integer values.\n     * @type {!Object}\n     * @inner\n     */\n    var UINT_CACHE = {};\n\n    /**\n     * @param {number} value\n     * @param {boolean=} unsigned\n     * @returns {!Long}\n     * @inner\n     */\n    function fromInt(value, unsigned) {\n      var obj, cachedObj, cache;\n      if (unsigned) {\n        value >>>= 0;\n        if ((cache = 0 <= value && value < 256)) {\n          cachedObj = UINT_CACHE[value];\n          if (cachedObj) return cachedObj;\n        }\n        obj = fromBits(value, 0, true);\n        if (cache) UINT_CACHE[value] = obj;\n        return obj;\n      } else {\n        value |= 0;\n        if ((cache = -128 <= value && value < 128)) {\n          cachedObj = INT_CACHE[value];\n          if (cachedObj) return cachedObj;\n        }\n        obj = fromBits(value, value < 0 ? -1 : 0, false);\n        if (cache) INT_CACHE[value] = obj;\n        return obj;\n      }\n    }\n\n    /**\n     * Returns a Long representing the given 32 bit integer value.\n     * @function\n     * @param {number} value The 32 bit integer in question\n     * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n     * @returns {!Long} The corresponding Long value\n     */\n    Long.fromInt = fromInt;\n\n    /**\n     * @param {number} value\n     * @param {boolean=} unsigned\n     * @returns {!Long}\n     * @inner\n     */\n    function fromNumber(value, unsigned) {\n      if (isNaN(value)) return unsigned ? UZERO : ZERO;\n      if (unsigned) {\n        if (value < 0) return UZERO;\n        if (value >= TWO_PWR_64_DBL) return MAX_UNSIGNED_VALUE;\n      } else {\n        if (value <= -TWO_PWR_63_DBL) return MIN_VALUE;\n        if (value + 1 >= TWO_PWR_63_DBL) return MAX_VALUE;\n      }\n      if (value < 0) return fromNumber(-value, unsigned).neg();\n      return fromBits(\n        value % TWO_PWR_32_DBL | 0,\n        (value / TWO_PWR_32_DBL) | 0,\n        unsigned,\n      );\n    }\n\n    /**\n     * Returns a Long representing the given value, provided that it is a finite number. Otherwise, zero is returned.\n     * @function\n     * @param {number} value The number in question\n     * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n     * @returns {!Long} The corresponding Long value\n     */\n    Long.fromNumber = fromNumber;\n\n    /**\n     * @param {number} lowBits\n     * @param {number} highBits\n     * @param {boolean=} unsigned\n     * @returns {!Long}\n     * @inner\n     */\n    function fromBits(lowBits, highBits, unsigned) {\n      return new Long(lowBits, highBits, unsigned);\n    }\n\n    /**\n     * Returns a Long representing the 64 bit integer that comes by concatenating the given low and high bits. Each is\n     *  assumed to use 32 bits.\n     * @function\n     * @param {number} lowBits The low 32 bits\n     * @param {number} highBits The high 32 bits\n     * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n     * @returns {!Long} The corresponding Long value\n     */\n    Long.fromBits = fromBits;\n\n    /**\n     * @function\n     * @param {number} base\n     * @param {number} exponent\n     * @returns {number}\n     * @inner\n     */\n    var pow_dbl = Math.pow; // Used 4 times (4*8 to 15+4)\n\n    /**\n     * @param {string} str\n     * @param {(boolean|number)=} unsigned\n     * @param {number=} radix\n     * @returns {!Long}\n     * @inner\n     */\n    function fromString(str, unsigned, radix) {\n      if (str.length === 0) throw Error(\"empty string\");\n      if (typeof unsigned === \"number\") {\n        // For goog.math.long compatibility\n        radix = unsigned;\n        unsigned = false;\n      } else {\n        unsigned = !!unsigned;\n      }\n      if (\n        str === \"NaN\" ||\n        str === \"Infinity\" ||\n        str === \"+Infinity\" ||\n        str === \"-Infinity\"\n      )\n        return unsigned ? UZERO : ZERO;\n      radix = radix || 10;\n      if (radix < 2 || 36 < radix) throw RangeError(\"radix\");\n      var p;\n      if ((p = str.indexOf(\"-\")) > 0) throw Error(\"interior hyphen\");\n      else if (p === 0) {\n        return fromString(str.substring(1), unsigned, radix).neg();\n      }\n\n      // Do several (8) digits each time through the loop, so as to\n      // minimize the calls to the very expensive emulated div.\n      var radixToPower = fromNumber(pow_dbl(radix, 8));\n      var result = ZERO;\n      for (var i = 0; i < str.length; i += 8) {\n        var size = Math.min(8, str.length - i),\n          value = parseInt(str.substring(i, i + size), radix);\n        if (size < 8) {\n          var power = fromNumber(pow_dbl(radix, size));\n          result = result.mul(power).add(fromNumber(value));\n        } else {\n          result = result.mul(radixToPower);\n          result = result.add(fromNumber(value));\n        }\n      }\n      result.unsigned = unsigned;\n      return result;\n    }\n\n    /**\n     * Returns a Long representation of the given string, written using the specified radix.\n     * @function\n     * @param {string} str The textual representation of the Long\n     * @param {(boolean|number)=} unsigned Whether unsigned or not, defaults to signed\n     * @param {number=} radix The radix in which the text is written (2-36), defaults to 10\n     * @returns {!Long} The corresponding Long value\n     */\n    Long.fromString = fromString;\n\n    /**\n     * @function\n     * @param {!Long|number|string|!{low: number, high: number, unsigned: boolean}} val\n     * @param {boolean=} unsigned\n     * @returns {!Long}\n     * @inner\n     */\n    function fromValue(val, unsigned) {\n      if (typeof val === \"number\") return fromNumber(val, unsigned);\n      if (typeof val === \"string\") return fromString(val, unsigned);\n      // Throws for non-objects, converts non-instanceof Long:\n      return fromBits(\n        val.low,\n        val.high,\n        typeof unsigned === \"boolean\" ? unsigned : val.unsigned,\n      );\n    }\n\n    /**\n     * Converts the specified value to a Long using the appropriate from* function for its type.\n     * @function\n     * @param {!Long|number|bigint|string|!{low: number, high: number, unsigned: boolean}} val Value\n     * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n     * @returns {!Long}\n     */\n    Long.fromValue = fromValue;\n\n    // NOTE: the compiler should inline these constant values below and then remove these variables, so there should be\n    // no runtime penalty for these.\n\n    /**\n     * @type {number}\n     * @const\n     * @inner\n     */\n    var TWO_PWR_16_DBL = 1 << 16;\n\n    /**\n     * @type {number}\n     * @const\n     * @inner\n     */\n    var TWO_PWR_24_DBL = 1 << 24;\n\n    /**\n     * @type {number}\n     * @const\n     * @inner\n     */\n    var TWO_PWR_32_DBL = TWO_PWR_16_DBL * TWO_PWR_16_DBL;\n\n    /**\n     * @type {number}\n     * @const\n     * @inner\n     */\n    var TWO_PWR_64_DBL = TWO_PWR_32_DBL * TWO_PWR_32_DBL;\n\n    /**\n     * @type {number}\n     * @const\n     * @inner\n     */\n    var TWO_PWR_63_DBL = TWO_PWR_64_DBL / 2;\n\n    /**\n     * @type {!Long}\n     * @const\n     * @inner\n     */\n    var TWO_PWR_24 = fromInt(TWO_PWR_24_DBL);\n\n    /**\n     * @type {!Long}\n     * @inner\n     */\n    var ZERO = fromInt(0);\n\n    /**\n     * Signed zero.\n     * @type {!Long}\n     */\n    Long.ZERO = ZERO;\n\n    /**\n     * @type {!Long}\n     * @inner\n     */\n    var UZERO = fromInt(0, true);\n\n    /**\n     * Unsigned zero.\n     * @type {!Long}\n     */\n    Long.UZERO = UZERO;\n\n    /**\n     * @type {!Long}\n     * @inner\n     */\n    var ONE = fromInt(1);\n\n    /**\n     * Signed one.\n     * @type {!Long}\n     */\n    Long.ONE = ONE;\n\n    /**\n     * @type {!Long}\n     * @inner\n     */\n    var UONE = fromInt(1, true);\n\n    /**\n     * Unsigned one.\n     * @type {!Long}\n     */\n    Long.UONE = UONE;\n\n    /**\n     * @type {!Long}\n     * @inner\n     */\n    var NEG_ONE = fromInt(-1);\n\n    /**\n     * Signed negative one.\n     * @type {!Long}\n     */\n    Long.NEG_ONE = NEG_ONE;\n\n    /**\n     * @type {!Long}\n     * @inner\n     */\n    var MAX_VALUE = fromBits(0xffffffff | 0, 0x7fffffff | 0, false);\n\n    /**\n     * Maximum signed value.\n     * @type {!Long}\n     */\n    Long.MAX_VALUE = MAX_VALUE;\n\n    /**\n     * @type {!Long}\n     * @inner\n     */\n    var MAX_UNSIGNED_VALUE = fromBits(0xffffffff | 0, 0xffffffff | 0, true);\n\n    /**\n     * Maximum unsigned value.\n     * @type {!Long}\n     */\n    Long.MAX_UNSIGNED_VALUE = MAX_UNSIGNED_VALUE;\n\n    /**\n     * @type {!Long}\n     * @inner\n     */\n    var MIN_VALUE = fromBits(0, 0x80000000 | 0, false);\n\n    /**\n     * Minimum signed value.\n     * @type {!Long}\n     */\n    Long.MIN_VALUE = MIN_VALUE;\n\n    /**\n     * @alias Long.prototype\n     * @inner\n     */\n    var LongPrototype = Long.prototype;\n\n    /**\n     * Converts the Long to a 32 bit integer, assuming it is a 32 bit integer.\n     * @this {!Long}\n     * @returns {number}\n     */\n    LongPrototype.toInt = function toInt() {\n      return this.unsigned ? this.low >>> 0 : this.low;\n    };\n\n    /**\n     * Converts the Long to a the nearest floating-point representation of this value (double, 53 bit mantissa).\n     * @this {!Long}\n     * @returns {number}\n     */\n    LongPrototype.toNumber = function toNumber() {\n      if (this.unsigned)\n        return (this.high >>> 0) * TWO_PWR_32_DBL + (this.low >>> 0);\n      return this.high * TWO_PWR_32_DBL + (this.low >>> 0);\n    };\n\n    /**\n     * Converts the Long to a string written in the specified radix.\n     * @this {!Long}\n     * @param {number=} radix Radix (2-36), defaults to 10\n     * @returns {string}\n     * @override\n     * @throws {RangeError} If `radix` is out of range\n     */\n    LongPrototype.toString = function toString(radix) {\n      radix = radix || 10;\n      if (radix < 2 || 36 < radix) throw RangeError(\"radix\");\n      if (this.isZero()) return \"0\";\n      if (this.isNegative()) {\n        // Unsigned Longs are never negative\n        if (this.eq(MIN_VALUE)) {\n          // We need to change the Long value before it can be negated, so we remove\n          // the bottom-most digit in this base and then recurse to do the rest.\n          var radixLong = fromNumber(radix),\n            div = this.div(radixLong),\n            rem1 = div.mul(radixLong).sub(this);\n          return div.toString(radix) + rem1.toInt().toString(radix);\n        } else return \"-\" + this.neg().toString(radix);\n      }\n\n      // Do several (6) digits each time through the loop, so as to\n      // minimize the calls to the very expensive emulated div.\n      var radixToPower = fromNumber(pow_dbl(radix, 6), this.unsigned),\n        rem = this;\n      var result = \"\";\n      while (true) {\n        var remDiv = rem.div(radixToPower),\n          intval = rem.sub(remDiv.mul(radixToPower)).toInt() >>> 0,\n          digits = intval.toString(radix);\n        rem = remDiv;\n        if (rem.isZero()) return digits + result;\n        else {\n          while (digits.length < 6) digits = \"0\" + digits;\n          result = \"\" + digits + result;\n        }\n      }\n    };\n\n    /**\n     * Gets the high 32 bits as a signed integer.\n     * @this {!Long}\n     * @returns {number} Signed high bits\n     */\n    LongPrototype.getHighBits = function getHighBits() {\n      return this.high;\n    };\n\n    /**\n     * Gets the high 32 bits as an unsigned integer.\n     * @this {!Long}\n     * @returns {number} Unsigned high bits\n     */\n    LongPrototype.getHighBitsUnsigned = function getHighBitsUnsigned() {\n      return this.high >>> 0;\n    };\n\n    /**\n     * Gets the low 32 bits as a signed integer.\n     * @this {!Long}\n     * @returns {number} Signed low bits\n     */\n    LongPrototype.getLowBits = function getLowBits() {\n      return this.low;\n    };\n\n    /**\n     * Gets the low 32 bits as an unsigned integer.\n     * @this {!Long}\n     * @returns {number} Unsigned low bits\n     */\n    LongPrototype.getLowBitsUnsigned = function getLowBitsUnsigned() {\n      return this.low >>> 0;\n    };\n\n    /**\n     * Gets the number of bits needed to represent the absolute value of this Long.\n     * @this {!Long}\n     * @returns {number}\n     */\n    LongPrototype.getNumBitsAbs = function getNumBitsAbs() {\n      if (this.isNegative())\n        // Unsigned Longs are never negative\n        return this.eq(MIN_VALUE) ? 64 : this.neg().getNumBitsAbs();\n      var val = this.high != 0 ? this.high : this.low;\n      for (var bit = 31; bit > 0; bit--) if ((val & (1 << bit)) != 0) break;\n      return this.high != 0 ? bit + 33 : bit + 1;\n    };\n\n    /**\n     * Tests if this Long can be safely represented as a JavaScript number.\n     * @this {!Long}\n     * @returns {boolean}\n     */\n    LongPrototype.isSafeInteger = function isSafeInteger() {\n      // 2^53-1 is the maximum safe value\n      var top11Bits = this.high >> 21;\n      // [0, 2^53-1]\n      if (!top11Bits) return true;\n      // > 2^53-1\n      if (this.unsigned) return false;\n      // [-2^53, -1] except -2^53\n      return top11Bits === -1 && !(this.low === 0 && this.high === -0x200000);\n    };\n\n    /**\n     * Tests if this Long's value equals zero.\n     * @this {!Long}\n     * @returns {boolean}\n     */\n    LongPrototype.isZero = function isZero() {\n      return this.high === 0 && this.low === 0;\n    };\n\n    /**\n     * Tests if this Long's value equals zero. This is an alias of {@link Long#isZero}.\n     * @returns {boolean}\n     */\n    LongPrototype.eqz = LongPrototype.isZero;\n\n    /**\n     * Tests if this Long's value is negative.\n     * @this {!Long}\n     * @returns {boolean}\n     */\n    LongPrototype.isNegative = function isNegative() {\n      return !this.unsigned && this.high < 0;\n    };\n\n    /**\n     * Tests if this Long's value is positive or zero.\n     * @this {!Long}\n     * @returns {boolean}\n     */\n    LongPrototype.isPositive = function isPositive() {\n      return this.unsigned || this.high >= 0;\n    };\n\n    /**\n     * Tests if this Long's value is odd.\n     * @this {!Long}\n     * @returns {boolean}\n     */\n    LongPrototype.isOdd = function isOdd() {\n      return (this.low & 1) === 1;\n    };\n\n    /**\n     * Tests if this Long's value is even.\n     * @this {!Long}\n     * @returns {boolean}\n     */\n    LongPrototype.isEven = function isEven() {\n      return (this.low & 1) === 0;\n    };\n\n    /**\n     * Tests if this Long's value equals the specified's.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.equals = function equals(other) {\n      if (!isLong(other)) other = fromValue(other);\n      if (\n        this.unsigned !== other.unsigned &&\n        this.high >>> 31 === 1 &&\n        other.high >>> 31 === 1\n      )\n        return false;\n      return this.high === other.high && this.low === other.low;\n    };\n\n    /**\n     * Tests if this Long's value equals the specified's. This is an alias of {@link Long#equals}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.eq = LongPrototype.equals;\n\n    /**\n     * Tests if this Long's value differs from the specified's.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.notEquals = function notEquals(other) {\n      return !this.eq(/* validates */ other);\n    };\n\n    /**\n     * Tests if this Long's value differs from the specified's. This is an alias of {@link Long#notEquals}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.neq = LongPrototype.notEquals;\n\n    /**\n     * Tests if this Long's value differs from the specified's. This is an alias of {@link Long#notEquals}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.ne = LongPrototype.notEquals;\n\n    /**\n     * Tests if this Long's value is less than the specified's.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.lessThan = function lessThan(other) {\n      return this.comp(/* validates */ other) < 0;\n    };\n\n    /**\n     * Tests if this Long's value is less than the specified's. This is an alias of {@link Long#lessThan}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.lt = LongPrototype.lessThan;\n\n    /**\n     * Tests if this Long's value is less than or equal the specified's.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.lessThanOrEqual = function lessThanOrEqual(other) {\n      return this.comp(/* validates */ other) <= 0;\n    };\n\n    /**\n     * Tests if this Long's value is less than or equal the specified's. This is an alias of {@link Long#lessThanOrEqual}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.lte = LongPrototype.lessThanOrEqual;\n\n    /**\n     * Tests if this Long's value is less than or equal the specified's. This is an alias of {@link Long#lessThanOrEqual}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.le = LongPrototype.lessThanOrEqual;\n\n    /**\n     * Tests if this Long's value is greater than the specified's.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.greaterThan = function greaterThan(other) {\n      return this.comp(/* validates */ other) > 0;\n    };\n\n    /**\n     * Tests if this Long's value is greater than the specified's. This is an alias of {@link Long#greaterThan}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.gt = LongPrototype.greaterThan;\n\n    /**\n     * Tests if this Long's value is greater than or equal the specified's.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.greaterThanOrEqual = function greaterThanOrEqual(other) {\n      return this.comp(/* validates */ other) >= 0;\n    };\n\n    /**\n     * Tests if this Long's value is greater than or equal the specified's. This is an alias of {@link Long#greaterThanOrEqual}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.gte = LongPrototype.greaterThanOrEqual;\n\n    /**\n     * Tests if this Long's value is greater than or equal the specified's. This is an alias of {@link Long#greaterThanOrEqual}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.ge = LongPrototype.greaterThanOrEqual;\n\n    /**\n     * Compares this Long's value with the specified's.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {number} 0 if they are the same, 1 if the this is greater and -1\n     *  if the given one is greater\n     */\n    LongPrototype.compare = function compare(other) {\n      if (!isLong(other)) other = fromValue(other);\n      if (this.eq(other)) return 0;\n      var thisNeg = this.isNegative(),\n        otherNeg = other.isNegative();\n      if (thisNeg && !otherNeg) return -1;\n      if (!thisNeg && otherNeg) return 1;\n      // At this point the sign bits are the same\n      if (!this.unsigned) return this.sub(other).isNegative() ? -1 : 1;\n      // Both are positive if at least one is unsigned\n      return other.high >>> 0 > this.high >>> 0 ||\n        (other.high === this.high && other.low >>> 0 > this.low >>> 0)\n        ? -1\n        : 1;\n    };\n\n    /**\n     * Compares this Long's value with the specified's. This is an alias of {@link Long#compare}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {number} 0 if they are the same, 1 if the this is greater and -1\n     *  if the given one is greater\n     */\n    LongPrototype.comp = LongPrototype.compare;\n\n    /**\n     * Negates this Long's value.\n     * @this {!Long}\n     * @returns {!Long} Negated Long\n     */\n    LongPrototype.negate = function negate() {\n      if (!this.unsigned && this.eq(MIN_VALUE)) return MIN_VALUE;\n      return this.not().add(ONE);\n    };\n\n    /**\n     * Negates this Long's value. This is an alias of {@link Long#negate}.\n     * @function\n     * @returns {!Long} Negated Long\n     */\n    LongPrototype.neg = LongPrototype.negate;\n\n    /**\n     * Returns the sum of this and the specified Long.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} addend Addend\n     * @returns {!Long} Sum\n     */\n    LongPrototype.add = function add(addend) {\n      if (!isLong(addend)) addend = fromValue(addend);\n\n      // Divide each number into 4 chunks of 16 bits, and then sum the chunks.\n\n      var a48 = this.high >>> 16;\n      var a32 = this.high & 0xffff;\n      var a16 = this.low >>> 16;\n      var a00 = this.low & 0xffff;\n      var b48 = addend.high >>> 16;\n      var b32 = addend.high & 0xffff;\n      var b16 = addend.low >>> 16;\n      var b00 = addend.low & 0xffff;\n      var c48 = 0,\n        c32 = 0,\n        c16 = 0,\n        c00 = 0;\n      c00 += a00 + b00;\n      c16 += c00 >>> 16;\n      c00 &= 0xffff;\n      c16 += a16 + b16;\n      c32 += c16 >>> 16;\n      c16 &= 0xffff;\n      c32 += a32 + b32;\n      c48 += c32 >>> 16;\n      c32 &= 0xffff;\n      c48 += a48 + b48;\n      c48 &= 0xffff;\n      return fromBits((c16 << 16) | c00, (c48 << 16) | c32, this.unsigned);\n    };\n\n    /**\n     * Returns the difference of this and the specified Long.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} subtrahend Subtrahend\n     * @returns {!Long} Difference\n     */\n    LongPrototype.subtract = function subtract(subtrahend) {\n      if (!isLong(subtrahend)) subtrahend = fromValue(subtrahend);\n      return this.add(subtrahend.neg());\n    };\n\n    /**\n     * Returns the difference of this and the specified Long. This is an alias of {@link Long#subtract}.\n     * @function\n     * @param {!Long|number|bigint|string} subtrahend Subtrahend\n     * @returns {!Long} Difference\n     */\n    LongPrototype.sub = LongPrototype.subtract;\n\n    /**\n     * Returns the product of this and the specified Long.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} multiplier Multiplier\n     * @returns {!Long} Product\n     */\n    LongPrototype.multiply = function multiply(multiplier) {\n      if (this.isZero()) return this;\n      if (!isLong(multiplier)) multiplier = fromValue(multiplier);\n\n      // use wasm support if present\n      if (wasm) {\n        var low = wasm[\"mul\"](\n          this.low,\n          this.high,\n          multiplier.low,\n          multiplier.high,\n        );\n        return fromBits(low, wasm[\"get_high\"](), this.unsigned);\n      }\n      if (multiplier.isZero()) return this.unsigned ? UZERO : ZERO;\n      if (this.eq(MIN_VALUE)) return multiplier.isOdd() ? MIN_VALUE : ZERO;\n      if (multiplier.eq(MIN_VALUE)) return this.isOdd() ? MIN_VALUE : ZERO;\n      if (this.isNegative()) {\n        if (multiplier.isNegative()) return this.neg().mul(multiplier.neg());\n        else return this.neg().mul(multiplier).neg();\n      } else if (multiplier.isNegative())\n        return this.mul(multiplier.neg()).neg();\n\n      // If both longs are small, use float multiplication\n      if (this.lt(TWO_PWR_24) && multiplier.lt(TWO_PWR_24))\n        return fromNumber(\n          this.toNumber() * multiplier.toNumber(),\n          this.unsigned,\n        );\n\n      // Divide each long into 4 chunks of 16 bits, and then add up 4x4 products.\n      // We can skip products that would overflow.\n\n      var a48 = this.high >>> 16;\n      var a32 = this.high & 0xffff;\n      var a16 = this.low >>> 16;\n      var a00 = this.low & 0xffff;\n      var b48 = multiplier.high >>> 16;\n      var b32 = multiplier.high & 0xffff;\n      var b16 = multiplier.low >>> 16;\n      var b00 = multiplier.low & 0xffff;\n      var c48 = 0,\n        c32 = 0,\n        c16 = 0,\n        c00 = 0;\n      c00 += a00 * b00;\n      c16 += c00 >>> 16;\n      c00 &= 0xffff;\n      c16 += a16 * b00;\n      c32 += c16 >>> 16;\n      c16 &= 0xffff;\n      c16 += a00 * b16;\n      c32 += c16 >>> 16;\n      c16 &= 0xffff;\n      c32 += a32 * b00;\n      c48 += c32 >>> 16;\n      c32 &= 0xffff;\n      c32 += a16 * b16;\n      c48 += c32 >>> 16;\n      c32 &= 0xffff;\n      c32 += a00 * b32;\n      c48 += c32 >>> 16;\n      c32 &= 0xffff;\n      c48 += a48 * b00 + a32 * b16 + a16 * b32 + a00 * b48;\n      c48 &= 0xffff;\n      return fromBits((c16 << 16) | c00, (c48 << 16) | c32, this.unsigned);\n    };\n\n    /**\n     * Returns the product of this and the specified Long. This is an alias of {@link Long#multiply}.\n     * @function\n     * @param {!Long|number|bigint|string} multiplier Multiplier\n     * @returns {!Long} Product\n     */\n    LongPrototype.mul = LongPrototype.multiply;\n\n    /**\n     * Returns this Long divided by the specified. The result is signed if this Long is signed or\n     *  unsigned if this Long is unsigned.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} divisor Divisor\n     * @returns {!Long} Quotient\n     */\n    LongPrototype.divide = function divide(divisor) {\n      if (!isLong(divisor)) divisor = fromValue(divisor);\n      if (divisor.isZero()) throw Error(\"division by zero\");\n\n      // use wasm support if present\n      if (wasm) {\n        // guard against signed division overflow: the largest\n        // negative number / -1 would be 1 larger than the largest\n        // positive number, due to two's complement.\n        if (\n          !this.unsigned &&\n          this.high === -0x80000000 &&\n          divisor.low === -1 &&\n          divisor.high === -1\n        ) {\n          // be consistent with non-wasm code path\n          return this;\n        }\n        var low = (this.unsigned ? wasm[\"div_u\"] : wasm[\"div_s\"])(\n          this.low,\n          this.high,\n          divisor.low,\n          divisor.high,\n        );\n        return fromBits(low, wasm[\"get_high\"](), this.unsigned);\n      }\n      if (this.isZero()) return this.unsigned ? UZERO : ZERO;\n      var approx, rem, res;\n      if (!this.unsigned) {\n        // This section is only relevant for signed longs and is derived from the\n        // closure library as a whole.\n        if (this.eq(MIN_VALUE)) {\n          if (divisor.eq(ONE) || divisor.eq(NEG_ONE))\n            return MIN_VALUE; // recall that -MIN_VALUE == MIN_VALUE\n          else if (divisor.eq(MIN_VALUE)) return ONE;\n          else {\n            // At this point, we have |other| >= 2, so |this/other| < |MIN_VALUE|.\n            var halfThis = this.shr(1);\n            approx = halfThis.div(divisor).shl(1);\n            if (approx.eq(ZERO)) {\n              return divisor.isNegative() ? ONE : NEG_ONE;\n            } else {\n              rem = this.sub(divisor.mul(approx));\n              res = approx.add(rem.div(divisor));\n              return res;\n            }\n          }\n        } else if (divisor.eq(MIN_VALUE)) return this.unsigned ? UZERO : ZERO;\n        if (this.isNegative()) {\n          if (divisor.isNegative()) return this.neg().div(divisor.neg());\n          return this.neg().div(divisor).neg();\n        } else if (divisor.isNegative()) return this.div(divisor.neg()).neg();\n        res = ZERO;\n      } else {\n        // The algorithm below has not been made for unsigned longs. It's therefore\n        // required to take special care of the MSB prior to running it.\n        if (!divisor.unsigned) divisor = divisor.toUnsigned();\n        if (divisor.gt(this)) return UZERO;\n        if (divisor.gt(this.shru(1)))\n          // 15 >>> 1 = 7 ; with divisor = 8 ; true\n          return UONE;\n        res = UZERO;\n      }\n\n      // Repeat the following until the remainder is less than other:  find a\n      // floating-point that approximates remainder / other *from below*, add this\n      // into the result, and subtract it from the remainder.  It is critical that\n      // the approximate value is less than or equal to the real value so that the\n      // remainder never becomes negative.\n      rem = this;\n      while (rem.gte(divisor)) {\n        // Approximate the result of division. This may be a little greater or\n        // smaller than the actual value.\n        approx = Math.max(1, Math.floor(rem.toNumber() / divisor.toNumber()));\n\n        // We will tweak the approximate result by changing it in the 48-th digit or\n        // the smallest non-fractional digit, whichever is larger.\n        var log2 = Math.ceil(Math.log(approx) / Math.LN2),\n          delta = log2 <= 48 ? 1 : pow_dbl(2, log2 - 48),\n          // Decrease the approximation until it is smaller than the remainder.  Note\n          // that if it is too large, the product overflows and is negative.\n          approxRes = fromNumber(approx),\n          approxRem = approxRes.mul(divisor);\n        while (approxRem.isNegative() || approxRem.gt(rem)) {\n          approx -= delta;\n          approxRes = fromNumber(approx, this.unsigned);\n          approxRem = approxRes.mul(divisor);\n        }\n\n        // We know the answer can't be zero... and actually, zero would cause\n        // infinite recursion since we would make no progress.\n        if (approxRes.isZero()) approxRes = ONE;\n        res = res.add(approxRes);\n        rem = rem.sub(approxRem);\n      }\n      return res;\n    };\n\n    /**\n     * Returns this Long divided by the specified. This is an alias of {@link Long#divide}.\n     * @function\n     * @param {!Long|number|bigint|string} divisor Divisor\n     * @returns {!Long} Quotient\n     */\n    LongPrototype.div = LongPrototype.divide;\n\n    /**\n     * Returns this Long modulo the specified.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} divisor Divisor\n     * @returns {!Long} Remainder\n     */\n    LongPrototype.modulo = function modulo(divisor) {\n      if (!isLong(divisor)) divisor = fromValue(divisor);\n\n      // use wasm support if present\n      if (wasm) {\n        var low = (this.unsigned ? wasm[\"rem_u\"] : wasm[\"rem_s\"])(\n          this.low,\n          this.high,\n          divisor.low,\n          divisor.high,\n        );\n        return fromBits(low, wasm[\"get_high\"](), this.unsigned);\n      }\n      return this.sub(this.div(divisor).mul(divisor));\n    };\n\n    /**\n     * Returns this Long modulo the specified. This is an alias of {@link Long#modulo}.\n     * @function\n     * @param {!Long|number|bigint|string} divisor Divisor\n     * @returns {!Long} Remainder\n     */\n    LongPrototype.mod = LongPrototype.modulo;\n\n    /**\n     * Returns this Long modulo the specified. This is an alias of {@link Long#modulo}.\n     * @function\n     * @param {!Long|number|bigint|string} divisor Divisor\n     * @returns {!Long} Remainder\n     */\n    LongPrototype.rem = LongPrototype.modulo;\n\n    /**\n     * Returns the bitwise NOT of this Long.\n     * @this {!Long}\n     * @returns {!Long}\n     */\n    LongPrototype.not = function not() {\n      return fromBits(~this.low, ~this.high, this.unsigned);\n    };\n\n    /**\n     * Returns count leading zeros of this Long.\n     * @this {!Long}\n     * @returns {!number}\n     */\n    LongPrototype.countLeadingZeros = function countLeadingZeros() {\n      return this.high ? Math.clz32(this.high) : Math.clz32(this.low) + 32;\n    };\n\n    /**\n     * Returns count leading zeros. This is an alias of {@link Long#countLeadingZeros}.\n     * @function\n     * @param {!Long}\n     * @returns {!number}\n     */\n    LongPrototype.clz = LongPrototype.countLeadingZeros;\n\n    /**\n     * Returns count trailing zeros of this Long.\n     * @this {!Long}\n     * @returns {!number}\n     */\n    LongPrototype.countTrailingZeros = function countTrailingZeros() {\n      return this.low ? ctz32(this.low) : ctz32(this.high) + 32;\n    };\n\n    /**\n     * Returns count trailing zeros. This is an alias of {@link Long#countTrailingZeros}.\n     * @function\n     * @param {!Long}\n     * @returns {!number}\n     */\n    LongPrototype.ctz = LongPrototype.countTrailingZeros;\n\n    /**\n     * Returns the bitwise AND of this Long and the specified.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other Long\n     * @returns {!Long}\n     */\n    LongPrototype.and = function and(other) {\n      if (!isLong(other)) other = fromValue(other);\n      return fromBits(\n        this.low & other.low,\n        this.high & other.high,\n        this.unsigned,\n      );\n    };\n\n    /**\n     * Returns the bitwise OR of this Long and the specified.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other Long\n     * @returns {!Long}\n     */\n    LongPrototype.or = function or(other) {\n      if (!isLong(other)) other = fromValue(other);\n      return fromBits(\n        this.low | other.low,\n        this.high | other.high,\n        this.unsigned,\n      );\n    };\n\n    /**\n     * Returns the bitwise XOR of this Long and the given one.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other Long\n     * @returns {!Long}\n     */\n    LongPrototype.xor = function xor(other) {\n      if (!isLong(other)) other = fromValue(other);\n      return fromBits(\n        this.low ^ other.low,\n        this.high ^ other.high,\n        this.unsigned,\n      );\n    };\n\n    /**\n     * Returns this Long with bits shifted to the left by the given amount.\n     * @this {!Long}\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Shifted Long\n     */\n    LongPrototype.shiftLeft = function shiftLeft(numBits) {\n      if (isLong(numBits)) numBits = numBits.toInt();\n      if ((numBits &= 63) === 0) return this;\n      else if (numBits < 32)\n        return fromBits(\n          this.low << numBits,\n          (this.high << numBits) | (this.low >>> (32 - numBits)),\n          this.unsigned,\n        );\n      else return fromBits(0, this.low << (numBits - 32), this.unsigned);\n    };\n\n    /**\n     * Returns this Long with bits shifted to the left by the given amount. This is an alias of {@link Long#shiftLeft}.\n     * @function\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Shifted Long\n     */\n    LongPrototype.shl = LongPrototype.shiftLeft;\n\n    /**\n     * Returns this Long with bits arithmetically shifted to the right by the given amount.\n     * @this {!Long}\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Shifted Long\n     */\n    LongPrototype.shiftRight = function shiftRight(numBits) {\n      if (isLong(numBits)) numBits = numBits.toInt();\n      if ((numBits &= 63) === 0) return this;\n      else if (numBits < 32)\n        return fromBits(\n          (this.low >>> numBits) | (this.high << (32 - numBits)),\n          this.high >> numBits,\n          this.unsigned,\n        );\n      else\n        return fromBits(\n          this.high >> (numBits - 32),\n          this.high >= 0 ? 0 : -1,\n          this.unsigned,\n        );\n    };\n\n    /**\n     * Returns this Long with bits arithmetically shifted to the right by the given amount. This is an alias of {@link Long#shiftRight}.\n     * @function\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Shifted Long\n     */\n    LongPrototype.shr = LongPrototype.shiftRight;\n\n    /**\n     * Returns this Long with bits logically shifted to the right by the given amount.\n     * @this {!Long}\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Shifted Long\n     */\n    LongPrototype.shiftRightUnsigned = function shiftRightUnsigned(numBits) {\n      if (isLong(numBits)) numBits = numBits.toInt();\n      if ((numBits &= 63) === 0) return this;\n      if (numBits < 32)\n        return fromBits(\n          (this.low >>> numBits) | (this.high << (32 - numBits)),\n          this.high >>> numBits,\n          this.unsigned,\n        );\n      if (numBits === 32) return fromBits(this.high, 0, this.unsigned);\n      return fromBits(this.high >>> (numBits - 32), 0, this.unsigned);\n    };\n\n    /**\n     * Returns this Long with bits logically shifted to the right by the given amount. This is an alias of {@link Long#shiftRightUnsigned}.\n     * @function\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Shifted Long\n     */\n    LongPrototype.shru = LongPrototype.shiftRightUnsigned;\n\n    /**\n     * Returns this Long with bits logically shifted to the right by the given amount. This is an alias of {@link Long#shiftRightUnsigned}.\n     * @function\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Shifted Long\n     */\n    LongPrototype.shr_u = LongPrototype.shiftRightUnsigned;\n\n    /**\n     * Returns this Long with bits rotated to the left by the given amount.\n     * @this {!Long}\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Rotated Long\n     */\n    LongPrototype.rotateLeft = function rotateLeft(numBits) {\n      var b;\n      if (isLong(numBits)) numBits = numBits.toInt();\n      if ((numBits &= 63) === 0) return this;\n      if (numBits === 32) return fromBits(this.high, this.low, this.unsigned);\n      if (numBits < 32) {\n        b = 32 - numBits;\n        return fromBits(\n          (this.low << numBits) | (this.high >>> b),\n          (this.high << numBits) | (this.low >>> b),\n          this.unsigned,\n        );\n      }\n      numBits -= 32;\n      b = 32 - numBits;\n      return fromBits(\n        (this.high << numBits) | (this.low >>> b),\n        (this.low << numBits) | (this.high >>> b),\n        this.unsigned,\n      );\n    };\n    /**\n     * Returns this Long with bits rotated to the left by the given amount. This is an alias of {@link Long#rotateLeft}.\n     * @function\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Rotated Long\n     */\n    LongPrototype.rotl = LongPrototype.rotateLeft;\n\n    /**\n     * Returns this Long with bits rotated to the right by the given amount.\n     * @this {!Long}\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Rotated Long\n     */\n    LongPrototype.rotateRight = function rotateRight(numBits) {\n      var b;\n      if (isLong(numBits)) numBits = numBits.toInt();\n      if ((numBits &= 63) === 0) return this;\n      if (numBits === 32) return fromBits(this.high, this.low, this.unsigned);\n      if (numBits < 32) {\n        b = 32 - numBits;\n        return fromBits(\n          (this.high << b) | (this.low >>> numBits),\n          (this.low << b) | (this.high >>> numBits),\n          this.unsigned,\n        );\n      }\n      numBits -= 32;\n      b = 32 - numBits;\n      return fromBits(\n        (this.low << b) | (this.high >>> numBits),\n        (this.high << b) | (this.low >>> numBits),\n        this.unsigned,\n      );\n    };\n    /**\n     * Returns this Long with bits rotated to the right by the given amount. This is an alias of {@link Long#rotateRight}.\n     * @function\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Rotated Long\n     */\n    LongPrototype.rotr = LongPrototype.rotateRight;\n\n    /**\n     * Converts this Long to signed.\n     * @this {!Long}\n     * @returns {!Long} Signed long\n     */\n    LongPrototype.toSigned = function toSigned() {\n      if (!this.unsigned) return this;\n      return fromBits(this.low, this.high, false);\n    };\n\n    /**\n     * Converts this Long to unsigned.\n     * @this {!Long}\n     * @returns {!Long} Unsigned long\n     */\n    LongPrototype.toUnsigned = function toUnsigned() {\n      if (this.unsigned) return this;\n      return fromBits(this.low, this.high, true);\n    };\n\n    /**\n     * Converts this Long to its byte representation.\n     * @param {boolean=} le Whether little or big endian, defaults to big endian\n     * @this {!Long}\n     * @returns {!Array.<number>} Byte representation\n     */\n    LongPrototype.toBytes = function toBytes(le) {\n      return le ? this.toBytesLE() : this.toBytesBE();\n    };\n\n    /**\n     * Converts this Long to its little endian byte representation.\n     * @this {!Long}\n     * @returns {!Array.<number>} Little endian byte representation\n     */\n    LongPrototype.toBytesLE = function toBytesLE() {\n      var hi = this.high,\n        lo = this.low;\n      return [\n        lo & 0xff,\n        (lo >>> 8) & 0xff,\n        (lo >>> 16) & 0xff,\n        lo >>> 24,\n        hi & 0xff,\n        (hi >>> 8) & 0xff,\n        (hi >>> 16) & 0xff,\n        hi >>> 24,\n      ];\n    };\n\n    /**\n     * Converts this Long to its big endian byte representation.\n     * @this {!Long}\n     * @returns {!Array.<number>} Big endian byte representation\n     */\n    LongPrototype.toBytesBE = function toBytesBE() {\n      var hi = this.high,\n        lo = this.low;\n      return [\n        hi >>> 24,\n        (hi >>> 16) & 0xff,\n        (hi >>> 8) & 0xff,\n        hi & 0xff,\n        lo >>> 24,\n        (lo >>> 16) & 0xff,\n        (lo >>> 8) & 0xff,\n        lo & 0xff,\n      ];\n    };\n\n    /**\n     * Creates a Long from its byte representation.\n     * @param {!Array.<number>} bytes Byte representation\n     * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n     * @param {boolean=} le Whether little or big endian, defaults to big endian\n     * @returns {Long} The corresponding Long value\n     */\n    Long.fromBytes = function fromBytes(bytes, unsigned, le) {\n      return le\n        ? Long.fromBytesLE(bytes, unsigned)\n        : Long.fromBytesBE(bytes, unsigned);\n    };\n\n    /**\n     * Creates a Long from its little endian byte representation.\n     * @param {!Array.<number>} bytes Little endian byte representation\n     * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n     * @returns {Long} The corresponding Long value\n     */\n    Long.fromBytesLE = function fromBytesLE(bytes, unsigned) {\n      return new Long(\n        bytes[0] | (bytes[1] << 8) | (bytes[2] << 16) | (bytes[3] << 24),\n        bytes[4] | (bytes[5] << 8) | (bytes[6] << 16) | (bytes[7] << 24),\n        unsigned,\n      );\n    };\n\n    /**\n     * Creates a Long from its big endian byte representation.\n     * @param {!Array.<number>} bytes Big endian byte representation\n     * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n     * @returns {Long} The corresponding Long value\n     */\n    Long.fromBytesBE = function fromBytesBE(bytes, unsigned) {\n      return new Long(\n        (bytes[4] << 24) | (bytes[5] << 16) | (bytes[6] << 8) | bytes[7],\n        (bytes[0] << 24) | (bytes[1] << 16) | (bytes[2] << 8) | bytes[3],\n        unsigned,\n      );\n    };\n\n    // Support conversion to/from BigInt where available\n    if (typeof BigInt === \"function\") {\n      /**\n       * Returns a Long representing the given big integer.\n       * @function\n       * @param {number} value The big integer value\n       * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n       * @returns {!Long} The corresponding Long value\n       */\n      Long.fromBigInt = function fromBigInt(value, unsigned) {\n        var lowBits = Number(BigInt.asIntN(32, value));\n        var highBits = Number(BigInt.asIntN(32, value >> BigInt(32)));\n        return fromBits(lowBits, highBits, unsigned);\n      };\n\n      // Override\n      Long.fromValue = function fromValueWithBigInt(value, unsigned) {\n        if (typeof value === \"bigint\") return Long.fromBigInt(value, unsigned);\n        return fromValue(value, unsigned);\n      };\n\n      /**\n       * Converts the Long to its big integer representation.\n       * @this {!Long}\n       * @returns {bigint}\n       */\n      LongPrototype.toBigInt = function toBigInt() {\n        var lowBigInt = BigInt(this.low >>> 0);\n        var highBigInt = BigInt(this.unsigned ? this.high >>> 0 : this.high);\n        return (highBigInt << BigInt(32)) | lowBigInt;\n      };\n    }\n    var _default = (_exports.default = Long);\n  },\n);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/long/umd/index.js\n");

/***/ })

};
;