import { doc, setDoc, collection, addDoc } from 'firebase/firestore';
import { createUserWithEmailAndPassword } from 'firebase/auth';
import { auth, db } from '@/lib/firebase';
import { UserProfile } from '@/contexts/AuthContext';
import { Patient, Appointment, Treatment, InventoryItem } from '@/types';

/**
 * Setup demo data for testing
 */
export async function setupDemoData() {
  try {
    console.log('Setting up demo data...');

    // Create demo clinic
    const clinicId = 'demo-clinic-001';
    await setDoc(doc(db, 'clinics', clinicId), {
      name: 'Klinik Gigi DentalCare Demo',
      address: 'Jl. Sudirman No. 123, Jakarta Pusat',
      phone: '(021) 1234-5678',
      email: '<EMAIL>',
      createdAt: new Date().toISOString()
    });

    // Create demo users
    const demoUsers = [
      {
        email: '<EMAIL>',
        password: 'demo123',
        profile: {
          name: 'Dr. <PERSON>',
          role: 'doctor' as const,
          clinicId,
          permissions: ['read_patients', 'write_patients', 'manage_treatments', 'read_appointments', 'write_appointments']
        }
      },
      {
        email: '<EMAIL>',
        password: 'demo123',
        profile: {
          name: 'Siti Nurhaliza',
          role: 'receptionist' as const,
          clinicId,
          permissions: ['read_patients', 'write_patients', 'manage_appointments', 'manage_billing']
        }
      },
      {
        email: '<EMAIL>',
        password: 'demo123',
        profile: {
          name: 'Ahmad Rahman',
          role: 'admin' as const,
          clinicId,
          permissions: ['full_access']
        }
      }
    ];

    for (const user of demoUsers) {
      try {
        const userCredential = await createUserWithEmailAndPassword(auth, user.email, user.password);
        const userProfile: UserProfile = {
          id: userCredential.user.uid,
          email: user.email,
          ...user.profile,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        await setDoc(doc(db, 'users', userCredential.user.uid), userProfile);
        console.log(`Created user: ${user.email}`);
      } catch (error: any) {
        if (error.code === 'auth/email-already-in-use') {
          console.log(`User ${user.email} already exists`);
        } else {
          console.error(`Error creating user ${user.email}:`, error);
        }
      }
    }

    // Create demo patients
    const demoPatients: Omit<Patient, 'id'>[] = [
      {
        medicalRecordNumber: 'RM2024001',
        name: 'Budi Santoso',
        email: '<EMAIL>',
        phone: '081234567890',
        dateOfBirth: '1985-03-15',
        address: 'Jl. Sudirman No. 123, Jakarta',
        nik: '3171234567890001',
        gender: 'male',
        emergencyContact: {
          name: 'Siti Santoso',
          phone: '081234567891',
          relationship: 'Istri'
        },
        medicalHistory: {
          allergies: ['Penisilin'],
          medications: ['Paracetamol'],
          conditions: ['Hipertensi']
        },
        clinicalImages: [],
        dentalChart: initializeDentalChart(),
        lastVisit: '2024-01-15',
        totalVisits: 5,
        status: 'active',
        clinicId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        medicalRecordNumber: 'RM2024002',
        name: 'Sari Dewi',
        email: '<EMAIL>',
        phone: '081234567892',
        dateOfBirth: '1990-07-22',
        address: 'Jl. Thamrin No. 456, Jakarta',
        nik: '3171234567890002',
        gender: 'female',
        emergencyContact: {
          name: 'Ahmad Dewi',
          phone: '081234567893',
          relationship: 'Suami'
        },
        medicalHistory: {
          allergies: [],
          medications: [],
          conditions: []
        },
        clinicalImages: [],
        dentalChart: initializeDentalChart(),
        lastVisit: '2024-01-10',
        totalVisits: 3,
        status: 'active',
        clinicId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        medicalRecordNumber: 'RM2024003',
        name: 'Andi Wijaya',
        email: '<EMAIL>',
        phone: '081234567894',
        dateOfBirth: '1988-11-08',
        address: 'Jl. Gatot Subroto No. 789, Jakarta',
        nik: '3171234567890003',
        gender: 'male',
        emergencyContact: {
          name: 'Maya Wijaya',
          phone: '081234567895',
          relationship: 'Istri'
        },
        medicalHistory: {
          allergies: ['Sulfa'],
          medications: [],
          conditions: ['Diabetes']
        },
        clinicalImages: [],
        dentalChart: initializeDentalChart(),
        lastVisit: '2024-01-08',
        totalVisits: 8,
        status: 'active',
        clinicId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];

    for (const patient of demoPatients) {
      await addDoc(collection(db, 'patients'), patient);
    }

    // Create demo treatments
    const demoTreatments: Omit<Treatment, 'id'>[] = [
      {
        code: 'CONS001',
        name: 'Konsultasi',
        category: 'Konsultasi',
        price: 150000,
        duration: 30,
        description: 'Konsultasi dan pemeriksaan gigi'
      },
      {
        code: 'SCAL001',
        name: 'Scaling',
        category: 'Preventif',
        price: 300000,
        duration: 60,
        description: 'Pembersihan karang gigi'
      },
      {
        code: 'FILL001',
        name: 'Tambal Gigi',
        category: 'Restoratif',
        price: 250000,
        duration: 45,
        description: 'Penambalan gigi dengan komposit'
      },
      {
        code: 'CROW001',
        name: 'Crown',
        category: 'Prostetik',
        price: 2500000,
        duration: 120,
        description: 'Pemasangan mahkota gigi'
      }
    ];

    for (const treatment of demoTreatments) {
      await addDoc(collection(db, 'treatments'), treatment);
    }

    // Create demo inventory
    const demoInventory: Omit<InventoryItem, 'id'>[] = [
      {
        name: 'Komposit Resin',
        category: 'Bahan Tambal',
        currentStock: 15,
        minStock: 10,
        unit: 'tube',
        price: 450000,
        supplier: 'PT Dental Supply',
        expiryDate: '2025-06-15',
        lastRestocked: '2024-01-01',
        status: 'in-stock',
        clinicId
      },
      {
        name: 'Anestesi Lidocaine',
        category: 'Obat',
        currentStock: 5,
        minStock: 20,
        unit: 'vial',
        price: 25000,
        supplier: 'PT Pharma Dental',
        expiryDate: '2024-12-31',
        lastRestocked: '2023-12-15',
        status: 'low-stock',
        clinicId
      },
      {
        name: 'Sarung Tangan Latex',
        category: 'Disposable',
        currentStock: 0,
        minStock: 50,
        unit: 'box',
        price: 85000,
        supplier: 'PT Medical Supply',
        lastRestocked: '2023-11-20',
        status: 'out-of-stock',
        clinicId
      }
    ];

    for (const item of demoInventory) {
      await addDoc(collection(db, 'inventory'), item);
    }

    console.log('Demo data setup completed!');
    return true;
  } catch (error) {
    console.error('Error setting up demo data:', error);
    return false;
  }
}

function initializeDentalChart() {
  const dentalChart = [];
  for (let i = 1; i <= 32; i++) {
    dentalChart.push({
      toothNumber: i,
      condition: 'healthy' as const,
      notes: '',
      updatedAt: new Date().toISOString()
    });
  }
  return dentalChart;
}
