import { 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  onSnapshot
} from 'firebase/firestore';
import { Appointment } from '@/types';
import { TenantService } from './base/TenantService';

export class AppointmentService extends TenantService {

  /**
   * Create new appointment
   */
  async createAppointment(appointmentData: Omit<Appointment, 'id'>): Promise<string> {
    try {
      this.validateTenantAccess();
      
      const newAppointment: Omit<Appointment, 'id'> = {
        ...appointmentData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      const docRef = await addDoc(this.getCollection('appointments'), newAppointment);
      return docRef.id;
    } catch (error) {
      this.handleError(error, 'create appointment');
    }
  }

  /**
   * Get appointment by ID
   */
  async getAppointment(id: string): Promise<Appointment | null> {
    try {
      this.validateTenantAccess();
      const docRef = this.getDocument('appointments', id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as Appointment;
      }
      return null;
    } catch (error) {
      this.handleError(error, 'get appointment');
    }
  }

  /**
   * Get all appointments for current tenant
   */
  async getAppointments(): Promise<Appointment[]> {
    try {
      this.validateTenantAccess();
      const q = query(
        this.getCollection('appointments'),
        orderBy('date', 'desc'),
        orderBy('time', 'asc')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Appointment[];
    } catch (error) {
      this.handleError(error, 'get appointments');
    }
  }

  /**
   * Get appointments by date
   */
  async getAppointmentsByDate(date: string): Promise<Appointment[]> {
    try {
      this.validateTenantAccess();
      const q = query(
        this.getCollection('appointments'),
        where('date', '==', date),
        orderBy('time', 'asc')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Appointment[];
    } catch (error) {
      this.handleError(error, 'get appointments by date');
    }
  }

  /**
   * Get appointments by patient ID
   */
  async getAppointmentsByPatient(patientId: string): Promise<Appointment[]> {
    try {
      this.validateTenantAccess();
      const q = query(
        this.getCollection('appointments'),
        where('patientId', '==', patientId),
        orderBy('date', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Appointment[];
    } catch (error) {
      this.handleError(error, 'get appointments by patient');
    }
  }

  /**
   * Get appointments by doctor ID
   */
  async getAppointmentsByDoctor(doctorId: string): Promise<Appointment[]> {
    try {
      this.validateTenantAccess();
      const q = query(
        this.getCollection('appointments'),
        where('doctorId', '==', doctorId),
        orderBy('date', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Appointment[];
    } catch (error) {
      this.handleError(error, 'get appointments by doctor');
    }
  }

  /**
   * Update appointment
   */
  async updateAppointment(id: string, updates: Partial<Appointment>): Promise<void> {
    try {
      this.validateTenantAccess();
      const docRef = this.getDocument('appointments', id);
      await updateDoc(docRef, {
        ...updates,
        updatedAt: new Date().toISOString()
      });
    } catch (error) {
      this.handleError(error, 'update appointment');
    }
  }

  /**
   * Delete appointment
   */
  async deleteAppointment(id: string): Promise<void> {
    try {
      this.validateTenantAccess();
      const docRef = this.getDocument('appointments', id);
      await deleteDoc(docRef);
    } catch (error) {
      this.handleError(error, 'delete appointment');
    }
  }

  /**
   * Update appointment status
   */
  async updateAppointmentStatus(
    id: string, 
    status: Appointment['status'],
    notes?: string
  ): Promise<void> {
    try {
      this.validateTenantAccess();
      const updates: Partial<Appointment> = {
        status,
        updatedAt: new Date().toISOString()
      };
      
      if (notes) {
        updates.notes = notes;
      }
      
      await this.updateAppointment(id, updates);
    } catch (error) {
      this.handleError(error, 'update appointment status');
    }
  }

  /**
   * Get today's appointments
   */
  async getTodayAppointments(): Promise<Appointment[]> {
    const today = new Date().toISOString().split('T')[0];
    return this.getAppointmentsByDate(today);
  }

  /**
   * Get upcoming appointments
   */
  async getUpcomingAppointments(days: number = 7): Promise<Appointment[]> {
    try {
      this.validateTenantAccess();
      const today = new Date();
      const futureDate = new Date(today.getTime() + (days * 24 * 60 * 60 * 1000));
      
      const q = query(
        this.getCollection('appointments'),
        where('date', '>=', today.toISOString().split('T')[0]),
        where('date', '<=', futureDate.toISOString().split('T')[0]),
        orderBy('date', 'asc'),
        orderBy('time', 'asc')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Appointment[];
    } catch (error) {
      this.handleError(error, 'get upcoming appointments');
    }
  }

  /**
   * Real-time subscription to appointments
   */
  subscribeToAppointments(callback: (appointments: Appointment[]) => void) {
    this.validateTenantAccess();
    const q = query(
      this.getCollection('appointments'),
      orderBy('date', 'desc'),
      orderBy('time', 'asc')
    );

    return onSnapshot(q, (snapshot) => {
      const appointments = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Appointment[];
      callback(appointments);
    }, (error) => {
      console.error('Error in appointments subscription:', error);
    });
  }

  /**
   * Subscribe to today's appointments
   */
  subscribeToTodayAppointments(callback: (appointments: Appointment[]) => void) {
    this.validateTenantAccess();
    const today = new Date().toISOString().split('T')[0];
    
    const q = query(
      this.getCollection('appointments'),
      where('date', '==', today),
      orderBy('time', 'asc')
    );

    return onSnapshot(q, (snapshot) => {
      const appointments = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Appointment[];
      callback(appointments);
    }, (error) => {
      console.error('Error in today appointments subscription:', error);
    });
  }
}
