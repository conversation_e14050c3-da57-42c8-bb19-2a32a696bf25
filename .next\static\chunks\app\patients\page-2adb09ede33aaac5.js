(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[333],{2231:function(e,t,a){Promise.resolve().then(a.bind(a,4707))},4707:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return PatientsPage}});var s=a(7437),n=a(2265),i=a(2621),r=a(6561),l=a(5255),c=a(4020),d=a(1809);function PhoneIcon({title:e,titleId:t,...a},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}))}let o=n.forwardRef(PhoneIcon);function EnvelopeIcon({title:e,titleId:t,...a},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))}let m=n.forwardRef(EnvelopeIcon);var x=a(9367),h=a(2125);function XMarkIcon({title:e,titleId:t,...a},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))}let u=n.forwardRef(XMarkIcon);function PhotoIcon({title:e,titleId:t,...a},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"}))}let g=n.forwardRef(PhotoIcon),compressImageToBase64=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:800,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:600,s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.8;return new Promise((n,i)=>{let r=document.createElement("canvas"),l=r.getContext("2d"),c=new Image;c.onload=()=>{let{width:e,height:i}=c;e>i?e>t&&(i=i*t/e,e=t):i>a&&(e=e*a/i,i=a),r.width=e,r.height=i,null==l||l.drawImage(c,0,0,e,i);let d=r.toDataURL("image/jpeg",s);n(d)},c.onerror=()=>i(Error("Failed to load image")),c.src=URL.createObjectURL(e)})},validateImageFile=e=>["image/jpeg","image/jpg","image/png","image/webp"].includes(e.type)?e.size>5242880?{valid:!1,error:"Ukuran file terlalu besar. Maksimal 5MB."}:{valid:!0}:{valid:!1,error:"Format file tidak didukung. Gunakan JPG, PNG, atau WebP."};function CreatePatientModal(e){let{onClose:t,onSubmit:a,isLoading:i}=e,[r,l]=(0,n.useState)({name:"",email:"",phone:"",dateOfBirth:"",address:"",nik:"",gender:"male",avatar:"",emergencyContact:{name:"",phone:"",relationship:""},medicalHistory:{allergies:[],medications:[],conditions:[]}}),[c,d]=(0,n.useState)(""),[o,m]=(0,n.useState)(""),[x,h]=(0,n.useState)(""),[p,f]=(0,n.useState)(""),handleInputChange=(e,t)=>{if(e.includes(".")){let[a,s]=e.split(".");l(e=>({...e,[a]:{...e[a],[s]:t}}))}else l(a=>({...a,[e]:t}))},handleImageUpload=async e=>{var t;let a=null===(t=e.target.files)||void 0===t?void 0:t[0];if(!a)return;f("");let s=validateImageFile(a);if(!s.valid){f(s.error||"Invalid file");return}try{let e=await compressImageToBase64(a,400,400,.8);l(t=>({...t,avatar:e}))}catch(e){f("Failed to process image")}},addToArray=(e,t)=>{t.trim()&&(l(a=>({...a,medicalHistory:{...a.medicalHistory,[e]:[...a.medicalHistory[e],t.trim()]}})),"allergies"===e&&d(""),"medications"===e&&m(""),"conditions"===e&&h(""))},removeFromArray=(e,t)=>{l(a=>({...a,medicalHistory:{...a.medicalHistory,[e]:a.medicalHistory[e].filter((e,a)=>a!==t)}}))};return(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,s.jsx)("div",{className:"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Tambah Pasien Baru"}),(0,s.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-gray-600",children:(0,s.jsx)(u,{className:"w-6 h-6"})})]}),(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault();let t={...r,totalVisits:0,status:"active",clinicalImages:[],dentalChart:[]};a(t)},className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:r.avatar?(0,s.jsx)("img",{src:r.avatar,alt:"Avatar",className:"w-24 h-24 rounded-full object-cover"}):(0,s.jsx)("div",{className:"w-24 h-24 rounded-full bg-gray-200 flex items-center justify-center",children:(0,s.jsx)(g,{className:"w-8 h-8 text-gray-400"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Foto Pasien"}),(0,s.jsx)("input",{type:"file",accept:"image/*",onChange:handleImageUpload,className:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"}),p&&(0,s.jsx)("p",{className:"text-red-600 text-sm mt-1",children:p})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Nama Lengkap *"}),(0,s.jsx)("input",{type:"text",required:!0,className:"input-field",value:r.name,onChange:e=>handleInputChange("name",e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),(0,s.jsx)("input",{type:"email",className:"input-field",value:r.email,onChange:e=>handleInputChange("email",e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Nomor Telepon *"}),(0,s.jsx)("input",{type:"tel",required:!0,className:"input-field",value:r.phone,onChange:e=>handleInputChange("phone",e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Tanggal Lahir *"}),(0,s.jsx)("input",{type:"date",required:!0,className:"input-field",value:r.dateOfBirth,onChange:e=>handleInputChange("dateOfBirth",e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"NIK *"}),(0,s.jsx)("input",{type:"text",required:!0,className:"input-field",value:r.nik,onChange:e=>handleInputChange("nik",e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Jenis Kelamin *"}),(0,s.jsxs)("select",{required:!0,className:"input-field",value:r.gender,onChange:e=>handleInputChange("gender",e.target.value),children:[(0,s.jsx)("option",{value:"male",children:"Laki-laki"}),(0,s.jsx)("option",{value:"female",children:"Perempuan"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Alamat *"}),(0,s.jsx)("textarea",{required:!0,rows:3,className:"input-field",value:r.address,onChange:e=>handleInputChange("address",e.target.value)})]}),(0,s.jsxs)("div",{className:"border-t pt-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Kontak Darurat"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Nama *"}),(0,s.jsx)("input",{type:"text",required:!0,className:"input-field",value:r.emergencyContact.name,onChange:e=>handleInputChange("emergencyContact.name",e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Nomor Telepon *"}),(0,s.jsx)("input",{type:"tel",required:!0,className:"input-field",value:r.emergencyContact.phone,onChange:e=>handleInputChange("emergencyContact.phone",e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Hubungan *"}),(0,s.jsx)("input",{type:"text",required:!0,className:"input-field",placeholder:"e.g., Suami, Istri, Anak",value:r.emergencyContact.relationship,onChange:e=>handleInputChange("emergencyContact.relationship",e.target.value)})]})]})]}),(0,s.jsxs)("div",{className:"border-t pt-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Riwayat Medis"}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Alergi"}),(0,s.jsxs)("div",{className:"flex space-x-2 mb-2",children:[(0,s.jsx)("input",{type:"text",className:"input-field flex-1",placeholder:"Tambah alergi",value:c,onChange:e=>d(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),addToArray("allergies",c))}),(0,s.jsx)("button",{type:"button",onClick:()=>addToArray("allergies",c),className:"btn-secondary",children:"Tambah"})]}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:r.medicalHistory.allergies.map((e,t)=>(0,s.jsxs)("span",{className:"bg-red-100 text-red-800 px-2 py-1 rounded-full text-sm flex items-center",children:[e,(0,s.jsx)("button",{type:"button",onClick:()=>removeFromArray("allergies",t),className:"ml-1 text-red-600 hover:text-red-800",children:"\xd7"})]},t))})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Obat-obatan"}),(0,s.jsxs)("div",{className:"flex space-x-2 mb-2",children:[(0,s.jsx)("input",{type:"text",className:"input-field flex-1",placeholder:"Tambah obat",value:o,onChange:e=>m(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),addToArray("medications",o))}),(0,s.jsx)("button",{type:"button",onClick:()=>addToArray("medications",o),className:"btn-secondary",children:"Tambah"})]}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:r.medicalHistory.medications.map((e,t)=>(0,s.jsxs)("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm flex items-center",children:[e,(0,s.jsx)("button",{type:"button",onClick:()=>removeFromArray("medications",t),className:"ml-1 text-blue-600 hover:text-blue-800",children:"\xd7"})]},t))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Kondisi Medis"}),(0,s.jsxs)("div",{className:"flex space-x-2 mb-2",children:[(0,s.jsx)("input",{type:"text",className:"input-field flex-1",placeholder:"Tambah kondisi medis",value:x,onChange:e=>h(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),addToArray("conditions",x))}),(0,s.jsx)("button",{type:"button",onClick:()=>addToArray("conditions",x),className:"btn-secondary",children:"Tambah"})]}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:r.medicalHistory.conditions.map((e,t)=>(0,s.jsxs)("span",{className:"bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-sm flex items-center",children:[e,(0,s.jsx)("button",{type:"button",onClick:()=>removeFromArray("conditions",t),className:"ml-1 text-yellow-600 hover:text-yellow-800",children:"\xd7"})]},t))})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t",children:[(0,s.jsx)("button",{type:"button",onClick:t,className:"btn-secondary",disabled:i,children:"Batal"}),(0,s.jsx)("button",{type:"submit",className:"btn-primary",disabled:i,children:i?"Menyimpan...":"Simpan Pasien"})]})]})]})})})}function PatientList(e){let{onSelectPatient:t}=e,[a,i]=(0,n.useState)(""),[u,g]=(0,n.useState)(!1),{data:p=[],isLoading:f,error:v}=(0,r.w5)(),j=(0,r.KA)(),y=p.filter(e=>e.name.toLowerCase().includes(a.toLowerCase())||e.medicalRecordNumber.toLowerCase().includes(a.toLowerCase())||e.phone.includes(a)||e.email.toLowerCase().includes(a.toLowerCase())),formatDate=e=>new Date(e).toLocaleDateString("id-ID"),calculateAge=e=>{let t=new Date,a=new Date(e),s=t.getFullYear()-a.getFullYear(),n=t.getMonth()-a.getMonth();return(n<0||0===n&&t.getDate()<a.getDate())&&s--,s};return f?(0,s.jsx)("div",{className:"card",children:(0,s.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded"}),(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-5/6"})]})]})}):v?(0,s.jsx)("div",{className:"card",children:(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsxs)("p",{className:"text-red-600",children:["Error loading patients: ",v.message]}),(0,s.jsx)("button",{onClick:()=>window.location.reload(),className:"btn-primary mt-4",children:"Retry"})]})}):(0,s.jsxs)("div",{className:"card",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Daftar Pasien"}),(0,s.jsxs)("button",{onClick:()=>g(!0),className:"btn-primary flex items-center",children:[(0,s.jsx)(l.Z,{className:"w-4 h-4 mr-2"}),"Tambah Pasien"]})]}),(0,s.jsxs)("div",{className:"relative mb-6",children:[(0,s.jsx)(c.Z,{className:"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,s.jsx)("input",{type:"text",placeholder:"Cari berdasarkan nama, nomor RM, atau telepon...",className:"input-field pl-10",value:a,onChange:e=>i(e.target.value)})]}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Pasien"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"No. RM"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Kontak"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Kunjungan Terakhir"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Aksi"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:y.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:(0,s.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:(0,s.jsx)(d.Z,{className:"h-6 w-6 text-gray-600"})})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:["male"===e.gender?"Laki-laki":"Perempuan",", ",calculateAge(e.dateOfBirth)," tahun"]})]})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.medicalRecordNumber}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,s.jsxs)("div",{className:"text-sm text-gray-900 flex items-center",children:[(0,s.jsx)(o,{className:"w-4 h-4 mr-1"}),e.phone]}),(0,s.jsxs)("div",{className:"text-sm text-gray-500 flex items-center",children:[(0,s.jsx)(m,{className:"w-4 h-4 mr-1"}),e.email]})]}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[e.lastVisit?formatDate(e.lastVisit):"Belum pernah",(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:[e.totalVisits," kunjungan"]})]}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"status-badge ".concat("active"===e.status?"status-active":"status-inactive"),children:"active"===e.status?"Aktif":"Tidak Aktif"})}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2",children:[(0,s.jsx)("button",{onClick:()=>t(e),className:"text-primary-600 hover:text-primary-900",children:(0,s.jsx)(x.Z,{className:"w-4 h-4"})}),(0,s.jsx)("button",{className:"text-gray-600 hover:text-gray-900",children:(0,s.jsx)(h.Z,{className:"w-4 h-4"})})]})]},e.id))})]})}),0===y.length&&(0,s.jsx)("div",{className:"text-center py-8 text-gray-500",children:a?"Tidak ada pasien yang ditemukan":"Belum ada data pasien"}),u&&(0,s.jsx)(CreatePatientModal,{onClose:()=>g(!1),onSubmit:j.mutate,isLoading:j.isPending})]})}function CalendarIcon({title:e,titleId:t,...a},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"}))}let p=n.forwardRef(CalendarIcon);function MapPinIcon({title:e,titleId:t,...a},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}),n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"}))}let f=n.forwardRef(MapPinIcon);function DocumentTextIcon({title:e,titleId:t,...a},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))}let v=n.forwardRef(DocumentTextIcon);var j=a(887);function PatientDetail(e){let{patient:t,onClose:a}=e;return(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,s.jsx)("div",{className:"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Detail Pasien"}),(0,s.jsx)("button",{onClick:a,className:"text-gray-400 hover:text-gray-600",children:"✕"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,s.jsxs)("div",{className:"card",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center",children:(0,s.jsx)(d.Z,{className:"w-8 h-8 text-gray-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:t.name}),(0,s.jsxs)("p",{className:"text-gray-600",children:["No. RM: ",t.medicalRecordNumber]}),(0,s.jsx)("span",{className:"status-badge ".concat("active"===t.status?"status-active":"status-inactive"),children:"active"===t.status?"Aktif":"Tidak Aktif"})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(p,{className:"w-5 h-5 text-gray-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Tanggal Lahir"}),(0,s.jsxs)("p",{className:"font-medium",children:[new Date(t.dateOfBirth).toLocaleDateString("id-ID",{weekday:"long",year:"numeric",month:"long",day:"numeric"})," (",(e=>{let t=new Date,a=new Date(e),s=t.getFullYear()-a.getFullYear(),n=t.getMonth()-a.getMonth();return(n<0||0===n&&t.getDate()<a.getDate())&&s--,s})(t.dateOfBirth)," tahun)"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(d.Z,{className:"w-5 h-5 text-gray-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Jenis Kelamin"}),(0,s.jsx)("p",{className:"font-medium",children:"male"===t.gender?"Laki-laki":"Perempuan"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(o,{className:"w-5 h-5 text-gray-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Telepon"}),(0,s.jsx)("p",{className:"font-medium",children:t.phone})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(m,{className:"w-5 h-5 text-gray-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Email"}),(0,s.jsx)("p",{className:"font-medium",children:t.email})]})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-2 md:col-span-2",children:[(0,s.jsx)(f,{className:"w-5 h-5 text-gray-400 mt-1"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Alamat"}),(0,s.jsx)("p",{className:"font-medium",children:t.address})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(v,{className:"w-5 h-5 text-gray-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"NIK"}),(0,s.jsx)("p",{className:"font-medium",children:t.nik})]})]})]})]}),(0,s.jsxs)("div",{className:"card",children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Riwayat Medis"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h5",{className:"font-medium text-gray-900 mb-2 flex items-center",children:[(0,s.jsx)(j.Z,{className:"w-4 h-4 text-red-500 mr-2"}),"Alergi"]}),t.medicalHistory.allergies.length>0?(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:t.medicalHistory.allergies.map((e,t)=>(0,s.jsx)("span",{className:"bg-red-100 text-red-800 px-2 py-1 rounded-full text-sm",children:e},t))}):(0,s.jsx)("p",{className:"text-gray-500 text-sm",children:"Tidak ada alergi yang diketahui"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h5",{className:"font-medium text-gray-900 mb-2",children:"Obat-obatan"}),t.medicalHistory.medications.length>0?(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:t.medicalHistory.medications.map((e,t)=>(0,s.jsx)("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm",children:e},t))}):(0,s.jsx)("p",{className:"text-gray-500 text-sm",children:"Tidak ada obat yang sedang dikonsumsi"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h5",{className:"font-medium text-gray-900 mb-2",children:"Kondisi Medis"}),t.medicalHistory.conditions.length>0?(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:t.medicalHistory.conditions.map((e,t)=>(0,s.jsx)("span",{className:"bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-sm",children:e},t))}):(0,s.jsx)("p",{className:"text-gray-500 text-sm",children:"Tidak ada kondisi medis khusus"})]})]})]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"card",children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Kontak Darurat"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"font-medium",children:t.emergencyContact.name}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:t.emergencyContact.relationship}),(0,s.jsxs)("p",{className:"text-sm text-gray-600 flex items-center",children:[(0,s.jsx)(o,{className:"w-4 h-4 mr-1"}),t.emergencyContact.phone]})]})]}),(0,s.jsxs)("div",{className:"card",children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Statistik Kunjungan"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Total Kunjungan"}),(0,s.jsx)("span",{className:"font-medium",children:t.totalVisits})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Kunjungan Terakhir"}),(0,s.jsx)("span",{className:"font-medium",children:t.lastVisit?new Date(t.lastVisit).toLocaleDateString("id-ID"):"Belum pernah"})]})]})]}),(0,s.jsxs)("div",{className:"card",children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Aksi Cepat"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("button",{className:"w-full btn-primary text-sm",children:"Buat Appointment"}),(0,s.jsx)("button",{className:"w-full btn-secondary text-sm",children:"Lihat Riwayat Treatment"}),(0,s.jsx)("button",{className:"w-full btn-secondary text-sm",children:"Edit Data Pasien"}),(0,s.jsx)("button",{className:"w-full btn-secondary text-sm",children:"Cetak Kartu Pasien"})]})]})]})]})]})})})}function PatientsPage(){let[e,t]=(0,n.useState)(null);return(0,s.jsxs)("div",{className:"flex-1 overflow-auto",children:[(0,s.jsx)(i.Z,{title:"Manajemen Pasien",subtitle:"Kelola data pasien dan rekam medis"}),(0,s.jsxs)("main",{className:"p-6",children:[(0,s.jsx)(PatientList,{onSelectPatient:t}),e&&(0,s.jsx)(PatientDetail,{patient:e,onClose:()=>t(null)})]})]})}},6561:function(e,t,a){"use strict";a.d(t,{KA:function(){return useCreatePatient},w5:function(){return usePatients}});var s=a(9891),n=a(8038),i=a(3588);a(2265);var r=a(4086),l=a(6831);let TenantService=class TenantService{getCollection(e){return(0,r.hJ)(l.db,"dentalcare",this.tenantId,e)}getDocument(e,t){return(0,r.JU)(l.db,"dentalcare",this.tenantId,e,t)}getSettingsDocument(e){return(0,r.JU)(l.db,"dentalcare",this.tenantId,"settings",e)}validateTenantAccess(){if(!this.tenantId)throw Error("No tenant ID provided")}handleError(e,t){if(console.error("TenantService Error [".concat(this.tenantId,"] - ").concat(t,":"),e),e instanceof r.WA)switch(e.code){case"permission-denied":throw Error("Access denied for tenant ".concat(this.tenantId,". Please check your permissions."));case"not-found":throw Error("Resource not found in tenant ".concat(this.tenantId,"."));case"unavailable":throw Error("Service temporarily unavailable. Please try again.");default:throw Error("Database operation failed: ".concat(e.message))}throw Error("Failed to ".concat(t,": ").concat(e.message||"Unknown error"))}getTenantId(){return this.tenantId}setTenantId(e){if(!e)throw Error("Invalid tenant ID");this.tenantId=e}constructor(e){if(!e)throw Error("TenantService requires a valid tenantId");this.tenantId=e}};let TenantService_TenantServiceRegistry=class TenantService_TenantServiceRegistry{static getService(e,t,a){this.instances.has(t)||this.instances.set(t,new Map);let s=this.instances.get(t);return s.has(a)||s.set(a,new e(t)),s.get(a)}static clearTenantServices(e){this.instances.delete(e)}static clearAllServices(){this.instances.clear()}};TenantService_TenantServiceRegistry.instances=new Map;let patients_PatientService=class patients_PatientService extends TenantService{async generateMedicalRecordNumber(){let e=new Date().getFullYear(),t=(0,r.IO)(this.getCollection("patients"),(0,r.ar)("medicalRecordNumber",">=","RM".concat(e)),(0,r.ar)("medicalRecordNumber","<","RM".concat(e+1)),(0,r.Xo)("medicalRecordNumber","desc"),(0,r.b9)(1)),a=await (0,r.PL)(t);if(a.empty)return"RM".concat(e,"001");let s=a.docs[0].data(),n=parseInt(s.medicalRecordNumber.slice(-3)),i=(n+1).toString().padStart(3,"0");return"RM".concat(e).concat(i)}async createPatient(e){try{this.validateTenantAccess();let t=await this.generateMedicalRecordNumber(),a={...e,medicalRecordNumber:t,totalVisits:0,status:"active",dentalChart:this.initializeDentalChart(),clinicalImages:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},s=await (0,r.ET)(this.getCollection("patients"),a);return s.id}catch(e){this.handleError(e,"create patient")}}initializeDentalChart(){let e=[];for(let t=1;t<=32;t++)e.push({toothNumber:t,condition:"healthy",notes:"",updatedAt:new Date().toISOString()});return e}async getPatient(e){try{this.validateTenantAccess();let t=this.getDocument("patients",e),a=await (0,r.QT)(t);if(a.exists())return{id:a.id,...a.data()};return null}catch(e){this.handleError(e,"get patient")}}async getPatients(){try{this.validateTenantAccess();let e=(0,r.IO)(this.getCollection("patients"),(0,r.Xo)("name")),t=await (0,r.PL)(e);return t.docs.map(e=>({id:e.id,...e.data()}))}catch(e){this.handleError(e,"get patients")}}async searchPatients(e){try{this.validateTenantAccess();let t=await this.getPatients(),a=e.toLowerCase();return t.filter(t=>t.name.toLowerCase().includes(a)||t.medicalRecordNumber.toLowerCase().includes(a)||t.phone.includes(e)||t.email.toLowerCase().includes(a))}catch(e){this.handleError(e,"search patients")}}async updatePatient(e,t){try{this.validateTenantAccess();let a=this.getDocument("patients",e);await (0,r.r7)(a,{...t,updatedAt:new Date().toISOString()})}catch(e){this.handleError(e,"update patient")}}async deletePatient(e){try{this.validateTenantAccess();let t=this.getDocument("patients",e);await (0,r.oe)(t)}catch(e){this.handleError(e,"delete patient")}}async addClinicalImage(e,t){try{this.validateTenantAccess();let a=await this.getPatient(e);if(!a)throw Error("Patient not found");let s={id:Date.now().toString(),...t,date:new Date().toISOString()},n=[...a.clinicalImages||[],s];await this.updatePatient(e,{clinicalImages:n})}catch(e){this.handleError(e,"add clinical image")}}async updateDentalChart(e,t,a,s){try{var n;this.validateTenantAccess();let i=await this.getPatient(e);if(!i)throw Error("Patient not found");let r=(null===(n=i.dentalChart)||void 0===n?void 0:n.map(e=>e.toothNumber===t?{...e,condition:a,notes:s||e.notes,updatedAt:new Date().toISOString()}:e))||[];await this.updatePatient(e,{dentalChart:r})}catch(e){this.handleError(e,"update dental chart")}}subscribeToPatients(e){this.validateTenantAccess();let t=(0,r.IO)(this.getCollection("patients"),(0,r.Xo)("name"));return(0,r.cf)(t,t=>{let a=t.docs.map(e=>({id:e.id,...e.data()}));e(a)},e=>{console.error("Error in patients subscription:",e)})}};var c=a(5495);function usePatients(){let{tenantId:e}=(0,c.S)();return(0,s.a)({queryKey:["patients",e],queryFn:()=>{if(!e)throw Error("No tenant selected");let t=TenantService_TenantServiceRegistry.getService(patients_PatientService,e,"patients");return t.getPatients()},enabled:!!e,staleTime:3e5})}function useCreatePatient(){let e=(0,n.NL)(),{tenantId:t}=(0,c.S)();return(0,i.D)({mutationFn:e=>{if(!t)throw Error("No tenant selected");let a=TenantService_TenantServiceRegistry.getService(patients_PatientService,t,"patients");return a.createPatient(e)},onSuccess:()=>{e.invalidateQueries({queryKey:["patients",t]})},onError:e=>{console.error("Error creating patient:",e)}})}},9367:function(e,t,a){"use strict";var s=a(2265);function EyeIcon({title:e,titleId:t,...a},n){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},a),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}let n=s.forwardRef(EyeIcon);t.Z=n},2125:function(e,t,a){"use strict";var s=a(2265);function PencilIcon({title:e,titleId:t,...a},n){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},a),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))}let n=s.forwardRef(PencilIcon);t.Z=n}},function(e){e.O(0,[609,15,801,273,995,621,971,472,744],function(){return e(e.s=2231)}),_N_E=e.O()}]);