(()=>{var e={};e.id=938,e.ids=[938],e.modules={5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},2361:e=>{"use strict";e.exports=require("events")},7147:e=>{"use strict";e.exports=require("fs")},3685:e=>{"use strict";e.exports=require("http")},5158:e=>{"use strict";e.exports=require("http2")},1808:e=>{"use strict";e.exports=require("net")},2037:e=>{"use strict";e.exports=require("os")},1017:e=>{"use strict";e.exports=require("path")},7282:e=>{"use strict";e.exports=require("process")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},3837:e=>{"use strict";e.exports=require("util")},9796:e=>{"use strict";e.exports=require("zlib")},5518:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>o,originalPathname:()=>m,pages:()=>x,routeModule:()=>p,tree:()=>d});var a=t(7096),r=t(6132),i=t(7284),l=t.n(i),n=t(2564),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,3859)),"D:\\dentalcare.id\\src\\app\\settings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,2594)),"D:\\dentalcare.id\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9291,23)),"next/dist/client/components/not-found-error"]}],x=["D:\\dentalcare.id\\src\\app\\settings\\page.tsx"],m="/settings/page",o={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5359:(e,s,t)=>{Promise.resolve().then(t.bind(t,5603))},5603:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>SettingsPage});var a=t(784),r=t(9212),i=t(7254),l=t(7863),n=t(5050),c=t(3190),d=t(5149),x=t(5573);function SettingsPage(){return(0,a.jsxs)("div",{className:"flex-1 overflow-auto",children:[a.jsx(r.Z,{title:"Pengaturan",subtitle:"Konfigurasi sistem dan preferensi klinik"}),a.jsx("main",{className:"p-6",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,a.jsxs)("div",{className:"card",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[a.jsx(i.Z,{className:"w-6 h-6 text-primary-600 mr-3"}),a.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Informasi Klinik"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Nama Klinik"}),a.jsx("input",{type:"text",className:"input-field",defaultValue:"Klinik Gigi DentalCare"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Nomor Telepon"}),a.jsx("input",{type:"text",className:"input-field",defaultValue:"(021) 1234-5678"})]}),(0,a.jsxs)("div",{className:"md:col-span-2",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Alamat"}),a.jsx("textarea",{className:"input-field",rows:3,defaultValue:"Jl. Sudirman No. 123, Jakarta Pusat 10110"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),a.jsx("input",{type:"email",className:"input-field",defaultValue:"<EMAIL>"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Website"}),a.jsx("input",{type:"url",className:"input-field",defaultValue:"https://dentalcare.id"})]})]}),a.jsx("div",{className:"mt-6",children:a.jsx("button",{className:"btn-primary",children:"Simpan Perubahan"})})]}),(0,a.jsxs)("div",{className:"card",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(l.Z,{className:"w-6 h-6 text-primary-600 mr-3"}),a.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Manajemen Pengguna"})]}),a.jsx("button",{className:"btn-primary",children:"+ Tambah Pengguna"})]}),a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[a.jsx("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Nama"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Email"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Role"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Status"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Aksi"})]})}),(0,a.jsxs)("tbody",{className:"bg-white divide-y divide-gray-200",children:[(0,a.jsxs)("tr",{children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:"Dr. Sarah Putri"}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"<EMAIL>"}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsx("span",{className:"status-badge bg-purple-100 text-purple-800",children:"Dokter"})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsx("span",{className:"status-badge status-active",children:"Aktif"})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[a.jsx("button",{className:"text-primary-600 hover:text-primary-900 mr-3",children:"Edit"}),a.jsx("button",{className:"text-red-600 hover:text-red-900",children:"Hapus"})]})]}),(0,a.jsxs)("tr",{children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:"Siti Nurhaliza"}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"<EMAIL>"}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsx("span",{className:"status-badge bg-blue-100 text-blue-800",children:"Resepsionis"})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsx("span",{className:"status-badge status-active",children:"Aktif"})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[a.jsx("button",{className:"text-primary-600 hover:text-primary-900 mr-3",children:"Edit"}),a.jsx("button",{className:"text-red-600 hover:text-red-900",children:"Hapus"})]})]})]})]})})]}),(0,a.jsxs)("div",{className:"card",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[a.jsx(n.Z,{className:"w-6 h-6 text-primary-600 mr-3"}),a.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Pengaturan Notifikasi"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium text-gray-900",children:"Reminder Appointment"}),a.jsx("p",{className:"text-sm text-gray-600",children:"Kirim reminder H-1 dan H-0 ke pasien"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[a.jsx("input",{type:"checkbox",className:"sr-only peer",defaultChecked:!0}),a.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium text-gray-900",children:"Alert Stok Menipis"}),a.jsx("p",{className:"text-sm text-gray-600",children:"Notifikasi ketika stok inventory menipis"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[a.jsx("input",{type:"checkbox",className:"sr-only peer",defaultChecked:!0}),a.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium text-gray-900",children:"Laporan Harian"}),a.jsx("p",{className:"text-sm text-gray-600",children:"Email laporan harian ke manajemen"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[a.jsx("input",{type:"checkbox",className:"sr-only peer"}),a.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"})]})]})]})]}),(0,a.jsxs)("div",{className:"card",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[a.jsx(c.Z,{className:"w-6 h-6 text-primary-600 mr-3"}),a.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Pengaturan Pembayaran"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Metode Pembayaran Aktif"}),a.jsx("div",{className:"space-y-2",children:["Tunai","Kartu Debit/Kredit","Transfer Bank","QRIS","E-Wallet"].map(e=>(0,a.jsxs)("label",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",className:"rounded border-gray-300 text-primary-600 mr-2",defaultChecked:!0}),a.jsx("span",{className:"text-sm text-gray-700",children:e})]},e))})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Pajak (%)"}),a.jsx("input",{type:"number",className:"input-field",defaultValue:"11",min:"0",max:"100"}),a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2 mt-4",children:"Diskon Maksimal (%)"}),a.jsx("input",{type:"number",className:"input-field",defaultValue:"20",min:"0",max:"100"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"card",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[a.jsx(d.Z,{className:"w-6 h-6 text-primary-600 mr-3"}),a.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Keamanan"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("button",{className:"w-full btn-secondary text-left",children:"Ubah Password"}),a.jsx("button",{className:"w-full btn-secondary text-left",children:"Aktivasi 2FA"}),a.jsx("button",{className:"w-full btn-secondary text-left",children:"Log Aktivitas"}),a.jsx("button",{className:"w-full btn-secondary text-left",children:"Backup Data"})]})]}),(0,a.jsxs)("div",{className:"card",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[a.jsx(x.Z,{className:"w-6 h-6 text-primary-600 mr-3"}),a.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Preferensi"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Bahasa"}),(0,a.jsxs)("select",{className:"input-field",children:[a.jsx("option",{value:"id",children:"Bahasa Indonesia"}),a.jsx("option",{value:"en",children:"English"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Zona Waktu"}),(0,a.jsxs)("select",{className:"input-field",children:[a.jsx("option",{value:"Asia/Jakarta",children:"WIB (Jakarta)"}),a.jsx("option",{value:"Asia/Makassar",children:"WITA (Makassar)"}),a.jsx("option",{value:"Asia/Jayapura",children:"WIT (Jayapura)"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Format Tanggal"}),(0,a.jsxs)("select",{className:"input-field",children:[a.jsx("option",{value:"dd/mm/yyyy",children:"DD/MM/YYYY"}),a.jsx("option",{value:"mm/dd/yyyy",children:"MM/DD/YYYY"}),a.jsx("option",{value:"yyyy-mm-dd",children:"YYYY-MM-DD"})]})]})]})]})]})]})})]})}},3859:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>i,default:()=>c});var a=t(5153);let r=(0,a.createProxy)(String.raw`D:\dentalcare.id\src\app\settings\page.tsx`),{__esModule:i,$$typeof:l}=r,n=r.default,c=n}};var s=require("../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),t=s.X(0,[172,684,763,29,212],()=>__webpack_exec__(5518));module.exports=t})();