import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { AppointmentService } from '@/services/appointments';
import { TenantServiceRegistry } from '@/services/base/TenantService';
import { useTenant } from '@/contexts/TenantContext';
import { Appointment } from '@/types';

/**
 * Get all appointments for current tenant
 */
export function useAppointments() {
  const { tenantId } = useTenant();
  
  return useQuery({
    queryKey: ['appointments', tenantId],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const appointmentService = TenantServiceRegistry.getService(AppointmentService, tenantId, 'appointments');
      return appointmentService.getAppointments();
    },
    enabled: !!tenantId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

/**
 * Get single appointment by ID
 */
export function useAppointment(id: string) {
  const { tenantId } = useTenant();
  
  return useQuery({
    queryKey: ['appointment', tenantId, id],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const appointmentService = TenantServiceRegistry.getService(AppointmentService, tenantId, 'appointments');
      return appointmentService.getAppointment(id);
    },
    enabled: !!id && !!tenantId,
  });
}

/**
 * Get appointments by date
 */
export function useAppointmentsByDate(date: string) {
  const { tenantId } = useTenant();
  
  return useQuery({
    queryKey: ['appointments', 'date', tenantId, date],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const appointmentService = TenantServiceRegistry.getService(AppointmentService, tenantId, 'appointments');
      return appointmentService.getAppointmentsByDate(date);
    },
    enabled: !!tenantId && !!date,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
}

/**
 * Get today's appointments
 */
export function useTodayAppointments() {
  const { tenantId } = useTenant();
  const today = new Date().toISOString().split('T')[0];
  
  return useQuery({
    queryKey: ['appointments', 'today', tenantId, today],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const appointmentService = TenantServiceRegistry.getService(AppointmentService, tenantId, 'appointments');
      return appointmentService.getTodayAppointments();
    },
    enabled: !!tenantId,
    staleTime: 1 * 60 * 1000, // 1 minute
    refetchInterval: 2 * 60 * 1000, // Refetch every 2 minutes
  });
}

/**
 * Get appointments by patient ID
 */
export function useAppointmentsByPatient(patientId: string) {
  const { tenantId } = useTenant();
  
  return useQuery({
    queryKey: ['appointments', 'patient', tenantId, patientId],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const appointmentService = TenantServiceRegistry.getService(AppointmentService, tenantId, 'appointments');
      return appointmentService.getAppointmentsByPatient(patientId);
    },
    enabled: !!tenantId && !!patientId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Get appointments by doctor ID
 */
export function useAppointmentsByDoctor(doctorId: string) {
  const { tenantId } = useTenant();
  
  return useQuery({
    queryKey: ['appointments', 'doctor', tenantId, doctorId],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const appointmentService = TenantServiceRegistry.getService(AppointmentService, tenantId, 'appointments');
      return appointmentService.getAppointmentsByDoctor(doctorId);
    },
    enabled: !!tenantId && !!doctorId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Get upcoming appointments
 */
export function useUpcomingAppointments(days: number = 7) {
  const { tenantId } = useTenant();
  
  return useQuery({
    queryKey: ['appointments', 'upcoming', tenantId, days],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const appointmentService = TenantServiceRegistry.getService(AppointmentService, tenantId, 'appointments');
      return appointmentService.getUpcomingAppointments(days);
    },
    enabled: !!tenantId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Create new appointment
 */
export function useCreateAppointment() {
  const queryClient = useQueryClient();
  const { tenantId } = useTenant();

  return useMutation({
    mutationFn: (appointmentData: Omit<Appointment, 'id'>) => {
      if (!tenantId) throw new Error('No tenant selected');
      const appointmentService = TenantServiceRegistry.getService(AppointmentService, tenantId, 'appointments');
      return appointmentService.createAppointment(appointmentData);
    },
    onSuccess: (_, appointmentData) => {
      queryClient.invalidateQueries({ queryKey: ['appointments', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['appointments', 'date', tenantId, appointmentData.date] });
      queryClient.invalidateQueries({ queryKey: ['appointments', 'today', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['appointments', 'upcoming', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['appointments', 'patient', tenantId, appointmentData.patientId] });
      queryClient.invalidateQueries({ queryKey: ['appointments', 'doctor', tenantId, appointmentData.doctorId] });
    },
    onError: (error) => {
      console.error('Error creating appointment:', error);
    }
  });
}

/**
 * Update appointment
 */
export function useUpdateAppointment() {
  const queryClient = useQueryClient();
  const { tenantId } = useTenant();

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<Appointment> }) => {
      if (!tenantId) throw new Error('No tenant selected');
      const appointmentService = TenantServiceRegistry.getService(AppointmentService, tenantId, 'appointments');
      return appointmentService.updateAppointment(id, updates);
    },
    onSuccess: (_, { id, updates }) => {
      queryClient.invalidateQueries({ queryKey: ['appointments', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['appointment', tenantId, id] });
      queryClient.invalidateQueries({ queryKey: ['appointments', 'today', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['appointments', 'upcoming', tenantId] });
      
      // Invalidate date-specific queries if date was updated
      if (updates.date) {
        queryClient.invalidateQueries({ queryKey: ['appointments', 'date', tenantId, updates.date] });
      }
    },
    onError: (error) => {
      console.error('Error updating appointment:', error);
    }
  });
}

/**
 * Update appointment status
 */
export function useUpdateAppointmentStatus() {
  const queryClient = useQueryClient();
  const { tenantId } = useTenant();

  return useMutation({
    mutationFn: ({ id, status, notes }: { id: string; status: Appointment['status']; notes?: string }) => {
      if (!tenantId) throw new Error('No tenant selected');
      const appointmentService = TenantServiceRegistry.getService(AppointmentService, tenantId, 'appointments');
      return appointmentService.updateAppointmentStatus(id, status, notes);
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['appointments', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['appointment', tenantId, id] });
      queryClient.invalidateQueries({ queryKey: ['appointments', 'today', tenantId] });
    },
    onError: (error) => {
      console.error('Error updating appointment status:', error);
    }
  });
}

/**
 * Delete appointment
 */
export function useDeleteAppointment() {
  const queryClient = useQueryClient();
  const { tenantId } = useTenant();

  return useMutation({
    mutationFn: (id: string) => {
      if (!tenantId) throw new Error('No tenant selected');
      const appointmentService = TenantServiceRegistry.getService(AppointmentService, tenantId, 'appointments');
      return appointmentService.deleteAppointment(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['appointments', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['appointments', 'today', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['appointments', 'upcoming', tenantId] });
    },
    onError: (error) => {
      console.error('Error deleting appointment:', error);
    }
  });
}

/**
 * Real-time appointments subscription
 */
export function useRealTimeAppointments() {
  const { tenantId } = useTenant();
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!tenantId) {
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    const appointmentService = TenantServiceRegistry.getService(AppointmentService, tenantId, 'appointments');
    const unsubscribe = appointmentService.subscribeToAppointments(
      (updatedAppointments: Appointment[]) => {
        setAppointments(updatedAppointments);
        setLoading(false);
      }
    );

    return () => {
      unsubscribe();
    };
  }, [tenantId]);

  return { appointments, loading, error };
}

/**
 * Real-time today's appointments subscription
 */
export function useRealTimeTodayAppointments() {
  const { tenantId } = useTenant();
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!tenantId) {
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    const appointmentService = TenantServiceRegistry.getService(AppointmentService, tenantId, 'appointments');
    const unsubscribe = appointmentService.subscribeToTodayAppointments(
      (updatedAppointments: Appointment[]) => {
        setAppointments(updatedAppointments);
        setLoading(false);
      }
    );

    return () => {
      unsubscribe();
    };
  }, [tenantId]);

  return { appointments, loading, error };
}
