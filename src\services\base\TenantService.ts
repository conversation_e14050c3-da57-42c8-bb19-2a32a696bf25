import { 
  collection, 
  doc, 
  CollectionReference, 
  DocumentReference,
  FirestoreError 
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

/**
 * Base service class for tenant-aware Firestore operations
 * All tenant-specific services should extend this class
 */
export abstract class TenantService {
  protected tenantId: string;

  constructor(tenantId: string) {
    if (!tenantId) {
      throw new Error('TenantService requires a valid tenantId');
    }
    this.tenantId = tenantId;
  }

  /**
   * Get a tenant-specific collection reference
   */
  protected getCollection(collectionName: string): CollectionReference {
    return collection(db, 'dentalcare', this.tenantId, collectionName);
  }

  /**
   * Get a tenant-specific document reference
   */
  protected getDocument(collectionName: string, documentId: string): DocumentReference {
    return doc(db, 'dentalcare', this.tenantId, collectionName, documentId);
  }

  /**
   * Get tenant settings document reference
   */
  protected getSettingsDocument(settingType: string): DocumentReference {
    return doc(db, 'dentalcare', this.tenantId, 'settings', settingType);
  }

  /**
   * Validate tenant access (can be overridden by subclasses)
   */
  protected validateTenantAccess(): void {
    if (!this.tenantId) {
      throw new Error('No tenant ID provided');
    }
  }

  /**
   * Handle Firestore errors with tenant context
   */
  protected handleError(error: any, operation: string): never {
    console.error(`TenantService Error [${this.tenantId}] - ${operation}:`, error);
    
    if (error instanceof FirestoreError) {
      switch (error.code) {
        case 'permission-denied':
          throw new Error(`Access denied for tenant ${this.tenantId}. Please check your permissions.`);
        case 'not-found':
          throw new Error(`Resource not found in tenant ${this.tenantId}.`);
        case 'unavailable':
          throw new Error('Service temporarily unavailable. Please try again.');
        default:
          throw new Error(`Database operation failed: ${error.message}`);
      }
    }
    
    throw new Error(`Failed to ${operation}: ${error.message || 'Unknown error'}`);
  }

  /**
   * Get current tenant ID
   */
  public getTenantId(): string {
    return this.tenantId;
  }

  /**
   * Update tenant ID (useful for tenant switching)
   */
  public setTenantId(newTenantId: string): void {
    if (!newTenantId) {
      throw new Error('Invalid tenant ID');
    }
    this.tenantId = newTenantId;
  }
}

/**
 * Factory function to create tenant-aware service instances
 */
export function createTenantService<T extends TenantService>(
  ServiceClass: new (tenantId: string) => T,
  tenantId: string
): T {
  return new ServiceClass(tenantId);
}

/**
 * Service registry for managing tenant-specific service instances
 */
export class TenantServiceRegistry {
  private static instances = new Map<string, Map<string, TenantService>>();

  /**
   * Get or create a tenant-specific service instance
   */
  static getService<T extends TenantService>(
    ServiceClass: new (tenantId: string) => T,
    tenantId: string,
    serviceName: string
  ): T {
    if (!this.instances.has(tenantId)) {
      this.instances.set(tenantId, new Map());
    }

    const tenantServices = this.instances.get(tenantId)!;
    
    if (!tenantServices.has(serviceName)) {
      tenantServices.set(serviceName, new ServiceClass(tenantId));
    }

    return tenantServices.get(serviceName) as T;
  }

  /**
   * Clear all service instances for a tenant (useful for tenant switching)
   */
  static clearTenantServices(tenantId: string): void {
    this.instances.delete(tenantId);
  }

  /**
   * Clear all service instances
   */
  static clearAllServices(): void {
    this.instances.clear();
  }
}
