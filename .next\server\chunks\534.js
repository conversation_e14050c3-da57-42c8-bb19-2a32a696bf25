"use strict";exports.id=534,exports.ids=[534],exports.modules={534:(t,e,a)=>{a.d(e,{KA:()=>useCreatePatient,w5:()=>usePatients});var n=a(1778),i=a(4070),r=a(4944);a(9885);var s=a(9904),c=a(2373);let TenantService=class TenantService{constructor(t){if(!t)throw Error("TenantService requires a valid tenantId");this.tenantId=t}getCollection(t){return(0,s.hJ)(c.db,"dentalcare",this.tenantId,t)}getDocument(t,e){return(0,s.JU)(c.db,"dentalcare",this.tenantId,t,e)}getSettingsDocument(t){return(0,s.JU)(c.db,"dentalcare",this.tenantId,"settings",t)}validateTenantAccess(){if(!this.tenantId)throw Error("No tenant ID provided")}handleError(t,e){if(console.error(`TenantService Error [${this.tenantId}] - ${e}:`,t),t instanceof s.WA)switch(t.code){case"permission-denied":throw Error(`Access denied for tenant ${this.tenantId}. Please check your permissions.`);case"not-found":throw Error(`Resource not found in tenant ${this.tenantId}.`);case"unavailable":throw Error("Service temporarily unavailable. Please try again.");default:throw Error(`Database operation failed: ${t.message}`)}throw Error(`Failed to ${e}: ${t.message||"Unknown error"}`)}getTenantId(){return this.tenantId}setTenantId(t){if(!t)throw Error("Invalid tenant ID");this.tenantId=t}};let TenantService_TenantServiceRegistry=class TenantService_TenantServiceRegistry{static{this.instances=new Map}static getService(t,e,a){this.instances.has(e)||this.instances.set(e,new Map);let n=this.instances.get(e);return n.has(a)||n.set(a,new t(e)),n.get(a)}static clearTenantServices(t){this.instances.delete(t)}static clearAllServices(){this.instances.clear()}};let patients_PatientService=class patients_PatientService extends TenantService{async generateMedicalRecordNumber(){let t=new Date().getFullYear(),e=(0,s.IO)(this.getCollection("patients"),(0,s.ar)("medicalRecordNumber",">=",`RM${t}`),(0,s.ar)("medicalRecordNumber","<",`RM${t+1}`),(0,s.Xo)("medicalRecordNumber","desc"),(0,s.b9)(1)),a=await (0,s.PL)(e);if(a.empty)return`RM${t}001`;let n=a.docs[0].data(),i=parseInt(n.medicalRecordNumber.slice(-3)),r=(i+1).toString().padStart(3,"0");return`RM${t}${r}`}async createPatient(t){try{this.validateTenantAccess();let e=await this.generateMedicalRecordNumber(),a={...t,medicalRecordNumber:e,totalVisits:0,status:"active",dentalChart:this.initializeDentalChart(),clinicalImages:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},n=await (0,s.ET)(this.getCollection("patients"),a);return n.id}catch(t){this.handleError(t,"create patient")}}initializeDentalChart(){let t=[];for(let e=1;e<=32;e++)t.push({toothNumber:e,condition:"healthy",notes:"",updatedAt:new Date().toISOString()});return t}async getPatient(t){try{this.validateTenantAccess();let e=this.getDocument("patients",t),a=await (0,s.QT)(e);if(a.exists())return{id:a.id,...a.data()};return null}catch(t){this.handleError(t,"get patient")}}async getPatients(){try{this.validateTenantAccess();let t=(0,s.IO)(this.getCollection("patients"),(0,s.Xo)("name")),e=await (0,s.PL)(t);return e.docs.map(t=>({id:t.id,...t.data()}))}catch(t){this.handleError(t,"get patients")}}async searchPatients(t){try{this.validateTenantAccess();let e=await this.getPatients(),a=t.toLowerCase();return e.filter(e=>e.name.toLowerCase().includes(a)||e.medicalRecordNumber.toLowerCase().includes(a)||e.phone.includes(t)||e.email.toLowerCase().includes(a))}catch(t){this.handleError(t,"search patients")}}async updatePatient(t,e){try{this.validateTenantAccess();let a=this.getDocument("patients",t);await (0,s.r7)(a,{...e,updatedAt:new Date().toISOString()})}catch(t){this.handleError(t,"update patient")}}async deletePatient(t){try{this.validateTenantAccess();let e=this.getDocument("patients",t);await (0,s.oe)(e)}catch(t){this.handleError(t,"delete patient")}}async addClinicalImage(t,e){try{this.validateTenantAccess();let a=await this.getPatient(t);if(!a)throw Error("Patient not found");let n={id:Date.now().toString(),...e,date:new Date().toISOString()},i=[...a.clinicalImages||[],n];await this.updatePatient(t,{clinicalImages:i})}catch(t){this.handleError(t,"add clinical image")}}async updateDentalChart(t,e,a,n){try{this.validateTenantAccess();let i=await this.getPatient(t);if(!i)throw Error("Patient not found");let r=i.dentalChart?.map(t=>t.toothNumber===e?{...t,condition:a,notes:n||t.notes,updatedAt:new Date().toISOString()}:t)||[];await this.updatePatient(t,{dentalChart:r})}catch(t){this.handleError(t,"update dental chart")}}subscribeToPatients(t){this.validateTenantAccess();let e=(0,s.IO)(this.getCollection("patients"),(0,s.Xo)("name"));return(0,s.cf)(e,e=>{let a=e.docs.map(t=>({id:t.id,...t.data()}));t(a)},t=>{console.error("Error in patients subscription:",t)})}};var o=a(4751);function usePatients(){let{tenantId:t}=(0,o.S)();return(0,n.a)({queryKey:["patients",t],queryFn:()=>{if(!t)throw Error("No tenant selected");let e=TenantService_TenantServiceRegistry.getService(patients_PatientService,t,"patients");return e.getPatients()},enabled:!!t,staleTime:3e5})}function useCreatePatient(){let t=(0,i.NL)(),{tenantId:e}=(0,o.S)();return(0,r.D)({mutationFn:t=>{if(!e)throw Error("No tenant selected");let a=TenantService_TenantServiceRegistry.getService(patients_PatientService,e,"patients");return a.createPatient(t)},onSuccess:()=>{t.invalidateQueries({queryKey:["patients",e]})},onError:t=>{console.error("Error creating patient:",t)}})}}};