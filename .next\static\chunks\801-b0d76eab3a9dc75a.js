(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[801],{4534:function(e,i,o){"use strict";o.d(i,{BH:function(){return Deferred},LL:function(){return ErrorFactory},ZR:function(){return FirebaseError},tV:function(){return base64Decode},L:function(){return base64urlEncodeWithoutPadding},Sg:function(){return createMockUserToken},ne:function(){return createSubscribe},vZ:function(){return deepEqual},pd:function(){return extractQuerystring},aH:function(){return getDefaultAppConfig},q4:function(){return getDefaultEmulatorHost},P0:function(){return getDefaultEmulatorHostnameAndPort},Pz:function(){return getExperimentalSetting},m9:function(){return getModularInstance},z$:function(){return getUA},ru:function(){return isBrowserExtension},Xx:function(){return isCloudWorkstation},L_:function(){return isCloudflareWorker},xb:function(){return isEmpty},w1:function(){return isIE},hl:function(){return isIndexedDBAvailable},uI:function(){return isMobileCordova},b$:function(){return isReactNative},G6:function(){return isSafari},Uo:function(){return pingServer},xO:function(){return querystring},zd:function(){return querystringDecode},dp:function(){return updateEmulatorBanner},eu:function(){return validateIndexedDBOpenable}});let getDefaultsFromPostinstall=()=>void 0;var s=o(2601);/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let stringToByteArray$1=function(e){let i=[],o=0;for(let s=0;s<e.length;s++){let h=e.charCodeAt(s);h<128?i[o++]=h:(h<2048?i[o++]=h>>6|192:((64512&h)==55296&&s+1<e.length&&(64512&e.charCodeAt(s+1))==56320?(h=65536+((1023&h)<<10)+(1023&e.charCodeAt(++s)),i[o++]=h>>18|240,i[o++]=h>>12&63|128):i[o++]=h>>12|224,i[o++]=h>>6&63|128),i[o++]=63&h|128)}return i},byteArrayToString=function(e){let i=[],o=0,s=0;for(;o<e.length;){let h=e[o++];if(h<128)i[s++]=String.fromCharCode(h);else if(h>191&&h<224){let f=e[o++];i[s++]=String.fromCharCode((31&h)<<6|63&f)}else if(h>239&&h<365){let f=e[o++],l=e[o++],d=e[o++],g=((7&h)<<18|(63&f)<<12|(63&l)<<6|63&d)-65536;i[s++]=String.fromCharCode(55296+(g>>10)),i[s++]=String.fromCharCode(56320+(1023&g))}else{let f=e[o++],l=e[o++];i[s++]=String.fromCharCode((15&h)<<12|(63&f)<<6|63&l)}}return i.join("")},h={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray(e,i){if(!Array.isArray(e))throw Error("encodeByteArray takes an array as a parameter");this.init_();let o=i?this.byteToCharMapWebSafe_:this.byteToCharMap_,s=[];for(let i=0;i<e.length;i+=3){let h=e[i],f=i+1<e.length,l=f?e[i+1]:0,d=i+2<e.length,g=d?e[i+2]:0,b=h>>2,w=(3&h)<<4|l>>4,_=(15&l)<<2|g>>6,O=63&g;d||(O=64,f||(_=64)),s.push(o[b],o[w],o[_],o[O])}return s.join("")},encodeString(e,i){return this.HAS_NATIVE_SUPPORT&&!i?btoa(e):this.encodeByteArray(stringToByteArray$1(e),i)},decodeString(e,i){return this.HAS_NATIVE_SUPPORT&&!i?atob(e):byteArrayToString(this.decodeStringToByteArray(e,i))},decodeStringToByteArray(e,i){this.init_();let o=i?this.charToByteMapWebSafe_:this.charToByteMap_,s=[];for(let i=0;i<e.length;){let h=o[e.charAt(i++)],f=i<e.length,l=f?o[e.charAt(i)]:0;++i;let d=i<e.length,g=d?o[e.charAt(i)]:64;++i;let b=i<e.length,w=b?o[e.charAt(i)]:64;if(++i,null==h||null==l||null==g||null==w)throw new DecodeBase64StringError;let _=h<<2|l>>4;if(s.push(_),64!==g){let e=l<<4&240|g>>2;if(s.push(e),64!==w){let e=g<<6&192|w;s.push(e)}}}return s},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let e=0;e<this.ENCODED_VALS.length;e++)this.byteToCharMap_[e]=this.ENCODED_VALS.charAt(e),this.charToByteMap_[this.byteToCharMap_[e]]=e,this.byteToCharMapWebSafe_[e]=this.ENCODED_VALS_WEBSAFE.charAt(e),this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[e]]=e,e>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(e)]=e,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(e)]=e)}}};let DecodeBase64StringError=class DecodeBase64StringError extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}};let base64Encode=function(e){let i=stringToByteArray$1(e);return h.encodeByteArray(i,!0)},base64urlEncodeWithoutPadding=function(e){return base64Encode(e).replace(/\./g,"")},base64Decode=function(e){try{return h.decodeString(e,!0)}catch(e){console.error("base64Decode failed: ",e)}return null};/**
 * @license
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function getGlobal(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==o.g)return o.g;throw Error("Unable to locate global object.")}/**
 * @license
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let getDefaultsFromGlobal=()=>getGlobal().__FIREBASE_DEFAULTS__,getDefaultsFromEnvVariable=()=>{if(void 0===s||void 0===s.env)return;let e=s.env.__FIREBASE_DEFAULTS__;if(e)return JSON.parse(e)},getDefaultsFromCookie=()=>{let e;if("undefined"==typeof document)return;try{e=document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/)}catch(e){return}let i=e&&base64Decode(e[1]);return i&&JSON.parse(i)},getDefaults=()=>{try{return getDefaultsFromPostinstall()||getDefaultsFromGlobal()||getDefaultsFromEnvVariable()||getDefaultsFromCookie()}catch(e){console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${e}`);return}},getDefaultEmulatorHost=e=>getDefaults()?.emulatorHosts?.[e],getDefaultEmulatorHostnameAndPort=e=>{let i=getDefaultEmulatorHost(e);if(!i)return;let o=i.lastIndexOf(":");if(o<=0||o+1===i.length)throw Error(`Invalid host ${i} with no separate hostname and port!`);let s=parseInt(i.substring(o+1),10);return"["===i[0]?[i.substring(1,o-1),s]:[i.substring(0,o),s]},getDefaultAppConfig=()=>getDefaults()?.config,getExperimentalSetting=e=>getDefaults()?.[`_${e}`];/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let Deferred=class Deferred{constructor(){this.reject=()=>{},this.resolve=()=>{},this.promise=new Promise((e,i)=>{this.resolve=e,this.reject=i})}wrapCallback(e){return(i,o)=>{i?this.reject(i):this.resolve(o),"function"==typeof e&&(this.promise.catch(()=>{}),1===e.length?e(i):e(i,o))}}};/**
 * @license
 * Copyright 2025 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function isCloudWorkstation(e){try{let i=e.startsWith("http://")||e.startsWith("https://")?new URL(e).hostname:e;return i.endsWith(".cloudworkstations.dev")}catch{return!1}}async function pingServer(e){let i=await fetch(e,{credentials:"include"});return i.ok}/**
 * @license
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function createMockUserToken(e,i){if(e.uid)throw Error('The "uid" field is no longer supported by mockUserToken. Please use "sub" instead for Firebase Auth User ID.');let o=i||"demo-project",s=e.iat||0,h=e.sub||e.user_id;if(!h)throw Error("mockUserToken must contain 'sub' or 'user_id' field!");let f={iss:`https://securetoken.google.com/${o}`,aud:o,iat:s,exp:s+3600,auth_time:s,sub:h,user_id:h,firebase:{sign_in_provider:"custom",identities:{}},...e};return[base64urlEncodeWithoutPadding(JSON.stringify({alg:"none",type:"JWT"})),base64urlEncodeWithoutPadding(JSON.stringify(f)),""].join(".")}let f={};function getEmulatorSummary(){let e={prod:[],emulator:[]};for(let i of Object.keys(f))f[i]?e.emulator.push(i):e.prod.push(i);return e}function getOrCreateEl(e){let i=document.getElementById(e),o=!1;return i||((i=document.createElement("div")).setAttribute("id",e),o=!0),{created:o,element:i}}let l=!1;function updateEmulatorBanner(e,i){if("undefined"==typeof window||"undefined"==typeof document||!isCloudWorkstation(window.location.host)||f[e]===i||f[e]||l)return;function prefixedId(e){return`__firebase__banner__${e}`}f[e]=i;let o="__firebase__banner",s=getEmulatorSummary(),h=s.prod.length>0;function tearDown(){let e=document.getElementById(o);e&&e.remove()}function setupBannerStyles(e){e.style.display="flex",e.style.background="#7faaf0",e.style.position="fixed",e.style.bottom="5px",e.style.left="5px",e.style.padding=".5em",e.style.borderRadius="5px",e.style.alignItems="center"}function setupIconStyles(e,i){e.setAttribute("width","24"),e.setAttribute("id",i),e.setAttribute("height","24"),e.setAttribute("viewBox","0 0 24 24"),e.setAttribute("fill","none"),e.style.marginLeft="-6px"}function setupCloseBtn(){let e=document.createElement("span");return e.style.cursor="pointer",e.style.marginLeft="16px",e.style.fontSize="24px",e.innerHTML=" &times;",e.onclick=()=>{l=!0,tearDown()},e}function setupLinkStyles(e,i){e.setAttribute("id",i),e.innerText="Learn more",e.href="https://firebase.google.com/docs/studio/preview-apps#preview-backend",e.setAttribute("target","__blank"),e.style.paddingLeft="5px",e.style.textDecoration="underline"}function setupDom(){let e=getOrCreateEl(o),i=prefixedId("text"),s=document.getElementById(i)||document.createElement("span"),f=prefixedId("learnmore"),l=document.getElementById(f)||document.createElement("a"),d=prefixedId("preprendIcon"),g=document.getElementById(d)||document.createElementNS("http://www.w3.org/2000/svg","svg");if(e.created){let i=e.element;setupBannerStyles(i),setupLinkStyles(l,f);let o=setupCloseBtn();setupIconStyles(g,d),i.append(g,s,l,o),document.body.appendChild(i)}h?(s.innerText="Preview backend disconnected.",g.innerHTML=`<g clip-path="url(#clip0_6013_33858)">
<path d="M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6013_33858">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`):(g.innerHTML=`<g clip-path="url(#clip0_6083_34804)">
<path d="M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6083_34804">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`,s.innerText="Preview backend running in this workspace."),s.setAttribute("id",i)}"loading"===document.readyState?window.addEventListener("DOMContentLoaded",setupDom):setupDom()}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function getUA(){return"undefined"!=typeof navigator&&"string"==typeof navigator.userAgent?navigator.userAgent:""}function isMobileCordova(){return"undefined"!=typeof window&&!!(window.cordova||window.phonegap||window.PhoneGap)&&/ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(getUA())}function isNode(){let e=getDefaults()?.forceEnvironment;if("node"===e)return!0;if("browser"===e)return!1;try{return"[object process]"===Object.prototype.toString.call(o.g.process)}catch(e){return!1}}function isCloudflareWorker(){return"undefined"!=typeof navigator&&"Cloudflare-Workers"===navigator.userAgent}function isBrowserExtension(){let e="object"==typeof chrome?chrome.runtime:"object"==typeof browser?browser.runtime:void 0;return"object"==typeof e&&void 0!==e.id}function isReactNative(){return"object"==typeof navigator&&"ReactNative"===navigator.product}function isIE(){let e=getUA();return e.indexOf("MSIE ")>=0||e.indexOf("Trident/")>=0}function isSafari(){return!isNode()&&!!navigator.userAgent&&navigator.userAgent.includes("Safari")&&!navigator.userAgent.includes("Chrome")}function isIndexedDBAvailable(){try{return"object"==typeof indexedDB}catch(e){return!1}}function validateIndexedDBOpenable(){return new Promise((e,i)=>{try{let o=!0,s="validate-browser-context-for-indexeddb-analytics-module",h=self.indexedDB.open(s);h.onsuccess=()=>{h.result.close(),o||self.indexedDB.deleteDatabase(s),e(!0)},h.onupgradeneeded=()=>{o=!1},h.onerror=()=>{i(h.error?.message||"")}}catch(e){i(e)}})}let FirebaseError=class FirebaseError extends Error{constructor(e,i,o){super(i),this.code=e,this.customData=o,this.name="FirebaseError",Object.setPrototypeOf(this,FirebaseError.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,ErrorFactory.prototype.create)}};let ErrorFactory=class ErrorFactory{constructor(e,i,o){this.service=e,this.serviceName=i,this.errors=o}create(e,...i){let o=i[0]||{},s=`${this.service}/${e}`,h=this.errors[e],f=h?replaceTemplate(h,o):"Error",l=`${this.serviceName}: ${f} (${s}).`,d=new FirebaseError(s,l,o);return d}};function replaceTemplate(e,i){return e.replace(d,(e,o)=>{let s=i[o];return null!=s?String(s):`<${o}?>`})}let d=/\{\$([^}]+)}/g;function isEmpty(e){for(let i in e)if(Object.prototype.hasOwnProperty.call(e,i))return!1;return!0}function deepEqual(e,i){if(e===i)return!0;let o=Object.keys(e),s=Object.keys(i);for(let h of o){if(!s.includes(h))return!1;let o=e[h],f=i[h];if(isObject(o)&&isObject(f)){if(!deepEqual(o,f))return!1}else if(o!==f)return!1}for(let e of s)if(!o.includes(e))return!1;return!0}function isObject(e){return null!==e&&"object"==typeof e}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function querystring(e){let i=[];for(let[o,s]of Object.entries(e))Array.isArray(s)?s.forEach(e=>{i.push(encodeURIComponent(o)+"="+encodeURIComponent(e))}):i.push(encodeURIComponent(o)+"="+encodeURIComponent(s));return i.length?"&"+i.join("&"):""}function querystringDecode(e){let i={},o=e.replace(/^\?/,"").split("&");return o.forEach(e=>{if(e){let[o,s]=e.split("=");i[decodeURIComponent(o)]=decodeURIComponent(s)}}),i}function extractQuerystring(e){let i=e.indexOf("?");if(!i)return"";let o=e.indexOf("#",i);return e.substring(i,o>0?o:void 0)}function createSubscribe(e,i){let o=new ObserverProxy(e,i);return o.subscribe.bind(o)}let ObserverProxy=class ObserverProxy{constructor(e,i){this.observers=[],this.unsubscribes=[],this.observerCount=0,this.task=Promise.resolve(),this.finalized=!1,this.onNoObservers=i,this.task.then(()=>{e(this)}).catch(e=>{this.error(e)})}next(e){this.forEachObserver(i=>{i.next(e)})}error(e){this.forEachObserver(i=>{i.error(e)}),this.close(e)}complete(){this.forEachObserver(e=>{e.complete()}),this.close()}subscribe(e,i,o){let s;if(void 0===e&&void 0===i&&void 0===o)throw Error("Missing Observer.");void 0===(s=implementsAnyMethods(e,["next","error","complete"])?e:{next:e,error:i,complete:o}).next&&(s.next=noop),void 0===s.error&&(s.error=noop),void 0===s.complete&&(s.complete=noop);let h=this.unsubscribeOne.bind(this,this.observers.length);return this.finalized&&this.task.then(()=>{try{this.finalError?s.error(this.finalError):s.complete()}catch(e){}}),this.observers.push(s),h}unsubscribeOne(e){void 0!==this.observers&&void 0!==this.observers[e]&&(delete this.observers[e],this.observerCount-=1,0===this.observerCount&&void 0!==this.onNoObservers&&this.onNoObservers(this))}forEachObserver(e){if(!this.finalized)for(let i=0;i<this.observers.length;i++)this.sendOne(i,e)}sendOne(e,i){this.task.then(()=>{if(void 0!==this.observers&&void 0!==this.observers[e])try{i(this.observers[e])}catch(e){"undefined"!=typeof console&&console.error&&console.error(e)}})}close(e){this.finalized||(this.finalized=!0,void 0!==e&&(this.finalError=e),this.task.then(()=>{this.observers=void 0,this.onNoObservers=void 0}))}};function implementsAnyMethods(e,i){if("object"!=typeof e||null===e)return!1;for(let o of i)if(o in e&&"function"==typeof e[o])return!0;return!1}function noop(){}/**
 * @license
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function getModularInstance(e){return e&&e._delegate?e._delegate:e}},2601:function(e,i,o){"use strict";var s,h;e.exports=(null==(s=o.g.process)?void 0:s.env)&&"object"==typeof(null==(h=o.g.process)?void 0:h.env)?o.g.process:o(8960)},263:function(e){!function(){var i={675:function(e,i){"use strict";i.byteLength=byteLength,i.toByteArray=toByteArray,i.fromByteArray=fromByteArray;for(var o=[],s=[],h="undefined"!=typeof Uint8Array?Uint8Array:Array,f="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",l=0,d=f.length;l<d;++l)o[l]=f[l],s[f.charCodeAt(l)]=l;function getLens(e){var i=e.length;if(i%4>0)throw Error("Invalid string. Length must be a multiple of 4");var o=e.indexOf("=");-1===o&&(o=i);var s=o===i?0:4-o%4;return[o,s]}function byteLength(e){var i=getLens(e),o=i[0],s=i[1];return(o+s)*3/4-s}function _byteLength(e,i,o){return(i+o)*3/4-o}function toByteArray(e){var i,o,f=getLens(e),l=f[0],d=f[1],g=new h(_byteLength(e,l,d)),b=0,w=d>0?l-4:l;for(o=0;o<w;o+=4)i=s[e.charCodeAt(o)]<<18|s[e.charCodeAt(o+1)]<<12|s[e.charCodeAt(o+2)]<<6|s[e.charCodeAt(o+3)],g[b++]=i>>16&255,g[b++]=i>>8&255,g[b++]=255&i;return 2===d&&(i=s[e.charCodeAt(o)]<<2|s[e.charCodeAt(o+1)]>>4,g[b++]=255&i),1===d&&(i=s[e.charCodeAt(o)]<<10|s[e.charCodeAt(o+1)]<<4|s[e.charCodeAt(o+2)]>>2,g[b++]=i>>8&255,g[b++]=255&i),g}function tripletToBase64(e){return o[e>>18&63]+o[e>>12&63]+o[e>>6&63]+o[63&e]}function encodeChunk(e,i,o){for(var s=[],h=i;h<o;h+=3)s.push(tripletToBase64((e[h]<<16&16711680)+(e[h+1]<<8&65280)+(255&e[h+2])));return s.join("")}function fromByteArray(e){for(var i,s=e.length,h=s%3,f=[],l=0,d=s-h;l<d;l+=16383)f.push(encodeChunk(e,l,l+16383>d?d:l+16383));return 1===h?f.push(o[(i=e[s-1])>>2]+o[i<<4&63]+"=="):2===h&&f.push(o[(i=(e[s-2]<<8)+e[s-1])>>10]+o[i>>4&63]+o[i<<2&63]+"="),f.join("")}s["-".charCodeAt(0)]=62,s["_".charCodeAt(0)]=63},72:function(e,i,o){"use strict";/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */var s=o(675),h=o(783),f="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function typedArraySupport(){try{var e=new Uint8Array(1),i={foo:function(){return 42}};return Object.setPrototypeOf(i,Uint8Array.prototype),Object.setPrototypeOf(e,i),42===e.foo()}catch(e){return!1}}function createBuffer(e){if(e>**********)throw RangeError('The value "'+e+'" is invalid for option "size"');var i=new Uint8Array(e);return Object.setPrototypeOf(i,Buffer.prototype),i}function Buffer(e,i,o){if("number"==typeof e){if("string"==typeof i)throw TypeError('The "string" argument must be of type string. Received type number');return allocUnsafe(e)}return from(e,i,o)}function from(e,i,o){if("string"==typeof e)return fromString(e,i);if(ArrayBuffer.isView(e))return fromArrayLike(e);if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(isInstance(e,ArrayBuffer)||e&&isInstance(e.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(isInstance(e,SharedArrayBuffer)||e&&isInstance(e.buffer,SharedArrayBuffer)))return fromArrayBuffer(e,i,o);if("number"==typeof e)throw TypeError('The "value" argument must not be of type number. Received type number');var s=e.valueOf&&e.valueOf();if(null!=s&&s!==e)return Buffer.from(s,i,o);var h=fromObject(e);if(h)return h;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return Buffer.from(e[Symbol.toPrimitive]("string"),i,o);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function assertSize(e){if("number"!=typeof e)throw TypeError('"size" argument must be of type number');if(e<0)throw RangeError('The value "'+e+'" is invalid for option "size"')}function alloc(e,i,o){return(assertSize(e),e<=0)?createBuffer(e):void 0!==i?"string"==typeof o?createBuffer(e).fill(i,o):createBuffer(e).fill(i):createBuffer(e)}function allocUnsafe(e){return assertSize(e),createBuffer(e<0?0:0|checked(e))}function fromString(e,i){if(("string"!=typeof i||""===i)&&(i="utf8"),!Buffer.isEncoding(i))throw TypeError("Unknown encoding: "+i);var o=0|byteLength(e,i),s=createBuffer(o),h=s.write(e,i);return h!==o&&(s=s.slice(0,h)),s}function fromArrayLike(e){for(var i=e.length<0?0:0|checked(e.length),o=createBuffer(i),s=0;s<i;s+=1)o[s]=255&e[s];return o}function fromArrayBuffer(e,i,o){var s;if(i<0||e.byteLength<i)throw RangeError('"offset" is outside of buffer bounds');if(e.byteLength<i+(o||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(s=void 0===i&&void 0===o?new Uint8Array(e):void 0===o?new Uint8Array(e,i):new Uint8Array(e,i,o),Buffer.prototype),s}function fromObject(e){if(Buffer.isBuffer(e)){var i,o=0|checked(e.length),s=createBuffer(o);return 0===s.length||e.copy(s,0,0,o),s}return void 0!==e.length?"number"!=typeof e.length||(i=e.length)!=i?createBuffer(0):fromArrayLike(e):"Buffer"===e.type&&Array.isArray(e.data)?fromArrayLike(e.data):void 0}function checked(e){if(e>=**********)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|e}function SlowBuffer(e){return+e!=e&&(e=0),Buffer.alloc(+e)}function byteLength(e,i){if(Buffer.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||isInstance(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);var o=e.length,s=arguments.length>2&&!0===arguments[2];if(!s&&0===o)return 0;for(var h=!1;;)switch(i){case"ascii":case"latin1":case"binary":return o;case"utf8":case"utf-8":return utf8ToBytes(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*o;case"hex":return o>>>1;case"base64":return base64ToBytes(e).length;default:if(h)return s?-1:utf8ToBytes(e).length;i=(""+i).toLowerCase(),h=!0}}function slowToString(e,i,o){var s=!1;if((void 0===i||i<0)&&(i=0),i>this.length||((void 0===o||o>this.length)&&(o=this.length),o<=0||(o>>>=0)<=(i>>>=0)))return"";for(e||(e="utf8");;)switch(e){case"hex":return hexSlice(this,i,o);case"utf8":case"utf-8":return utf8Slice(this,i,o);case"ascii":return asciiSlice(this,i,o);case"latin1":case"binary":return latin1Slice(this,i,o);case"base64":return base64Slice(this,i,o);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return utf16leSlice(this,i,o);default:if(s)throw TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),s=!0}}function swap(e,i,o){var s=e[i];e[i]=e[o],e[o]=s}function bidirectionalIndexOf(e,i,o,s,h){var f;if(0===e.length)return -1;if("string"==typeof o?(s=o,o=0):o>**********?o=**********:o<-2147483648&&(o=-2147483648),(f=o=+o)!=f&&(o=h?0:e.length-1),o<0&&(o=e.length+o),o>=e.length){if(h)return -1;o=e.length-1}else if(o<0){if(!h)return -1;o=0}if("string"==typeof i&&(i=Buffer.from(i,s)),Buffer.isBuffer(i))return 0===i.length?-1:arrayIndexOf(e,i,o,s,h);if("number"==typeof i)return(i&=255,"function"==typeof Uint8Array.prototype.indexOf)?h?Uint8Array.prototype.indexOf.call(e,i,o):Uint8Array.prototype.lastIndexOf.call(e,i,o):arrayIndexOf(e,[i],o,s,h);throw TypeError("val must be string, number or Buffer")}function arrayIndexOf(e,i,o,s,h){var f,l=1,d=e.length,g=i.length;if(void 0!==s&&("ucs2"===(s=String(s).toLowerCase())||"ucs-2"===s||"utf16le"===s||"utf-16le"===s)){if(e.length<2||i.length<2)return -1;l=2,d/=2,g/=2,o/=2}function read(e,i){return 1===l?e[i]:e.readUInt16BE(i*l)}if(h){var b=-1;for(f=o;f<d;f++)if(read(e,f)===read(i,-1===b?0:f-b)){if(-1===b&&(b=f),f-b+1===g)return b*l}else -1!==b&&(f-=f-b),b=-1}else for(o+g>d&&(o=d-g),f=o;f>=0;f--){for(var w=!0,_=0;_<g;_++)if(read(e,f+_)!==read(i,_)){w=!1;break}if(w)return f}return -1}function hexWrite(e,i,o,s){o=Number(o)||0;var h=e.length-o;s?(s=Number(s))>h&&(s=h):s=h;var f=i.length;s>f/2&&(s=f/2);for(var l=0;l<s;++l){var d=parseInt(i.substr(2*l,2),16);if(d!=d)break;e[o+l]=d}return l}function utf8Write(e,i,o,s){return blitBuffer(utf8ToBytes(i,e.length-o),e,o,s)}function latin1Write(e,i,o,s){return blitBuffer(asciiToBytes(i),e,o,s)}function base64Write(e,i,o,s){return blitBuffer(base64ToBytes(i),e,o,s)}function ucs2Write(e,i,o,s){return blitBuffer(utf16leToBytes(i,e.length-o),e,o,s)}function base64Slice(e,i,o){return 0===i&&o===e.length?s.fromByteArray(e):s.fromByteArray(e.slice(i,o))}function utf8Slice(e,i,o){o=Math.min(e.length,o);for(var s=[],h=i;h<o;){var f,l,d,g,b=e[h],w=null,_=b>239?4:b>223?3:b>191?2:1;if(h+_<=o)switch(_){case 1:b<128&&(w=b);break;case 2:(192&(f=e[h+1]))==128&&(g=(31&b)<<6|63&f)>127&&(w=g);break;case 3:f=e[h+1],l=e[h+2],(192&f)==128&&(192&l)==128&&(g=(15&b)<<12|(63&f)<<6|63&l)>2047&&(g<55296||g>57343)&&(w=g);break;case 4:f=e[h+1],l=e[h+2],d=e[h+3],(192&f)==128&&(192&l)==128&&(192&d)==128&&(g=(15&b)<<18|(63&f)<<12|(63&l)<<6|63&d)>65535&&g<1114112&&(w=g)}null===w?(w=65533,_=1):w>65535&&(w-=65536,s.push(w>>>10&1023|55296),w=56320|1023&w),s.push(w),h+=_}return decodeCodePointsArray(s)}function decodeCodePointsArray(e){var i=e.length;if(i<=4096)return String.fromCharCode.apply(String,e);for(var o="",s=0;s<i;)o+=String.fromCharCode.apply(String,e.slice(s,s+=4096));return o}function asciiSlice(e,i,o){var s="";o=Math.min(e.length,o);for(var h=i;h<o;++h)s+=String.fromCharCode(127&e[h]);return s}function latin1Slice(e,i,o){var s="";o=Math.min(e.length,o);for(var h=i;h<o;++h)s+=String.fromCharCode(e[h]);return s}function hexSlice(e,i,o){var s=e.length;(!i||i<0)&&(i=0),(!o||o<0||o>s)&&(o=s);for(var h="",f=i;f<o;++f)h+=d[e[f]];return h}function utf16leSlice(e,i,o){for(var s=e.slice(i,o),h="",f=0;f<s.length;f+=2)h+=String.fromCharCode(s[f]+256*s[f+1]);return h}function checkOffset(e,i,o){if(e%1!=0||e<0)throw RangeError("offset is not uint");if(e+i>o)throw RangeError("Trying to access beyond buffer length")}function checkInt(e,i,o,s,h,f){if(!Buffer.isBuffer(e))throw TypeError('"buffer" argument must be a Buffer instance');if(i>h||i<f)throw RangeError('"value" argument is out of bounds');if(o+s>e.length)throw RangeError("Index out of range")}function checkIEEE754(e,i,o,s,h,f){if(o+s>e.length||o<0)throw RangeError("Index out of range")}function writeFloat(e,i,o,s,f){return i=+i,o>>>=0,f||checkIEEE754(e,i,o,4,34028234663852886e22,-34028234663852886e22),h.write(e,i,o,s,23,4),o+4}function writeDouble(e,i,o,s,f){return i=+i,o>>>=0,f||checkIEEE754(e,i,o,8,17976931348623157e292,-17976931348623157e292),h.write(e,i,o,s,52,8),o+8}i.Buffer=Buffer,i.SlowBuffer=SlowBuffer,i.INSPECT_MAX_BYTES=50,i.kMaxLength=**********,Buffer.TYPED_ARRAY_SUPPORT=typedArraySupport(),Buffer.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(Buffer.prototype,"parent",{enumerable:!0,get:function(){if(Buffer.isBuffer(this))return this.buffer}}),Object.defineProperty(Buffer.prototype,"offset",{enumerable:!0,get:function(){if(Buffer.isBuffer(this))return this.byteOffset}}),Buffer.poolSize=8192,Buffer.from=function(e,i,o){return from(e,i,o)},Object.setPrototypeOf(Buffer.prototype,Uint8Array.prototype),Object.setPrototypeOf(Buffer,Uint8Array),Buffer.alloc=function(e,i,o){return alloc(e,i,o)},Buffer.allocUnsafe=function(e){return allocUnsafe(e)},Buffer.allocUnsafeSlow=function(e){return allocUnsafe(e)},Buffer.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==Buffer.prototype},Buffer.compare=function(e,i){if(isInstance(e,Uint8Array)&&(e=Buffer.from(e,e.offset,e.byteLength)),isInstance(i,Uint8Array)&&(i=Buffer.from(i,i.offset,i.byteLength)),!Buffer.isBuffer(e)||!Buffer.isBuffer(i))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===i)return 0;for(var o=e.length,s=i.length,h=0,f=Math.min(o,s);h<f;++h)if(e[h]!==i[h]){o=e[h],s=i[h];break}return o<s?-1:s<o?1:0},Buffer.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},Buffer.concat=function(e,i){if(!Array.isArray(e))throw TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return Buffer.alloc(0);if(void 0===i)for(o=0,i=0;o<e.length;++o)i+=e[o].length;var o,s=Buffer.allocUnsafe(i),h=0;for(o=0;o<e.length;++o){var f=e[o];if(isInstance(f,Uint8Array)&&(f=Buffer.from(f)),!Buffer.isBuffer(f))throw TypeError('"list" argument must be an Array of Buffers');f.copy(s,h),h+=f.length}return s},Buffer.byteLength=byteLength,Buffer.prototype._isBuffer=!0,Buffer.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var i=0;i<e;i+=2)swap(this,i,i+1);return this},Buffer.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var i=0;i<e;i+=4)swap(this,i,i+3),swap(this,i+1,i+2);return this},Buffer.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var i=0;i<e;i+=8)swap(this,i,i+7),swap(this,i+1,i+6),swap(this,i+2,i+5),swap(this,i+3,i+4);return this},Buffer.prototype.toString=function(){var e=this.length;return 0===e?"":0==arguments.length?utf8Slice(this,0,e):slowToString.apply(this,arguments)},Buffer.prototype.toLocaleString=Buffer.prototype.toString,Buffer.prototype.equals=function(e){if(!Buffer.isBuffer(e))throw TypeError("Argument must be a Buffer");return this===e||0===Buffer.compare(this,e)},Buffer.prototype.inspect=function(){var e="",o=i.INSPECT_MAX_BYTES;return e=this.toString("hex",0,o).replace(/(.{2})/g,"$1 ").trim(),this.length>o&&(e+=" ... "),"<Buffer "+e+">"},f&&(Buffer.prototype[f]=Buffer.prototype.inspect),Buffer.prototype.compare=function(e,i,o,s,h){if(isInstance(e,Uint8Array)&&(e=Buffer.from(e,e.offset,e.byteLength)),!Buffer.isBuffer(e))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===i&&(i=0),void 0===o&&(o=e?e.length:0),void 0===s&&(s=0),void 0===h&&(h=this.length),i<0||o>e.length||s<0||h>this.length)throw RangeError("out of range index");if(s>=h&&i>=o)return 0;if(s>=h)return -1;if(i>=o)return 1;if(i>>>=0,o>>>=0,s>>>=0,h>>>=0,this===e)return 0;for(var f=h-s,l=o-i,d=Math.min(f,l),g=this.slice(s,h),b=e.slice(i,o),w=0;w<d;++w)if(g[w]!==b[w]){f=g[w],l=b[w];break}return f<l?-1:l<f?1:0},Buffer.prototype.includes=function(e,i,o){return -1!==this.indexOf(e,i,o)},Buffer.prototype.indexOf=function(e,i,o){return bidirectionalIndexOf(this,e,i,o,!0)},Buffer.prototype.lastIndexOf=function(e,i,o){return bidirectionalIndexOf(this,e,i,o,!1)},Buffer.prototype.write=function(e,i,o,s){if(void 0===i)s="utf8",o=this.length,i=0;else if(void 0===o&&"string"==typeof i)s=i,o=this.length,i=0;else if(isFinite(i))i>>>=0,isFinite(o)?(o>>>=0,void 0===s&&(s="utf8")):(s=o,o=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var h,f,l=this.length-i;if((void 0===o||o>l)&&(o=l),e.length>0&&(o<0||i<0)||i>this.length)throw RangeError("Attempt to write outside buffer bounds");s||(s="utf8");for(var d=!1;;)switch(s){case"hex":return hexWrite(this,e,i,o);case"utf8":case"utf-8":return utf8Write(this,e,i,o);case"ascii":return h=i,f=o,blitBuffer(asciiToBytes(e),this,h,f);case"latin1":case"binary":return latin1Write(this,e,i,o);case"base64":return base64Write(this,e,i,o);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return ucs2Write(this,e,i,o);default:if(d)throw TypeError("Unknown encoding: "+s);s=(""+s).toLowerCase(),d=!0}},Buffer.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},Buffer.prototype.slice=function(e,i){var o=this.length;e=~~e,i=void 0===i?o:~~i,e<0?(e+=o)<0&&(e=0):e>o&&(e=o),i<0?(i+=o)<0&&(i=0):i>o&&(i=o),i<e&&(i=e);var s=this.subarray(e,i);return Object.setPrototypeOf(s,Buffer.prototype),s},Buffer.prototype.readUIntLE=function(e,i,o){e>>>=0,i>>>=0,o||checkOffset(e,i,this.length);for(var s=this[e],h=1,f=0;++f<i&&(h*=256);)s+=this[e+f]*h;return s},Buffer.prototype.readUIntBE=function(e,i,o){e>>>=0,i>>>=0,o||checkOffset(e,i,this.length);for(var s=this[e+--i],h=1;i>0&&(h*=256);)s+=this[e+--i]*h;return s},Buffer.prototype.readUInt8=function(e,i){return e>>>=0,i||checkOffset(e,1,this.length),this[e]},Buffer.prototype.readUInt16LE=function(e,i){return e>>>=0,i||checkOffset(e,2,this.length),this[e]|this[e+1]<<8},Buffer.prototype.readUInt16BE=function(e,i){return e>>>=0,i||checkOffset(e,2,this.length),this[e]<<8|this[e+1]},Buffer.prototype.readUInt32LE=function(e,i){return e>>>=0,i||checkOffset(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},Buffer.prototype.readUInt32BE=function(e,i){return e>>>=0,i||checkOffset(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},Buffer.prototype.readIntLE=function(e,i,o){e>>>=0,i>>>=0,o||checkOffset(e,i,this.length);for(var s=this[e],h=1,f=0;++f<i&&(h*=256);)s+=this[e+f]*h;return s>=(h*=128)&&(s-=Math.pow(2,8*i)),s},Buffer.prototype.readIntBE=function(e,i,o){e>>>=0,i>>>=0,o||checkOffset(e,i,this.length);for(var s=i,h=1,f=this[e+--s];s>0&&(h*=256);)f+=this[e+--s]*h;return f>=(h*=128)&&(f-=Math.pow(2,8*i)),f},Buffer.prototype.readInt8=function(e,i){return(e>>>=0,i||checkOffset(e,1,this.length),128&this[e])?-((255-this[e]+1)*1):this[e]},Buffer.prototype.readInt16LE=function(e,i){e>>>=0,i||checkOffset(e,2,this.length);var o=this[e]|this[e+1]<<8;return 32768&o?4294901760|o:o},Buffer.prototype.readInt16BE=function(e,i){e>>>=0,i||checkOffset(e,2,this.length);var o=this[e+1]|this[e]<<8;return 32768&o?4294901760|o:o},Buffer.prototype.readInt32LE=function(e,i){return e>>>=0,i||checkOffset(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},Buffer.prototype.readInt32BE=function(e,i){return e>>>=0,i||checkOffset(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},Buffer.prototype.readFloatLE=function(e,i){return e>>>=0,i||checkOffset(e,4,this.length),h.read(this,e,!0,23,4)},Buffer.prototype.readFloatBE=function(e,i){return e>>>=0,i||checkOffset(e,4,this.length),h.read(this,e,!1,23,4)},Buffer.prototype.readDoubleLE=function(e,i){return e>>>=0,i||checkOffset(e,8,this.length),h.read(this,e,!0,52,8)},Buffer.prototype.readDoubleBE=function(e,i){return e>>>=0,i||checkOffset(e,8,this.length),h.read(this,e,!1,52,8)},Buffer.prototype.writeUIntLE=function(e,i,o,s){if(e=+e,i>>>=0,o>>>=0,!s){var h=Math.pow(2,8*o)-1;checkInt(this,e,i,o,h,0)}var f=1,l=0;for(this[i]=255&e;++l<o&&(f*=256);)this[i+l]=e/f&255;return i+o},Buffer.prototype.writeUIntBE=function(e,i,o,s){if(e=+e,i>>>=0,o>>>=0,!s){var h=Math.pow(2,8*o)-1;checkInt(this,e,i,o,h,0)}var f=o-1,l=1;for(this[i+f]=255&e;--f>=0&&(l*=256);)this[i+f]=e/l&255;return i+o},Buffer.prototype.writeUInt8=function(e,i,o){return e=+e,i>>>=0,o||checkInt(this,e,i,1,255,0),this[i]=255&e,i+1},Buffer.prototype.writeUInt16LE=function(e,i,o){return e=+e,i>>>=0,o||checkInt(this,e,i,2,65535,0),this[i]=255&e,this[i+1]=e>>>8,i+2},Buffer.prototype.writeUInt16BE=function(e,i,o){return e=+e,i>>>=0,o||checkInt(this,e,i,2,65535,0),this[i]=e>>>8,this[i+1]=255&e,i+2},Buffer.prototype.writeUInt32LE=function(e,i,o){return e=+e,i>>>=0,o||checkInt(this,e,i,4,4294967295,0),this[i+3]=e>>>24,this[i+2]=e>>>16,this[i+1]=e>>>8,this[i]=255&e,i+4},Buffer.prototype.writeUInt32BE=function(e,i,o){return e=+e,i>>>=0,o||checkInt(this,e,i,4,4294967295,0),this[i]=e>>>24,this[i+1]=e>>>16,this[i+2]=e>>>8,this[i+3]=255&e,i+4},Buffer.prototype.writeIntLE=function(e,i,o,s){if(e=+e,i>>>=0,!s){var h=Math.pow(2,8*o-1);checkInt(this,e,i,o,h-1,-h)}var f=0,l=1,d=0;for(this[i]=255&e;++f<o&&(l*=256);)e<0&&0===d&&0!==this[i+f-1]&&(d=1),this[i+f]=(e/l>>0)-d&255;return i+o},Buffer.prototype.writeIntBE=function(e,i,o,s){if(e=+e,i>>>=0,!s){var h=Math.pow(2,8*o-1);checkInt(this,e,i,o,h-1,-h)}var f=o-1,l=1,d=0;for(this[i+f]=255&e;--f>=0&&(l*=256);)e<0&&0===d&&0!==this[i+f+1]&&(d=1),this[i+f]=(e/l>>0)-d&255;return i+o},Buffer.prototype.writeInt8=function(e,i,o){return e=+e,i>>>=0,o||checkInt(this,e,i,1,127,-128),e<0&&(e=255+e+1),this[i]=255&e,i+1},Buffer.prototype.writeInt16LE=function(e,i,o){return e=+e,i>>>=0,o||checkInt(this,e,i,2,32767,-32768),this[i]=255&e,this[i+1]=e>>>8,i+2},Buffer.prototype.writeInt16BE=function(e,i,o){return e=+e,i>>>=0,o||checkInt(this,e,i,2,32767,-32768),this[i]=e>>>8,this[i+1]=255&e,i+2},Buffer.prototype.writeInt32LE=function(e,i,o){return e=+e,i>>>=0,o||checkInt(this,e,i,4,**********,-2147483648),this[i]=255&e,this[i+1]=e>>>8,this[i+2]=e>>>16,this[i+3]=e>>>24,i+4},Buffer.prototype.writeInt32BE=function(e,i,o){return e=+e,i>>>=0,o||checkInt(this,e,i,4,**********,-2147483648),e<0&&(e=4294967295+e+1),this[i]=e>>>24,this[i+1]=e>>>16,this[i+2]=e>>>8,this[i+3]=255&e,i+4},Buffer.prototype.writeFloatLE=function(e,i,o){return writeFloat(this,e,i,!0,o)},Buffer.prototype.writeFloatBE=function(e,i,o){return writeFloat(this,e,i,!1,o)},Buffer.prototype.writeDoubleLE=function(e,i,o){return writeDouble(this,e,i,!0,o)},Buffer.prototype.writeDoubleBE=function(e,i,o){return writeDouble(this,e,i,!1,o)},Buffer.prototype.copy=function(e,i,o,s){if(!Buffer.isBuffer(e))throw TypeError("argument should be a Buffer");if(o||(o=0),s||0===s||(s=this.length),i>=e.length&&(i=e.length),i||(i=0),s>0&&s<o&&(s=o),s===o||0===e.length||0===this.length)return 0;if(i<0)throw RangeError("targetStart out of bounds");if(o<0||o>=this.length)throw RangeError("Index out of range");if(s<0)throw RangeError("sourceEnd out of bounds");s>this.length&&(s=this.length),e.length-i<s-o&&(s=e.length-i+o);var h=s-o;if(this===e&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(i,o,s);else if(this===e&&o<i&&i<s)for(var f=h-1;f>=0;--f)e[f+i]=this[f+o];else Uint8Array.prototype.set.call(e,this.subarray(o,s),i);return h},Buffer.prototype.fill=function(e,i,o,s){if("string"==typeof e){if("string"==typeof i?(s=i,i=0,o=this.length):"string"==typeof o&&(s=o,o=this.length),void 0!==s&&"string"!=typeof s)throw TypeError("encoding must be a string");if("string"==typeof s&&!Buffer.isEncoding(s))throw TypeError("Unknown encoding: "+s);if(1===e.length){var h,f=e.charCodeAt(0);("utf8"===s&&f<128||"latin1"===s)&&(e=f)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(i<0||this.length<i||this.length<o)throw RangeError("Out of range index");if(o<=i)return this;if(i>>>=0,o=void 0===o?this.length:o>>>0,e||(e=0),"number"==typeof e)for(h=i;h<o;++h)this[h]=e;else{var l=Buffer.isBuffer(e)?e:Buffer.from(e,s),d=l.length;if(0===d)throw TypeError('The value "'+e+'" is invalid for argument "value"');for(h=0;h<o-i;++h)this[h+i]=l[h%d]}return this};var l=/[^+/0-9A-Za-z-_]/g;function base64clean(e){if((e=(e=e.split("=")[0]).trim().replace(l,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}function utf8ToBytes(e,i){i=i||1/0;for(var o,s=e.length,h=null,f=[],l=0;l<s;++l){if((o=e.charCodeAt(l))>55295&&o<57344){if(!h){if(o>56319||l+1===s){(i-=3)>-1&&f.push(239,191,189);continue}h=o;continue}if(o<56320){(i-=3)>-1&&f.push(239,191,189),h=o;continue}o=(h-55296<<10|o-56320)+65536}else h&&(i-=3)>-1&&f.push(239,191,189);if(h=null,o<128){if((i-=1)<0)break;f.push(o)}else if(o<2048){if((i-=2)<0)break;f.push(o>>6|192,63&o|128)}else if(o<65536){if((i-=3)<0)break;f.push(o>>12|224,o>>6&63|128,63&o|128)}else if(o<1114112){if((i-=4)<0)break;f.push(o>>18|240,o>>12&63|128,o>>6&63|128,63&o|128)}else throw Error("Invalid code point")}return f}function asciiToBytes(e){for(var i=[],o=0;o<e.length;++o)i.push(255&e.charCodeAt(o));return i}function utf16leToBytes(e,i){for(var o,s,h=[],f=0;f<e.length&&!((i-=2)<0);++f)s=(o=e.charCodeAt(f))>>8,h.push(o%256),h.push(s);return h}function base64ToBytes(e){return s.toByteArray(base64clean(e))}function blitBuffer(e,i,o,s){for(var h=0;h<s&&!(h+o>=i.length)&&!(h>=e.length);++h)i[h+o]=e[h];return h}function isInstance(e,i){return e instanceof i||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===i.name}var d=function(){for(var e="0123456789abcdef",i=Array(256),o=0;o<16;++o)for(var s=16*o,h=0;h<16;++h)i[s+h]=e[o]+e[h];return i}()},783:function(e,i){/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */i.read=function(e,i,o,s,h){var f,l,d=8*h-s-1,g=(1<<d)-1,b=g>>1,w=-7,_=o?h-1:0,O=o?-1:1,k=e[i+_];for(_+=O,f=k&(1<<-w)-1,k>>=-w,w+=d;w>0;f=256*f+e[i+_],_+=O,w-=8);for(l=f&(1<<-w)-1,f>>=-w,w+=s;w>0;l=256*l+e[i+_],_+=O,w-=8);if(0===f)f=1-b;else{if(f===g)return l?NaN:(k?-1:1)*(1/0);l+=Math.pow(2,s),f-=b}return(k?-1:1)*l*Math.pow(2,f-s)},i.write=function(e,i,o,s,h,f){var l,d,g,b=8*f-h-1,w=(1<<b)-1,_=w>>1,O=23===h?5960464477539062e-23:0,k=s?0:f-1,j=s?1:-1,$=i<0||0===i&&1/i<0?1:0;for(isNaN(i=Math.abs(i))||i===1/0?(d=isNaN(i)?1:0,l=w):(l=Math.floor(Math.log(i)/Math.LN2),i*(g=Math.pow(2,-l))<1&&(l--,g*=2),l+_>=1?i+=O/g:i+=O*Math.pow(2,1-_),i*g>=2&&(l++,g/=2),l+_>=w?(d=0,l=w):l+_>=1?(d=(i*g-1)*Math.pow(2,h),l+=_):(d=i*Math.pow(2,_-1)*Math.pow(2,h),l=0));h>=8;e[o+k]=255&d,k+=j,d/=256,h-=8);for(l=l<<h|d,b+=h;b>0;e[o+k]=255&l,k+=j,l/=256,b-=8);e[o+k-j]|=128*$}}},o={};function __nccwpck_require__(e){var s=o[e];if(void 0!==s)return s.exports;var h=o[e]={exports:{}},f=!0;try{i[e](h,h.exports,__nccwpck_require__),f=!1}finally{f&&delete o[e]}return h.exports}__nccwpck_require__.ab="//";var s=__nccwpck_require__(72);e.exports=s}()},8960:function(e){!function(){var i={229:function(e){var i,o,s,h=e.exports={};function defaultSetTimout(){throw Error("setTimeout has not been defined")}function defaultClearTimeout(){throw Error("clearTimeout has not been defined")}function runTimeout(e){if(i===setTimeout)return setTimeout(e,0);if((i===defaultSetTimout||!i)&&setTimeout)return i=setTimeout,setTimeout(e,0);try{return i(e,0)}catch(o){try{return i.call(null,e,0)}catch(o){return i.call(this,e,0)}}}function runClearTimeout(e){if(o===clearTimeout)return clearTimeout(e);if((o===defaultClearTimeout||!o)&&clearTimeout)return o=clearTimeout,clearTimeout(e);try{return o(e)}catch(i){try{return o.call(null,e)}catch(i){return o.call(this,e)}}}!function(){try{i="function"==typeof setTimeout?setTimeout:defaultSetTimout}catch(e){i=defaultSetTimout}try{o="function"==typeof clearTimeout?clearTimeout:defaultClearTimeout}catch(e){o=defaultClearTimeout}}();var f=[],l=!1,d=-1;function cleanUpNextTick(){l&&s&&(l=!1,s.length?f=s.concat(f):d=-1,f.length&&drainQueue())}function drainQueue(){if(!l){var e=runTimeout(cleanUpNextTick);l=!0;for(var i=f.length;i;){for(s=f,f=[];++d<i;)s&&s[d].run();d=-1,i=f.length}s=null,l=!1,runClearTimeout(e)}}function Item(e,i){this.fun=e,this.array=i}function noop(){}h.nextTick=function(e){var i=Array(arguments.length-1);if(arguments.length>1)for(var o=1;o<arguments.length;o++)i[o-1]=arguments[o];f.push(new Item(e,i)),1!==f.length||l||runTimeout(drainQueue)},Item.prototype.run=function(){this.fun.apply(null,this.array)},h.title="browser",h.browser=!0,h.env={},h.argv=[],h.version="",h.versions={},h.on=noop,h.addListener=noop,h.once=noop,h.off=noop,h.removeListener=noop,h.removeAllListeners=noop,h.emit=noop,h.prependListener=noop,h.prependOnceListener=noop,h.listeners=function(e){return[]},h.binding=function(e){throw Error("process.binding is not supported")},h.cwd=function(){return"/"},h.chdir=function(e){throw Error("process.chdir is not supported")},h.umask=function(){return 0}}},o={};function __nccwpck_require__(e){var s=o[e];if(void 0!==s)return s.exports;var h=o[e]={exports:{}},f=!0;try{i[e](h,h.exports,__nccwpck_require__),f=!1}finally{f&&delete o[e]}return h.exports}__nccwpck_require__.ab="//";var s=__nccwpck_require__(229);e.exports=s}()},622:function(e,i,o){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var s=o(2265),h=Symbol.for("react.element"),f=Symbol.for("react.fragment"),l=Object.prototype.hasOwnProperty,d=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,g={key:!0,ref:!0,__self:!0,__source:!0};function q(e,i,o){var s,f={},b=null,w=null;for(s in void 0!==o&&(b=""+o),void 0!==i.key&&(b=""+i.key),void 0!==i.ref&&(w=i.ref),i)l.call(i,s)&&!g.hasOwnProperty(s)&&(f[s]=i[s]);if(e&&e.defaultProps)for(s in i=e.defaultProps)void 0===f[s]&&(f[s]=i[s]);return{$$typeof:h,type:e,key:b,ref:w,props:f,_owner:d.current}}i.Fragment=f,i.jsx=q,i.jsxs=q},7437:function(e,i,o){"use strict";e.exports=o(622)},3304:function(e,i,o){"use strict";let s,h;o.d(i,{Jn:function(){return tf},qX:function(){return _getProvider},rh:function(){return _isFirebaseServerApp},Xd:function(){return _registerComponent},Mq:function(){return getApp},ZF:function(){return initializeApp},KN:function(){return registerVersion}});var f=o(3576),l=o(8650),d=o(4534);let instanceOfAny=(e,i)=>i.some(i=>e instanceof i);function getIdbProxyableTypes(){return s||(s=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])}function getCursorAdvanceMethods(){return h||(h=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])}let g=new WeakMap,b=new WeakMap,w=new WeakMap,_=new WeakMap,O=new WeakMap;function promisifyRequest(e){let i=new Promise((i,o)=>{let unlisten=()=>{e.removeEventListener("success",success),e.removeEventListener("error",error)},success=()=>{i(wrap_idb_value_wrap(e.result)),unlisten()},error=()=>{o(e.error),unlisten()};e.addEventListener("success",success),e.addEventListener("error",error)});return i.then(i=>{i instanceof IDBCursor&&g.set(i,e)}).catch(()=>{}),O.set(i,e),i}function cacheDonePromiseForTransaction(e){if(b.has(e))return;let i=new Promise((i,o)=>{let unlisten=()=>{e.removeEventListener("complete",complete),e.removeEventListener("error",error),e.removeEventListener("abort",error)},complete=()=>{i(),unlisten()},error=()=>{o(e.error||new DOMException("AbortError","AbortError")),unlisten()};e.addEventListener("complete",complete),e.addEventListener("error",error),e.addEventListener("abort",error)});b.set(e,i)}let k={get(e,i,o){if(e instanceof IDBTransaction){if("done"===i)return b.get(e);if("objectStoreNames"===i)return e.objectStoreNames||w.get(e);if("store"===i)return o.objectStoreNames[1]?void 0:o.objectStore(o.objectStoreNames[0])}return wrap_idb_value_wrap(e[i])},set:(e,i,o)=>(e[i]=o,!0),has:(e,i)=>e instanceof IDBTransaction&&("done"===i||"store"===i)||i in e};function replaceTraps(e){k=e(k)}function wrapFunction(e){return e!==IDBDatabase.prototype.transaction||"objectStoreNames"in IDBTransaction.prototype?getCursorAdvanceMethods().includes(e)?function(...i){return e.apply(unwrap(this),i),wrap_idb_value_wrap(g.get(this))}:function(...i){return wrap_idb_value_wrap(e.apply(unwrap(this),i))}:function(i,...o){let s=e.call(unwrap(this),i,...o);return w.set(s,i.sort?i.sort():[i]),wrap_idb_value_wrap(s)}}function transformCachableValue(e){return"function"==typeof e?wrapFunction(e):(e instanceof IDBTransaction&&cacheDonePromiseForTransaction(e),instanceOfAny(e,getIdbProxyableTypes()))?new Proxy(e,k):e}function wrap_idb_value_wrap(e){if(e instanceof IDBRequest)return promisifyRequest(e);if(_.has(e))return _.get(e);let i=transformCachableValue(e);return i!==e&&(_.set(e,i),O.set(i,e)),i}let unwrap=e=>O.get(e);function openDB(e,i,{blocked:o,upgrade:s,blocking:h,terminated:f}={}){let l=indexedDB.open(e,i),d=wrap_idb_value_wrap(l);return s&&l.addEventListener("upgradeneeded",e=>{s(wrap_idb_value_wrap(l.result),e.oldVersion,e.newVersion,wrap_idb_value_wrap(l.transaction),e)}),o&&l.addEventListener("blocked",e=>o(e.oldVersion,e.newVersion,e)),d.then(e=>{f&&e.addEventListener("close",()=>f()),h&&e.addEventListener("versionchange",e=>h(e.oldVersion,e.newVersion,e))}).catch(()=>{}),d}let j=["get","getKey","getAll","getAllKeys","count"],$=["put","add","delete","clear"],tt=new Map;function getMethod(e,i){if(!(e instanceof IDBDatabase&&!(i in e)&&"string"==typeof i))return;if(tt.get(i))return tt.get(i);let o=i.replace(/FromIndex$/,""),s=i!==o,h=$.includes(o);if(!(o in(s?IDBIndex:IDBObjectStore).prototype)||!(h||j.includes(o)))return;let method=async function(e,...i){let f=this.transaction(e,h?"readwrite":"readonly"),l=f.store;return s&&(l=l.index(i.shift())),(await Promise.all([l[o](...i),h&&f.done]))[0]};return tt.set(i,method),method}replaceTraps(e=>({...e,get:(i,o,s)=>getMethod(i,o)||e.get(i,o,s),has:(i,o)=>!!getMethod(i,o)||e.has(i,o)}));/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let PlatformLoggerServiceImpl=class PlatformLoggerServiceImpl{constructor(e){this.container=e}getPlatformInfoString(){let e=this.container.getProviders();return e.map(e=>{if(!isVersionServiceProvider(e))return null;{let i=e.getImmediate();return`${i.library}/${i.version}`}}).filter(e=>e).join(" ")}};function isVersionServiceProvider(e){let i=e.getComponent();return i?.type==="VERSION"}let te="@firebase/app",tr="0.14.0",tn=new l.Yd("@firebase/app"),ti="[DEFAULT]",to={[te]:"fire-core","@firebase/app-compat":"fire-core-compat","@firebase/analytics":"fire-analytics","@firebase/analytics-compat":"fire-analytics-compat","@firebase/app-check":"fire-app-check","@firebase/app-check-compat":"fire-app-check-compat","@firebase/auth":"fire-auth","@firebase/auth-compat":"fire-auth-compat","@firebase/database":"fire-rtdb","@firebase/data-connect":"fire-data-connect","@firebase/database-compat":"fire-rtdb-compat","@firebase/functions":"fire-fn","@firebase/functions-compat":"fire-fn-compat","@firebase/installations":"fire-iid","@firebase/installations-compat":"fire-iid-compat","@firebase/messaging":"fire-fcm","@firebase/messaging-compat":"fire-fcm-compat","@firebase/performance":"fire-perf","@firebase/performance-compat":"fire-perf-compat","@firebase/remote-config":"fire-rc","@firebase/remote-config-compat":"fire-rc-compat","@firebase/storage":"fire-gcs","@firebase/storage-compat":"fire-gcs-compat","@firebase/firestore":"fire-fst","@firebase/firestore-compat":"fire-fst-compat","@firebase/ai":"fire-vertex","fire-js":"fire-js",firebase:"fire-js-all"},ts=new Map,ta=new Map,th=new Map;function _addComponent(e,i){try{e.container.addComponent(i)}catch(o){tn.debug(`Component ${i.name} failed to register with FirebaseApp ${e.name}`,o)}}function _registerComponent(e){let i=e.name;if(th.has(i))return tn.debug(`There were multiple attempts to register component ${i}.`),!1;for(let o of(th.set(i,e),ts.values()))_addComponent(o,e);for(let i of ta.values())_addComponent(i,e);return!0}function _getProvider(e,i){let o=e.container.getProvider("heartbeat").getImmediate({optional:!0});return o&&o.triggerHeartbeat(),e.container.getProvider(i)}function _isFirebaseServerApp(e){return null!=e&&void 0!==e.settings}let tu=new d.LL("app","Firebase",{"no-app":"No Firebase App '{$appName}' has been created - call initializeApp() first","bad-app-name":"Illegal App name: '{$appName}'","duplicate-app":"Firebase App named '{$appName}' already exists with different options or config","app-deleted":"Firebase App named '{$appName}' already deleted","server-app-deleted":"Firebase Server App has been deleted","no-options":"Need to provide options, when not being deployed to hosting via source.","invalid-app-argument":"firebase.{$appName}() takes either no argument or a Firebase App instance.","invalid-log-argument":"First argument to `onLog` must be null or a function.","idb-open":"Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.","idb-get":"Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.","idb-set":"Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.","idb-delete":"Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.","finalization-registry-not-supported":"FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.","invalid-server-app-environment":"FirebaseServerApp is not for use in browser environments."});/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let FirebaseAppImpl=class FirebaseAppImpl{constructor(e,i,o){this._isDeleted=!1,this._options={...e},this._config={...i},this._name=i.name,this._automaticDataCollectionEnabled=i.automaticDataCollectionEnabled,this._container=o,this.container.addComponent(new f.wA("app",()=>this,"PUBLIC"))}get automaticDataCollectionEnabled(){return this.checkDestroyed(),this._automaticDataCollectionEnabled}set automaticDataCollectionEnabled(e){this.checkDestroyed(),this._automaticDataCollectionEnabled=e}get name(){return this.checkDestroyed(),this._name}get options(){return this.checkDestroyed(),this._options}get config(){return this.checkDestroyed(),this._config}get container(){return this._container}get isDeleted(){return this._isDeleted}set isDeleted(e){this._isDeleted=e}checkDestroyed(){if(this.isDeleted)throw tu.create("app-deleted",{appName:this._name})}};/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let tf="12.0.0";function initializeApp(e,i={}){let o=e;if("object"!=typeof i){let e=i;i={name:e}}let s={name:ti,automaticDataCollectionEnabled:!0,...i},h=s.name;if("string"!=typeof h||!h)throw tu.create("bad-app-name",{appName:String(h)});if(o||(o=(0,d.aH)()),!o)throw tu.create("no-options");let l=ts.get(h);if(l){if((0,d.vZ)(o,l.options)&&(0,d.vZ)(s,l.config))return l;throw tu.create("duplicate-app",{appName:h})}let g=new f.H0(h);for(let e of th.values())g.addComponent(e);let b=new FirebaseAppImpl(o,s,g);return ts.set(h,b),b}function getApp(e=ti){let i=ts.get(e);if(!i&&e===ti&&(0,d.aH)())return initializeApp();if(!i)throw tu.create("no-app",{appName:e});return i}function registerVersion(e,i,o){let s=to[e]??e;o&&(s+=`-${o}`);let h=s.match(/\s|\//),l=i.match(/\s|\//);if(h||l){let e=[`Unable to register library "${s}" with version "${i}":`];h&&e.push(`library name "${s}" contains illegal characters (whitespace or "/")`),h&&l&&e.push("and"),l&&e.push(`version name "${i}" contains illegal characters (whitespace or "/")`),tn.warn(e.join(" "));return}_registerComponent(new f.wA(`${s}-version`,()=>({library:s,version:i}),"VERSION"))}let tl="firebase-heartbeat-store",tp=null;function getDbPromise(){return tp||(tp=openDB("firebase-heartbeat-database",1,{upgrade:(e,i)=>{if(0===i)try{e.createObjectStore(tl)}catch(e){console.warn(e)}}}).catch(e=>{throw tu.create("idb-open",{originalErrorMessage:e.message})})),tp}async function readHeartbeatsFromIndexedDB(e){try{let i=await getDbPromise(),o=i.transaction(tl),s=await o.objectStore(tl).get(computeKey(e));return await o.done,s}catch(e){if(e instanceof d.ZR)tn.warn(e.message);else{let i=tu.create("idb-get",{originalErrorMessage:e?.message});tn.warn(i.message)}}}async function writeHeartbeatsToIndexedDB(e,i){try{let o=await getDbPromise(),s=o.transaction(tl,"readwrite"),h=s.objectStore(tl);await h.put(i,computeKey(e)),await s.done}catch(e){if(e instanceof d.ZR)tn.warn(e.message);else{let i=tu.create("idb-set",{originalErrorMessage:e?.message});tn.warn(i.message)}}}function computeKey(e){return`${e.name}!${e.options.appId}`}let HeartbeatServiceImpl=class HeartbeatServiceImpl{constructor(e){this.container=e,this._heartbeatsCache=null;let i=this.container.getProvider("app").getImmediate();this._storage=new HeartbeatStorageImpl(i),this._heartbeatsCachePromise=this._storage.read().then(e=>(this._heartbeatsCache=e,e))}async triggerHeartbeat(){try{let e=this.container.getProvider("platform-logger").getImmediate(),i=e.getPlatformInfoString(),o=getUTCDateString();if(this._heartbeatsCache?.heartbeats==null&&(this._heartbeatsCache=await this._heartbeatsCachePromise,this._heartbeatsCache?.heartbeats==null)||this._heartbeatsCache.lastSentHeartbeatDate===o||this._heartbeatsCache.heartbeats.some(e=>e.date===o))return;if(this._heartbeatsCache.heartbeats.push({date:o,agent:i}),this._heartbeatsCache.heartbeats.length>30){let e=getEarliestHeartbeatIdx(this._heartbeatsCache.heartbeats);this._heartbeatsCache.heartbeats.splice(e,1)}return this._storage.overwrite(this._heartbeatsCache)}catch(e){tn.warn(e)}}async getHeartbeatsHeader(){try{if(null===this._heartbeatsCache&&await this._heartbeatsCachePromise,this._heartbeatsCache?.heartbeats==null||0===this._heartbeatsCache.heartbeats.length)return"";let e=getUTCDateString(),{heartbeatsToSend:i,unsentEntries:o}=extractHeartbeatsForHeader(this._heartbeatsCache.heartbeats),s=(0,d.L)(JSON.stringify({version:2,heartbeats:i}));return this._heartbeatsCache.lastSentHeartbeatDate=e,o.length>0?(this._heartbeatsCache.heartbeats=o,await this._storage.overwrite(this._heartbeatsCache)):(this._heartbeatsCache.heartbeats=[],this._storage.overwrite(this._heartbeatsCache)),s}catch(e){return tn.warn(e),""}}};function getUTCDateString(){let e=new Date;return e.toISOString().substring(0,10)}function extractHeartbeatsForHeader(e,i=1024){let o=[],s=e.slice();for(let h of e){let e=o.find(e=>e.agent===h.agent);if(e){if(e.dates.push(h.date),countBytes(o)>i){e.dates.pop();break}}else if(o.push({agent:h.agent,dates:[h.date]}),countBytes(o)>i){o.pop();break}s=s.slice(1)}return{heartbeatsToSend:o,unsentEntries:s}}let HeartbeatStorageImpl=class HeartbeatStorageImpl{constructor(e){this.app=e,this._canUseIndexedDBPromise=this.runIndexedDBEnvironmentCheck()}async runIndexedDBEnvironmentCheck(){return!!(0,d.hl)()&&(0,d.eu)().then(()=>!0).catch(()=>!1)}async read(){let e=await this._canUseIndexedDBPromise;if(!e)return{heartbeats:[]};{let e=await readHeartbeatsFromIndexedDB(this.app);return e?.heartbeats?e:{heartbeats:[]}}}async overwrite(e){let i=await this._canUseIndexedDBPromise;if(i){let i=await this.read();return writeHeartbeatsToIndexedDB(this.app,{lastSentHeartbeatDate:e.lastSentHeartbeatDate??i.lastSentHeartbeatDate,heartbeats:e.heartbeats})}}async add(e){let i=await this._canUseIndexedDBPromise;if(i){let i=await this.read();return writeHeartbeatsToIndexedDB(this.app,{lastSentHeartbeatDate:e.lastSentHeartbeatDate??i.lastSentHeartbeatDate,heartbeats:[...i.heartbeats,...e.heartbeats]})}}};function countBytes(e){return(0,d.L)(JSON.stringify({version:2,heartbeats:e})).length}function getEarliestHeartbeatIdx(e){if(0===e.length)return -1;let i=0,o=e[0].date;for(let s=1;s<e.length;s++)e[s].date<o&&(o=e[s].date,i=s);return i}_registerComponent(new f.wA("platform-logger",e=>new PlatformLoggerServiceImpl(e),"PRIVATE")),_registerComponent(new f.wA("heartbeat",e=>new HeartbeatServiceImpl(e),"PRIVATE")),registerVersion(te,tr,""),registerVersion(te,tr,"esm2020"),registerVersion("fire-js","")},3576:function(e,i,o){"use strict";o.d(i,{H0:function(){return ComponentContainer},wA:function(){return Component}});var s=o(4534);let Component=class Component{constructor(e,i,o){this.name=e,this.instanceFactory=i,this.type=o,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}};/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let h="[DEFAULT]";/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let Provider=class Provider{constructor(e,i){this.name=e,this.container=i,this.component=null,this.instances=new Map,this.instancesDeferred=new Map,this.instancesOptions=new Map,this.onInitCallbacks=new Map}get(e){let i=this.normalizeInstanceIdentifier(e);if(!this.instancesDeferred.has(i)){let e=new s.BH;if(this.instancesDeferred.set(i,e),this.isInitialized(i)||this.shouldAutoInitialize())try{let o=this.getOrInitializeService({instanceIdentifier:i});o&&e.resolve(o)}catch(e){}}return this.instancesDeferred.get(i).promise}getImmediate(e){let i=this.normalizeInstanceIdentifier(e?.identifier),o=e?.optional??!1;if(this.isInitialized(i)||this.shouldAutoInitialize())try{return this.getOrInitializeService({instanceIdentifier:i})}catch(e){if(o)return null;throw e}else{if(o)return null;throw Error(`Service ${this.name} is not available`)}}getComponent(){return this.component}setComponent(e){if(e.name!==this.name)throw Error(`Mismatching Component ${e.name} for Provider ${this.name}.`);if(this.component)throw Error(`Component for ${this.name} has already been provided`);if(this.component=e,this.shouldAutoInitialize()){if(isComponentEager(e))try{this.getOrInitializeService({instanceIdentifier:h})}catch(e){}for(let[e,i]of this.instancesDeferred.entries()){let o=this.normalizeInstanceIdentifier(e);try{let e=this.getOrInitializeService({instanceIdentifier:o});i.resolve(e)}catch(e){}}}}clearInstance(e=h){this.instancesDeferred.delete(e),this.instancesOptions.delete(e),this.instances.delete(e)}async delete(){let e=Array.from(this.instances.values());await Promise.all([...e.filter(e=>"INTERNAL"in e).map(e=>e.INTERNAL.delete()),...e.filter(e=>"_delete"in e).map(e=>e._delete())])}isComponentSet(){return null!=this.component}isInitialized(e=h){return this.instances.has(e)}getOptions(e=h){return this.instancesOptions.get(e)||{}}initialize(e={}){let{options:i={}}=e,o=this.normalizeInstanceIdentifier(e.instanceIdentifier);if(this.isInitialized(o))throw Error(`${this.name}(${o}) has already been initialized`);if(!this.isComponentSet())throw Error(`Component ${this.name} has not been registered yet`);let s=this.getOrInitializeService({instanceIdentifier:o,options:i});for(let[e,i]of this.instancesDeferred.entries()){let h=this.normalizeInstanceIdentifier(e);o===h&&i.resolve(s)}return s}onInit(e,i){let o=this.normalizeInstanceIdentifier(i),s=this.onInitCallbacks.get(o)??new Set;s.add(e),this.onInitCallbacks.set(o,s);let h=this.instances.get(o);return h&&e(h,o),()=>{s.delete(e)}}invokeOnInitCallbacks(e,i){let o=this.onInitCallbacks.get(i);if(o)for(let s of o)try{s(e,i)}catch{}}getOrInitializeService({instanceIdentifier:e,options:i={}}){let o=this.instances.get(e);if(!o&&this.component&&(o=this.component.instanceFactory(this.container,{instanceIdentifier:normalizeIdentifierForFactory(e),options:i}),this.instances.set(e,o),this.instancesOptions.set(e,i),this.invokeOnInitCallbacks(o,e),this.component.onInstanceCreated))try{this.component.onInstanceCreated(this.container,e,o)}catch{}return o||null}normalizeInstanceIdentifier(e=h){return this.component?this.component.multipleInstances?e:h:e}shouldAutoInitialize(){return!!this.component&&"EXPLICIT"!==this.component.instantiationMode}};function normalizeIdentifierForFactory(e){return e===h?void 0:e}function isComponentEager(e){return"EAGER"===e.instantiationMode}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let ComponentContainer=class ComponentContainer{constructor(e){this.name=e,this.providers=new Map}addComponent(e){let i=this.getProvider(e.name);if(i.isComponentSet())throw Error(`Component ${e.name} has already been registered with ${this.name}`);i.setComponent(e)}addOrOverwriteComponent(e){let i=this.getProvider(e.name);i.isComponentSet()&&this.providers.delete(e.name),this.addComponent(e)}getProvider(e){if(this.providers.has(e))return this.providers.get(e);let i=new Provider(e,this);return this.providers.set(e,i),i}getProviders(){return Array.from(this.providers.values())}}},8650:function(e,i,o){"use strict";var s,h;o.d(i,{Yd:function(){return Logger},in:function(){return s}});/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let f=[];(h=s||(s={}))[h.DEBUG=0]="DEBUG",h[h.VERBOSE=1]="VERBOSE",h[h.INFO=2]="INFO",h[h.WARN=3]="WARN",h[h.ERROR=4]="ERROR",h[h.SILENT=5]="SILENT";let l={debug:s.DEBUG,verbose:s.VERBOSE,info:s.INFO,warn:s.WARN,error:s.ERROR,silent:s.SILENT},d=s.INFO,g={[s.DEBUG]:"log",[s.VERBOSE]:"log",[s.INFO]:"info",[s.WARN]:"warn",[s.ERROR]:"error"},defaultLogHandler=(e,i,...o)=>{if(i<e.logLevel)return;let s=new Date().toISOString(),h=g[i];if(h)console[h](`[${s}]  ${e.name}:`,...o);else throw Error(`Attempted to log a message with an invalid logType (value: ${i})`)};let Logger=class Logger{constructor(e){this.name=e,this._logLevel=d,this._logHandler=defaultLogHandler,this._userLogHandler=null,f.push(this)}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in s))throw TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel="string"==typeof e?l[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if("function"!=typeof e)throw TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,s.DEBUG,...e),this._logHandler(this,s.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,s.VERBOSE,...e),this._logHandler(this,s.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,s.INFO,...e),this._logHandler(this,s.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,s.WARN,...e),this._logHandler(this,s.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,s.ERROR,...e),this._logHandler(this,s.ERROR,...e)}}},3172:function(e,i,o){"use strict";o.d(i,{V8:function(){return h},z8:function(){return s}});var s,h,f="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},l={};(function(){function m(){this.blockSize=-1,this.blockSize=64,this.g=[,,,,],this.B=Array(this.blockSize),this.o=this.h=0,this.s()}function n(e,i,o){o||(o=0);var s=Array(16);if("string"==typeof i)for(var h=0;16>h;++h)s[h]=i.charCodeAt(o++)|i.charCodeAt(o++)<<8|i.charCodeAt(o++)<<16|i.charCodeAt(o++)<<24;else for(h=0;16>h;++h)s[h]=i[o++]|i[o++]<<8|i[o++]<<16|i[o++]<<24;i=e.g[0],o=e.g[1],h=e.g[2];var f=e.g[3],l=i+(f^o&(h^f))+s[0]+3614090360&4294967295;l=f+(h^(i=o+(l<<7&4294967295|l>>>25))&(o^h))+s[1]+3905402710&4294967295,l=h+(o^(f=i+(l<<12&4294967295|l>>>20))&(i^o))+s[2]+606105819&4294967295,l=o+(i^(h=f+(l<<17&4294967295|l>>>15))&(f^i))+s[3]+3250441966&4294967295,l=i+(f^(o=h+(l<<22&4294967295|l>>>10))&(h^f))+s[4]+4118548399&4294967295,l=f+(h^(i=o+(l<<7&4294967295|l>>>25))&(o^h))+s[5]+1200080426&4294967295,l=h+(o^(f=i+(l<<12&4294967295|l>>>20))&(i^o))+s[6]+2821735955&4294967295,l=o+(i^(h=f+(l<<17&4294967295|l>>>15))&(f^i))+s[7]+4249261313&4294967295,l=i+(f^(o=h+(l<<22&4294967295|l>>>10))&(h^f))+s[8]+1770035416&4294967295,l=f+(h^(i=o+(l<<7&4294967295|l>>>25))&(o^h))+s[9]+2336552879&4294967295,l=h+(o^(f=i+(l<<12&4294967295|l>>>20))&(i^o))+s[10]+4294925233&4294967295,l=o+(i^(h=f+(l<<17&4294967295|l>>>15))&(f^i))+s[11]+2304563134&4294967295,l=i+(f^(o=h+(l<<22&4294967295|l>>>10))&(h^f))+s[12]+1804603682&4294967295,l=f+(h^(i=o+(l<<7&4294967295|l>>>25))&(o^h))+s[13]+4254626195&4294967295,l=h+(o^(f=i+(l<<12&4294967295|l>>>20))&(i^o))+s[14]+2792965006&4294967295,l=o+(i^(h=f+(l<<17&4294967295|l>>>15))&(f^i))+s[15]+1236535329&4294967295,o=h+(l<<22&4294967295|l>>>10),l=i+(h^f&(o^h))+s[1]+4129170786&4294967295,i=o+(l<<5&4294967295|l>>>27),l=f+(o^h&(i^o))+s[6]+3225465664&4294967295,f=i+(l<<9&4294967295|l>>>23),l=h+(i^o&(f^i))+s[11]+643717713&4294967295,h=f+(l<<14&4294967295|l>>>18),l=o+(f^i&(h^f))+s[0]+3921069994&4294967295,o=h+(l<<20&4294967295|l>>>12),l=i+(h^f&(o^h))+s[5]+3593408605&4294967295,i=o+(l<<5&4294967295|l>>>27),l=f+(o^h&(i^o))+s[10]+38016083&4294967295,f=i+(l<<9&4294967295|l>>>23),l=h+(i^o&(f^i))+s[15]+3634488961&4294967295,h=f+(l<<14&4294967295|l>>>18),l=o+(f^i&(h^f))+s[4]+3889429448&4294967295,o=h+(l<<20&4294967295|l>>>12),l=i+(h^f&(o^h))+s[9]+568446438&4294967295,i=o+(l<<5&4294967295|l>>>27),l=f+(o^h&(i^o))+s[14]+3275163606&4294967295,f=i+(l<<9&4294967295|l>>>23),l=h+(i^o&(f^i))+s[3]+4107603335&4294967295,h=f+(l<<14&4294967295|l>>>18),l=o+(f^i&(h^f))+s[8]+1163531501&4294967295,o=h+(l<<20&4294967295|l>>>12),l=i+(h^f&(o^h))+s[13]+2850285829&4294967295,i=o+(l<<5&4294967295|l>>>27),l=f+(o^h&(i^o))+s[2]+4243563512&4294967295,f=i+(l<<9&4294967295|l>>>23),l=h+(i^o&(f^i))+s[7]+1735328473&4294967295,h=f+(l<<14&4294967295|l>>>18),l=o+(f^i&(h^f))+s[12]+2368359562&4294967295,l=i+((o=h+(l<<20&4294967295|l>>>12))^h^f)+s[5]+4294588738&4294967295,l=f+((i=o+(l<<4&4294967295|l>>>28))^o^h)+s[8]+2272392833&4294967295,l=h+((f=i+(l<<11&4294967295|l>>>21))^i^o)+s[11]+1839030562&4294967295,l=o+((h=f+(l<<16&4294967295|l>>>16))^f^i)+s[14]+4259657740&4294967295,l=i+((o=h+(l<<23&4294967295|l>>>9))^h^f)+s[1]+2763975236&4294967295,l=f+((i=o+(l<<4&4294967295|l>>>28))^o^h)+s[4]+1272893353&4294967295,l=h+((f=i+(l<<11&4294967295|l>>>21))^i^o)+s[7]+4139469664&4294967295,l=o+((h=f+(l<<16&4294967295|l>>>16))^f^i)+s[10]+3200236656&4294967295,l=i+((o=h+(l<<23&4294967295|l>>>9))^h^f)+s[13]+681279174&4294967295,l=f+((i=o+(l<<4&4294967295|l>>>28))^o^h)+s[0]+3936430074&4294967295,l=h+((f=i+(l<<11&4294967295|l>>>21))^i^o)+s[3]+3572445317&4294967295,l=o+((h=f+(l<<16&4294967295|l>>>16))^f^i)+s[6]+76029189&4294967295,l=i+((o=h+(l<<23&4294967295|l>>>9))^h^f)+s[9]+3654602809&4294967295,l=f+((i=o+(l<<4&4294967295|l>>>28))^o^h)+s[12]+3873151461&4294967295,l=h+((f=i+(l<<11&4294967295|l>>>21))^i^o)+s[15]+530742520&4294967295,l=o+((h=f+(l<<16&4294967295|l>>>16))^f^i)+s[2]+3299628645&4294967295,o=h+(l<<23&4294967295|l>>>9),l=i+(h^(o|~f))+s[0]+4096336452&4294967295,i=o+(l<<6&4294967295|l>>>26),l=f+(o^(i|~h))+s[7]+1126891415&4294967295,f=i+(l<<10&4294967295|l>>>22),l=h+(i^(f|~o))+s[14]+2878612391&4294967295,h=f+(l<<15&4294967295|l>>>17),l=o+(f^(h|~i))+s[5]+4237533241&4294967295,o=h+(l<<21&4294967295|l>>>11),l=i+(h^(o|~f))+s[12]+1700485571&4294967295,i=o+(l<<6&4294967295|l>>>26),l=f+(o^(i|~h))+s[3]+2399980690&4294967295,f=i+(l<<10&4294967295|l>>>22),l=h+(i^(f|~o))+s[10]+4293915773&4294967295,h=f+(l<<15&4294967295|l>>>17),l=o+(f^(h|~i))+s[1]+2240044497&4294967295,o=h+(l<<21&4294967295|l>>>11),l=i+(h^(o|~f))+s[8]+1873313359&4294967295,i=o+(l<<6&4294967295|l>>>26),l=f+(o^(i|~h))+s[15]+4264355552&4294967295,f=i+(l<<10&4294967295|l>>>22),l=h+(i^(f|~o))+s[6]+2734768916&4294967295,h=f+(l<<15&4294967295|l>>>17),l=o+(f^(h|~i))+s[13]+1309151649&4294967295,o=h+(l<<21&4294967295|l>>>11),l=i+(h^(o|~f))+s[4]+4149444226&4294967295,i=o+(l<<6&4294967295|l>>>26),l=f+(o^(i|~h))+s[11]+3174756917&4294967295,f=i+(l<<10&4294967295|l>>>22),l=h+(i^(f|~o))+s[2]+718787259&4294967295,h=f+(l<<15&4294967295|l>>>17),l=o+(f^(h|~i))+s[9]+3951481745&4294967295,e.g[0]=e.g[0]+i&4294967295,e.g[1]=e.g[1]+(h+(l<<21&4294967295|l>>>11))&4294967295,e.g[2]=e.g[2]+h&4294967295,e.g[3]=e.g[3]+f&4294967295}function p(e,o){return Object.prototype.hasOwnProperty.call(i,e)?i[e]:i[e]=o(e)}function t(e,i){this.h=i;for(var o=[],s=!0,h=e.length-1;0<=h;h--){var f=0|e[h];s&&f==i||(o[h]=f,s=!1)}this.g=o}(function(e,i){function c(){}c.prototype=i.prototype,e.D=i.prototype,e.prototype=new c,e.prototype.constructor=e,e.C=function(e,o,s){for(var h=Array(arguments.length-2),f=2;f<arguments.length;f++)h[f-2]=arguments[f];return i.prototype[o].apply(e,h)}})(m,function(){this.blockSize=-1}),m.prototype.s=function(){this.g[0]=1732584193,this.g[1]=4023233417,this.g[2]=2562383102,this.g[3]=271733878,this.o=this.h=0},m.prototype.u=function(e,i){void 0===i&&(i=e.length);for(var o=i-this.blockSize,s=this.B,h=this.h,f=0;f<i;){if(0==h)for(;f<=o;)n(this,e,f),f+=this.blockSize;if("string"==typeof e){for(;f<i;)if(s[h++]=e.charCodeAt(f++),h==this.blockSize){n(this,s),h=0;break}}else for(;f<i;)if(s[h++]=e[f++],h==this.blockSize){n(this,s),h=0;break}}this.h=h,this.o+=i},m.prototype.v=function(){var e=Array((56>this.h?this.blockSize:2*this.blockSize)-this.h);e[0]=128;for(var i=1;i<e.length-8;++i)e[i]=0;var o=8*this.o;for(i=e.length-8;i<e.length;++i)e[i]=255&o,o/=256;for(this.u(e),e=Array(16),i=o=0;4>i;++i)for(var s=0;32>s;s+=8)e[o++]=this.g[i]>>>s&255;return e};var e,i={};function u(e){return -128<=e&&128>e?p(e,function(e){return new t([0|e],0>e?-1:0)}):new t([0|e],0>e?-1:0)}function v(e){if(isNaN(e)||!isFinite(e))return o;if(0>e)return x(v(-e));for(var i=[],s=1,h=0;e>=s;h++)i[h]=e/s|0,s*=4294967296;return new t(i,0)}function y(e,i){if(0==e.length)throw Error("number format error: empty string");if(2>(i=i||10)||36<i)throw Error("radix out of range: "+i);if("-"==e.charAt(0))return x(y(e.substring(1),i));if(0<=e.indexOf("-"))throw Error('number format error: interior "-" character');for(var s=v(Math.pow(i,8)),h=o,f=0;f<e.length;f+=8){var l=Math.min(8,e.length-f),d=parseInt(e.substring(f,f+l),i);8>l?(l=v(Math.pow(i,l)),h=h.j(l).add(v(d))):h=(h=h.j(s)).add(v(d))}return h}var o=u(0),f=u(1),d=u(16777216);function C(e){if(0!=e.h)return!1;for(var i=0;i<e.g.length;i++)if(0!=e.g[i])return!1;return!0}function B(e){return -1==e.h}function x(e){for(var i=e.g.length,o=[],s=0;s<i;s++)o[s]=~e.g[s];return new t(o,~e.h).add(f)}function F(e,i){return e.add(x(i))}function G(e,i){for(;(65535&e[i])!=e[i];)e[i+1]+=e[i]>>>16,e[i]&=65535,i++}function H(e,i){this.g=e,this.h=i}function D(e,i){if(C(i))throw Error("division by zero");if(C(e))return new H(o,o);if(B(e))return i=D(x(e),i),new H(x(i.g),x(i.h));if(B(i))return i=D(e,x(i)),new H(x(i.g),i.h);if(30<e.g.length){if(B(e)||B(i))throw Error("slowDivide_ only works with positive integers.");for(var s=f,h=i;0>=h.l(e);)s=I(s),h=I(h);var l=J(s,1),d=J(h,1);for(h=J(h,2),s=J(s,2);!C(h);){var g=d.add(h);0>=g.l(e)&&(l=l.add(s),d=g),h=J(h,1),s=J(s,1)}return i=F(e,l.j(i)),new H(l,i)}for(l=o;0<=e.l(i);){for(h=48>=(h=Math.ceil(Math.log(s=Math.max(1,Math.floor(e.m()/i.m())))/Math.LN2))?1:Math.pow(2,h-48),g=(d=v(s)).j(i);B(g)||0<g.l(e);)s-=h,g=(d=v(s)).j(i);C(d)&&(d=f),l=l.add(d),e=F(e,g)}return new H(l,e)}function I(e){for(var i=e.g.length+1,o=[],s=0;s<i;s++)o[s]=e.i(s)<<1|e.i(s-1)>>>31;return new t(o,e.h)}function J(e,i){var o=i>>5;i%=32;for(var s=e.g.length-o,h=[],f=0;f<s;f++)h[f]=0<i?e.i(f+o)>>>i|e.i(f+o+1)<<32-i:e.i(f+o);return new t(h,e.h)}(e=t.prototype).m=function(){if(B(this))return-x(this).m();for(var e=0,i=1,o=0;o<this.g.length;o++){var s=this.i(o);e+=(0<=s?s:4294967296+s)*i,i*=4294967296}return e},e.toString=function(e){if(2>(e=e||10)||36<e)throw Error("radix out of range: "+e);if(C(this))return"0";if(B(this))return"-"+x(this).toString(e);for(var i=v(Math.pow(e,6)),o=this,s="";;){var h=D(o,i).g,f=((0<(o=F(o,h.j(i))).g.length?o.g[0]:o.h)>>>0).toString(e);if(C(o=h))return f+s;for(;6>f.length;)f="0"+f;s=f+s}},e.i=function(e){return 0>e?0:e<this.g.length?this.g[e]:this.h},e.l=function(e){return B(e=F(this,e))?-1:C(e)?0:1},e.abs=function(){return B(this)?x(this):this},e.add=function(e){for(var i=Math.max(this.g.length,e.g.length),o=[],s=0,h=0;h<=i;h++){var f=s+(65535&this.i(h))+(65535&e.i(h)),l=(f>>>16)+(this.i(h)>>>16)+(e.i(h)>>>16);s=l>>>16,f&=65535,l&=65535,o[h]=l<<16|f}return new t(o,-2147483648&o[o.length-1]?-1:0)},e.j=function(e){if(C(this)||C(e))return o;if(B(this))return B(e)?x(this).j(x(e)):x(x(this).j(e));if(B(e))return x(this.j(x(e)));if(0>this.l(d)&&0>e.l(d))return v(this.m()*e.m());for(var i=this.g.length+e.g.length,s=[],h=0;h<2*i;h++)s[h]=0;for(h=0;h<this.g.length;h++)for(var f=0;f<e.g.length;f++){var l=this.i(h)>>>16,g=65535&this.i(h),b=e.i(f)>>>16,w=65535&e.i(f);s[2*h+2*f]+=g*w,G(s,2*h+2*f),s[2*h+2*f+1]+=l*w,G(s,2*h+2*f+1),s[2*h+2*f+1]+=g*b,G(s,2*h+2*f+1),s[2*h+2*f+2]+=l*b,G(s,2*h+2*f+2)}for(h=0;h<i;h++)s[h]=s[2*h+1]<<16|s[2*h];for(h=i;h<2*i;h++)s[h]=0;return new t(s,0)},e.A=function(e){return D(this,e).h},e.and=function(e){for(var i=Math.max(this.g.length,e.g.length),o=[],s=0;s<i;s++)o[s]=this.i(s)&e.i(s);return new t(o,this.h&e.h)},e.or=function(e){for(var i=Math.max(this.g.length,e.g.length),o=[],s=0;s<i;s++)o[s]=this.i(s)|e.i(s);return new t(o,this.h|e.h)},e.xor=function(e){for(var i=Math.max(this.g.length,e.g.length),o=[],s=0;s<i;s++)o[s]=this.i(s)^e.i(s);return new t(o,this.h^e.h)},m.prototype.digest=m.prototype.v,m.prototype.reset=m.prototype.s,m.prototype.update=m.prototype.u,h=l.Md5=m,t.prototype.add=t.prototype.add,t.prototype.multiply=t.prototype.j,t.prototype.modulo=t.prototype.A,t.prototype.compare=t.prototype.l,t.prototype.toNumber=t.prototype.m,t.prototype.toString=t.prototype.toString,t.prototype.getBits=t.prototype.i,t.fromNumber=v,t.fromString=y,s=l.Integer=t}).apply(void 0!==f?f:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},4203:function(e,i,o){"use strict";o.d(i,{FJ:function(){return b},JJ:function(){return s},UE:function(){return w},ii:function(){return h},jK:function(){return l},ju:function(){return g},kN:function(){return d},tw:function(){return f}});var s,h,f,l,d,g,b,w,_="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},O={};(function(){var e,i,o,k="function"==typeof Object.defineProperties?Object.defineProperty:function(e,i,o){return e==Array.prototype||e==Object.prototype||(e[i]=o.value),e},j=function(e){e=["object"==typeof globalThis&&globalThis,e,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof _&&_];for(var i=0;i<e.length;++i){var o=e[i];if(o&&o.Math==Math)return o}throw Error("Cannot find global object")}(this);function ea(e,i){e instanceof String&&(e+="");var o=0,s=!1,h={next:function(){if(!s&&o<e.length){var h=o++;return{value:i(h,e[h]),done:!1}}return s=!0,{done:!0,value:void 0}}};return h[Symbol.iterator]=function(){return h},h}!function(e,i){if(i)t:{var o=j;e=e.split(".");for(var s=0;s<e.length-1;s++){var h=e[s];if(!(h in o))break t;o=o[h]}(i=i(s=o[e=e[e.length-1]]))!=s&&null!=i&&k(o,e,{configurable:!0,writable:!0,value:i})}}("Array.prototype.values",function(e){return e||function(){return ea(this,function(e,i){return i})}});var $=$||{},tt=this||self;function ha(e){var i=typeof e;return"array"==(i="object"!=i?i:e?Array.isArray(e)?"array":i:"null")||"object"==i&&"number"==typeof e.length}function n(e){var i=typeof e;return"object"==i&&null!=e||"function"==i}function ia(e,i,o){return e.call.apply(e.bind,arguments)}function ja(e,i,o){if(!e)throw Error();if(2<arguments.length){var s=Array.prototype.slice.call(arguments,2);return function(){var o=Array.prototype.slice.call(arguments);return Array.prototype.unshift.apply(o,s),e.apply(i,o)}}return function(){return e.apply(i,arguments)}}function p(e,i,o){return(p=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?ia:ja).apply(null,arguments)}function ka(e,i){var o=Array.prototype.slice.call(arguments,1);return function(){var i=o.slice();return i.push.apply(i,arguments),e.apply(this,i)}}function r(e,i){function c(){}c.prototype=i.prototype,e.aa=i.prototype,e.prototype=new c,e.prototype.constructor=e,e.Qb=function(e,o,s){for(var h=Array(arguments.length-2),f=2;f<arguments.length;f++)h[f-2]=arguments[f];return i.prototype[o].apply(e,h)}}function la(e){let i=e.length;if(0<i){let o=Array(i);for(let s=0;s<i;s++)o[s]=e[s];return o}return[]}function ma(e,i){for(let i=1;i<arguments.length;i++){let o=arguments[i];if(ha(o)){let i=e.length||0,s=o.length||0;e.length=i+s;for(let h=0;h<s;h++)e[i+h]=o[h]}else e.push(o)}}let na=class na{constructor(e,i){this.i=e,this.j=i,this.h=0,this.g=null}get(){let e;return 0<this.h?(this.h--,e=this.g,this.g=e.next,e.next=null):e=this.i(),e}};function t(e){return/^[\s\xa0]*$/.test(e)}function u(){var e=tt.navigator;return e&&(e=e.userAgent)?e:""}function oa(e){return oa[" "](e),e}oa[" "]=function(){};var te=-1!=u().indexOf("Gecko")&&!(-1!=u().toLowerCase().indexOf("webkit")&&-1==u().indexOf("Edge"))&&!(-1!=u().indexOf("Trident")||-1!=u().indexOf("MSIE"))&&-1==u().indexOf("Edge");function qa(e,i,o){for(let s in e)i.call(o,e[s],s,e)}function ra(e,i){for(let o in e)i.call(void 0,e[o],o,e)}function sa(e){let i={};for(let o in e)i[o]=e[o];return i}let tr="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function ua(e,i){let o,s;for(let i=1;i<arguments.length;i++){for(o in s=arguments[i])e[o]=s[o];for(let i=0;i<tr.length;i++)o=tr[i],Object.prototype.hasOwnProperty.call(s,o)&&(e[o]=s[o])}}function va(e){var i=1;e=e.split(":");let o=[];for(;0<i&&e.length;)o.push(e.shift()),i--;return e.length&&o.push(e.join(":")),o}function wa(e){tt.setTimeout(()=>{throw e},0)}function xa(){let e=null;return ts.g&&(e=ts.g,ts.g=ts.g.next,ts.g||(ts.h=null),e.next=null),e}let Aa=class Aa{constructor(){this.h=this.g=null}add(e,i){let o=tn.get();o.set(e,i),this.h?this.h.next=o:this.g=o,this.h=o}};var tn=new na(()=>new Ca,e=>e.reset());let Ca=class Ca{constructor(){this.next=this.g=this.h=null}set(e,i){this.h=e,this.g=i,this.next=null}reset(){this.next=this.g=this.h=null}};let ti,to=!1,ts=new Aa,Ea=()=>{let e=tt.Promise.resolve(void 0);ti=()=>{e.then(Da)}};var Da=()=>{for(var e;e=xa();){try{e.h.call(e.g)}catch(e){wa(e)}tn.j(e),100>tn.h&&(tn.h++,e.next=tn.g,tn.g=e)}to=!1};function z(){this.s=this.s,this.C=this.C}function A(e,i){this.type=e,this.g=this.target=i,this.defaultPrevented=!1}z.prototype.s=!1,z.prototype.ma=function(){this.s||(this.s=!0,this.N())},z.prototype.N=function(){if(this.C)for(;this.C.length;)this.C.shift()()},A.prototype.h=function(){this.defaultPrevented=!0};var ta=function(){if(!tt.addEventListener||!Object.defineProperty)return!1;var e=!1,i=Object.defineProperty({},"passive",{get:function(){e=!0}});try{let c=()=>{};tt.addEventListener("test",c,i),tt.removeEventListener("test",c,i)}catch(e){}return e}();function C(e,i){if(A.call(this,e?e.type:""),this.relatedTarget=this.g=this.target=null,this.button=this.screenY=this.screenX=this.clientY=this.clientX=0,this.key="",this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1,this.state=null,this.pointerId=0,this.pointerType="",this.i=null,e){var o=this.type=e.type,s=e.changedTouches&&e.changedTouches.length?e.changedTouches[0]:null;if(this.target=e.target||e.srcElement,this.g=i,i=e.relatedTarget){if(te){t:{try{oa(i.nodeName);var h=!0;break t}catch(e){}h=!1}h||(i=null)}}else"mouseover"==o?i=e.fromElement:"mouseout"==o&&(i=e.toElement);this.relatedTarget=i,s?(this.clientX=void 0!==s.clientX?s.clientX:s.pageX,this.clientY=void 0!==s.clientY?s.clientY:s.pageY,this.screenX=s.screenX||0,this.screenY=s.screenY||0):(this.clientX=void 0!==e.clientX?e.clientX:e.pageX,this.clientY=void 0!==e.clientY?e.clientY:e.pageY,this.screenX=e.screenX||0,this.screenY=e.screenY||0),this.button=e.button,this.key=e.key||"",this.ctrlKey=e.ctrlKey,this.altKey=e.altKey,this.shiftKey=e.shiftKey,this.metaKey=e.metaKey,this.pointerId=e.pointerId||0,this.pointerType="string"==typeof e.pointerType?e.pointerType:th[e.pointerType]||"",this.state=e.state,this.i=e,e.defaultPrevented&&C.aa.h.call(this)}}r(C,A);var th={2:"touch",3:"pen",4:"mouse"};C.prototype.h=function(){C.aa.h.call(this);var e=this.i;e.preventDefault?e.preventDefault():e.returnValue=!1};var tu="closure_listenable_"+(1e6*Math.random()|0),tf=0;function Ia(e,i,o,s,h){this.listener=e,this.proxy=null,this.src=i,this.type=o,this.capture=!!s,this.ha=h,this.key=++tf,this.da=this.fa=!1}function Ja(e){e.da=!0,e.listener=null,e.proxy=null,e.src=null,e.ha=null}function Ka(e){this.src=e,this.g={},this.h=0}function Ma(e,i){var o=i.type;if(o in e.g){var s,h=e.g[o],f=Array.prototype.indexOf.call(h,i,void 0);(s=0<=f)&&Array.prototype.splice.call(h,f,1),s&&(Ja(i),0==e.g[o].length&&(delete e.g[o],e.h--))}}function La(e,i,o,s){for(var h=0;h<e.length;++h){var f=e[h];if(!f.da&&f.listener==i&&!!o==f.capture&&f.ha==s)return h}return -1}Ka.prototype.add=function(e,i,o,s,h){var f=e.toString();(e=this.g[f])||(e=this.g[f]=[],this.h++);var l=La(e,i,s,h);return -1<l?(i=e[l],o||(i.fa=!1)):((i=new Ia(i,this.src,f,!!s,h)).fa=o,e.push(i)),i};var tl="closure_lm_"+(1e6*Math.random()|0),tp={};function Qa(e,i,o,s,h){if(s&&s.once)return Ra(e,i,o,s,h);if(Array.isArray(i)){for(var f=0;f<i.length;f++)Qa(e,i[f],o,s,h);return null}return o=Sa(o),e&&e[tu]?e.K(i,o,n(s)?!!s.capture:!!s,h):Ta(e,i,o,!1,s,h)}function Ta(e,i,o,s,h,f){if(!i)throw Error("Invalid event type");var l=n(h)?!!h.capture:!!h,d=Ua(e);if(d||(e[tl]=d=new Ka(e)),(o=d.add(i,o,s,l,f)).proxy)return o;if(s=Va(),o.proxy=s,s.src=e,s.listener=o,e.addEventListener)ta||(h=l),void 0===h&&(h=!1),e.addEventListener(i.toString(),s,h);else if(e.attachEvent)e.attachEvent(Wa(i.toString()),s);else if(e.addListener&&e.removeListener)e.addListener(s);else throw Error("addEventListener and attachEvent are unavailable.");return o}function Va(){function a(i){return e.call(a.src,a.listener,i)}let e=Xa;return a}function Ra(e,i,o,s,h){if(Array.isArray(i)){for(var f=0;f<i.length;f++)Ra(e,i[f],o,s,h);return null}return o=Sa(o),e&&e[tu]?e.L(i,o,n(s)?!!s.capture:!!s,h):Ta(e,i,o,!0,s,h)}function Ya(e,i,o,s,h){if(Array.isArray(i))for(var f=0;f<i.length;f++)Ya(e,i[f],o,s,h);else(s=n(s)?!!s.capture:!!s,o=Sa(o),e&&e[tu])?(e=e.i,(i=String(i).toString())in e.g&&-1<(o=La(f=e.g[i],o,s,h))&&(Ja(f[o]),Array.prototype.splice.call(f,o,1),0==f.length&&(delete e.g[i],e.h--))):e&&(e=Ua(e))&&(i=e.g[i.toString()],e=-1,i&&(e=La(i,o,s,h)),(o=-1<e?i[e]:null)&&Za(o))}function Za(e){if("number"!=typeof e&&e&&!e.da){var i=e.src;if(i&&i[tu])Ma(i.i,e);else{var o=e.type,s=e.proxy;i.removeEventListener?i.removeEventListener(o,s,e.capture):i.detachEvent?i.detachEvent(Wa(o),s):i.addListener&&i.removeListener&&i.removeListener(s),(o=Ua(i))?(Ma(o,e),0==o.h&&(o.src=null,i[tl]=null)):Ja(e)}}}function Wa(e){return e in tp?tp[e]:tp[e]="on"+e}function Xa(e,i){if(e.da)e=!0;else{i=new C(i,this);var o=e.listener,s=e.ha||e.src;e.fa&&Za(e),e=o.call(s,i)}return e}function Ua(e){return(e=e[tl])instanceof Ka?e:null}var td="__closure_events_fn_"+(1e9*Math.random()>>>0);function Sa(e){return"function"==typeof e?e:(e[td]||(e[td]=function(i){return e.handleEvent(i)}),e[td])}function E(){z.call(this),this.i=new Ka(this),this.M=this,this.F=null}function F(e,i){var o,s=e.F;if(s)for(o=[];s;s=s.F)o.push(s);if(e=e.M,s=i.type||i,"string"==typeof i)i=new A(i,e);else if(i instanceof A)i.target=i.target||e;else{var h=i;ua(i=new A(s,e),h)}if(h=!0,o)for(var f=o.length-1;0<=f;f--){var l=i.g=o[f];h=ab(l,s,!0,i)&&h}if(h=ab(l=i.g=e,s,!0,i)&&h,h=ab(l,s,!1,i)&&h,o)for(f=0;f<o.length;f++)h=ab(l=i.g=o[f],s,!1,i)&&h}function ab(e,i,o,s){if(!(i=e.i.g[String(i)]))return!0;i=i.concat();for(var h=!0,f=0;f<i.length;++f){var l=i[f];if(l&&!l.da&&l.capture==o){var d=l.listener,g=l.ha||l.src;l.fa&&Ma(e.i,l),h=!1!==d.call(g,s)&&h}}return h&&!s.defaultPrevented}function bb(e,i,o){if("function"==typeof e)o&&(e=p(e,o));else if(e&&"function"==typeof e.handleEvent)e=p(e.handleEvent,e);else throw Error("Invalid listener argument");return **********<Number(i)?-1:tt.setTimeout(e,i||0)}function cb(e){e.g=bb(()=>{e.g=null,e.i&&(e.i=!1,cb(e))},e.l);let i=e.h;e.h=null,e.m.apply(null,i)}r(E,z),E.prototype[tu]=!0,E.prototype.removeEventListener=function(e,i,o,s){Ya(this,e,i,o,s)},E.prototype.N=function(){if(E.aa.N.call(this),this.i){var e,i=this.i;for(e in i.g){for(var o=i.g[e],s=0;s<o.length;s++)Ja(o[s]);delete i.g[e],i.h--}}this.F=null},E.prototype.K=function(e,i,o,s){return this.i.add(String(e),i,!1,o,s)},E.prototype.L=function(e,i,o,s){return this.i.add(String(e),i,!0,o,s)};let eb=class eb extends z{constructor(e,i){super(),this.m=e,this.l=i,this.h=null,this.i=!1,this.g=null}j(e){this.h=arguments,this.g?this.i=!0:cb(this)}N(){super.N(),this.g&&(tt.clearTimeout(this.g),this.g=null,this.i=!1,this.h=null)}};function G(e){z.call(this),this.h=e,this.g={}}r(G,z);var tg=[];function gb(e){qa(e.g,function(e,i){this.g.hasOwnProperty(i)&&Za(e)},e),e.g={}}G.prototype.N=function(){G.aa.N.call(this),gb(this)},G.prototype.handleEvent=function(){throw Error("EventHandler.handleEvent not implemented")};var ty=tt.JSON.stringify,tm=tt.JSON.parse,tv=class{stringify(e){return tt.JSON.stringify(e,void 0)}parse(e){return tt.JSON.parse(e,void 0)}};function kb(){}function lb(e){return e.h||(e.h=e.i())}function mb(){}kb.prototype.h=null;var tw={OPEN:"a",kb:"b",Ja:"c",wb:"d"};function nb(){A.call(this,"d")}function ob(){A.call(this,"c")}r(nb,A),r(ob,A);var tE={},tS=null;function qb(){return tS=tS||new E}function rb(e){A.call(this,tE.La,e)}function J(e){let i=qb();F(i,new rb(i))}function sb(e,i){A.call(this,tE.STAT_EVENT,e),this.stat=i}function K(e){let i=qb();F(i,new sb(i,e))}function tb(e,i){A.call(this,tE.Ma,e),this.size=i}function ub(e,i){if("function"!=typeof e)throw Error("Fn must not be null and must be a function");return tt.setTimeout(function(){e()},i)}function vb(){this.g=!0}function wb(e,i,o,s,h,f){e.info(function(){if(e.g){if(f)for(var l="",d=f.split("&"),g=0;g<d.length;g++){var b=d[g].split("=");if(1<b.length){var w=b[0];b=b[1];var _=w.split("_");l=2<=_.length&&"type"==_[1]?l+(w+"=")+b+"&":l+(w+"=redacted&")}}else l=null}else l=f;return"XMLHTTP REQ ("+s+") [attempt "+h+"]: "+i+"\n"+o+"\n"+l})}function xb(e,i,o,s,h,f,l){e.info(function(){return"XMLHTTP RESP ("+s+") [ attempt "+h+"]: "+i+"\n"+o+"\n"+f+" "+l})}function L(e,i,o,s){e.info(function(){return"XMLHTTP TEXT ("+i+"): "+yb(e,o)+(s?" "+s:"")})}function zb(e,i){e.info(function(){return"TIMEOUT: "+i})}function yb(e,i){if(!e.g)return i;if(!i)return null;try{var o=JSON.parse(i);if(o){for(e=0;e<o.length;e++)if(Array.isArray(o[e])){var s=o[e];if(!(2>s.length)){var h=s[1];if(Array.isArray(h)&&!(1>h.length)){var f=h[0];if("noop"!=f&&"stop"!=f&&"close"!=f)for(var l=1;l<h.length;l++)h[l]=""}}}}return ty(o)}catch(e){return i}}tE.La="serverreachability",r(rb,A),tE.STAT_EVENT="statevent",r(sb,A),tE.Ma="timingevent",r(tb,A),vb.prototype.xa=function(){this.g=!1},vb.prototype.info=function(){};var tB={NO_ERROR:0,gb:1,tb:2,sb:3,nb:4,rb:5,ub:6,Ia:7,TIMEOUT:8,xb:9},tC={lb:"complete",Hb:"success",Ja:"error",Ia:"abort",zb:"ready",Ab:"readystatechange",TIMEOUT:"timeout",vb:"incrementaldata",yb:"progress",ob:"downloadprogress",Pb:"uploadprogress"};function Db(){}function M(e,i,o,s){this.j=e,this.i=i,this.l=o,this.R=s||1,this.U=new G(this),this.I=45e3,this.H=null,this.o=!1,this.m=this.A=this.v=this.L=this.F=this.S=this.B=null,this.D=[],this.g=null,this.C=0,this.s=this.u=null,this.X=-1,this.J=!1,this.O=0,this.M=null,this.W=this.K=this.T=this.P=!1,this.h=new Eb}function Eb(){this.i=null,this.g="",this.h=!1}r(Db,kb),Db.prototype.g=function(){return new XMLHttpRequest},Db.prototype.i=function(){return{}},i=new Db;var tA={},tI={};function Hb(e,i,o){e.L=1,e.v=Ib(N(i)),e.m=o,e.P=!0,Jb(e,null)}function Jb(e,i){e.F=Date.now(),Kb(e),e.A=N(e.v);var o=e.A,s=e.R;Array.isArray(s)||(s=[String(s)]),Lb(o.i,"t",s),e.C=0,o=e.j.J,e.h=new Eb,e.g=Mb(e.j,o?i:null,!e.m),0<e.O&&(e.M=new eb(p(e.Y,e,e.g),e.O)),i=e.U,o=e.g,s=e.ca;var h="readystatechange";Array.isArray(h)||(h&&(tg[0]=h.toString()),h=tg);for(var f=0;f<h.length;f++){var l=Qa(o,h[f],s||i.handleEvent,!1,i.h||i);if(!l)break;i.g[l.key]=l}i=e.H?sa(e.H):{},e.m?(e.u||(e.u="POST"),i["Content-Type"]="application/x-www-form-urlencoded",e.g.ea(e.A,e.u,e.m,i)):(e.u="GET",e.g.ea(e.A,e.u,null,i)),J(),wb(e.i,e.u,e.A,e.l,e.R,e.m)}function Pb(e){return!!e.g&&"GET"==e.u&&2!=e.L&&e.j.Ca}function Sb(e,i){var o=e.C,s=i.indexOf("\n",o);return -1==s?tI:isNaN(o=Number(i.substring(o,s)))?tA:(s+=1)+o>i.length?tI:(i=i.slice(s,s+o),e.C=s+o,i)}function Kb(e){e.S=Date.now()+e.I,Wb(e,e.I)}function Wb(e,i){if(null!=e.B)throw Error("WatchDog timer not null");e.B=ub(p(e.ba,e),i)}function Ob(e){e.B&&(tt.clearTimeout(e.B),e.B=null)}function Qb(e){0==e.j.G||e.J||Ub(e.j,e)}function Q(e){Ob(e);var i=e.M;i&&"function"==typeof i.ma&&i.ma(),e.M=null,gb(e.U),e.g&&(i=e.g,e.g=null,i.abort(),i.ma())}function Rb(e,i){try{var o=e.j;if(0!=o.G&&(o.g==e||Xb(o.h,e))){if(!e.K&&Xb(o.h,e)&&3==o.G){try{var s=o.Da.g.parse(i)}catch(e){s=null}if(Array.isArray(s)&&3==s.length){var h=s;if(0==h[0]){t:if(!o.u){if(o.g){if(o.g.F+3e3<e.F)Yb(o),Zb(o);else break t}$b(o),K(18)}}else o.za=h[1],0<o.za-o.T&&37500>h[2]&&o.F&&0==o.v&&!o.C&&(o.C=ub(p(o.Za,o),6e3));if(1>=ac(o.h)&&o.ca){try{o.ca()}catch(e){}o.ca=void 0}}else R(o,11)}else if((e.K||o.g==e)&&Yb(o),!t(i))for(h=o.Da.g.parse(i),i=0;i<h.length;i++){let d=h[i];if(o.T=d[0],d=d[1],2==o.G){if("c"==d[0]){o.K=d[1],o.ia=d[2];let i=d[3];null!=i&&(o.la=i,o.j.info("VER="+o.la));let h=d[4];null!=h&&(o.Aa=h,o.j.info("SVER="+o.Aa));let g=d[5];null!=g&&"number"==typeof g&&0<g&&(s=1.5*g,o.L=s,o.j.info("backChannelRequestTimeoutMs_="+s)),s=o;let b=e.g;if(b){let e=b.g?b.g.getResponseHeader("X-Client-Wire-Protocol"):null;if(e){var f=s.h;f.g||-1==e.indexOf("spdy")&&-1==e.indexOf("quic")&&-1==e.indexOf("h2")||(f.j=f.l,f.g=new Set,f.h&&(bc(f,f.h),f.h=null))}if(s.D){let e=b.g?b.g.getResponseHeader("X-HTTP-Session-Id"):null;e&&(s.ya=e,S(s.I,s.D,e))}}if(o.G=3,o.l&&o.l.ua(),o.ba&&(o.R=Date.now()-e.F,o.j.info("Handshake RTT: "+o.R+"ms")),(s=o).qa=cc(s,s.J?s.ia:null,s.W),e.K){dc(s.h,e);var l=s.L;l&&(e.I=l),e.B&&(Ob(e),Kb(e)),s.g=e}else ec(s);0<o.i.length&&fc(o)}else"stop"!=d[0]&&"close"!=d[0]||R(o,7)}else 3==o.G&&("stop"==d[0]||"close"==d[0]?"stop"==d[0]?R(o,7):gc(o):"noop"!=d[0]&&o.l&&o.l.ta(d),o.v=0)}}J(4)}catch(e){}}M.prototype.ca=function(e){e=e.target;let i=this.M;i&&3==P(e)?i.j():this.Y(e)},M.prototype.Y=function(e){try{if(e==this.g)t:{let _=P(this.g);var i=this.g.Ba();let O=this.g.Z();if(!(3>_)&&(3!=_||this.g&&(this.h.h||this.g.oa()||Nb(this.g)))){this.J||4!=_||7==i||(8==i||0>=O?J(3):J(2)),Ob(this);var o=this.g.Z();this.X=o;e:if(Pb(this)){var s=Nb(this.g);e="";var h=s.length,f=4==P(this.g);if(!this.h.i){if("undefined"==typeof TextDecoder){Q(this),Qb(this);var l="";break e}this.h.i=new tt.TextDecoder}for(i=0;i<h;i++)this.h.h=!0,e+=this.h.i.decode(s[i],{stream:!(f&&i==h-1)});s.length=0,this.h.g+=e,this.C=0,l=this.h.g}else l=this.g.oa();if(this.o=200==o,xb(this.i,this.u,this.A,this.l,this.R,_,o),this.o){if(this.T&&!this.K){e:{if(this.g){var d,g=this.g;if((d=g.g?g.g.getResponseHeader("X-HTTP-Initial-Response"):null)&&!t(d)){var b=d;break e}}b=null}if(o=b)L(this.i,this.l,o,"Initial handshake response via X-HTTP-Initial-Response"),this.K=!0,Rb(this,o);else{this.o=!1,this.s=3,K(12),Q(this),Qb(this);break t}}if(this.P){let e;for(o=!0;!this.J&&this.C<l.length;)if((e=Sb(this,l))==tI){4==_&&(this.s=4,K(14),o=!1),L(this.i,this.l,null,"[Incomplete Response]");break}else if(e==tA){this.s=4,K(15),L(this.i,this.l,l,"[Invalid Chunk]"),o=!1;break}else L(this.i,this.l,e,null),Rb(this,e);if(Pb(this)&&0!=this.C&&(this.h.g=this.h.g.slice(this.C),this.C=0),4!=_||0!=l.length||this.h.h||(this.s=1,K(16),o=!1),this.o=this.o&&o,o){if(0<l.length&&!this.W){this.W=!0;var w=this.j;w.g==this&&w.ba&&!w.M&&(w.j.info("Great, no buffering proxy detected. Bytes received: "+l.length),Tb(w),w.M=!0,K(11))}}else L(this.i,this.l,l,"[Invalid Chunked Response]"),Q(this),Qb(this)}else L(this.i,this.l,l,null),Rb(this,l);4==_&&Q(this),this.o&&!this.J&&(4==_?Ub(this.j,this):(this.o=!1,Kb(this)))}else Vb(this.g),400==o&&0<l.indexOf("Unknown SID")?(this.s=3,K(12)):(this.s=0,K(13)),Q(this),Qb(this)}}}catch(e){}finally{}},M.prototype.cancel=function(){this.J=!0,Q(this)},M.prototype.ba=function(){this.B=null;let e=Date.now();0<=e-this.S?(zb(this.i,this.A),2!=this.L&&(J(),K(17)),Q(this),this.s=2,Qb(this)):Wb(this,this.S-e)};var t_=class{constructor(e,i){this.g=e,this.map=i}};function ic(e){this.l=e||10,e=tt.PerformanceNavigationTiming?0<(e=tt.performance.getEntriesByType("navigation")).length&&("hq"==e[0].nextHopProtocol||"h2"==e[0].nextHopProtocol):!!(tt.chrome&&tt.chrome.loadTimes&&tt.chrome.loadTimes()&&tt.chrome.loadTimes().wasFetchedViaSpdy),this.j=e?this.l:1,this.g=null,1<this.j&&(this.g=new Set),this.h=null,this.i=[]}function jc(e){return!!e.h||!!e.g&&e.g.size>=e.j}function ac(e){return e.h?1:e.g?e.g.size:0}function Xb(e,i){return e.h?e.h==i:!!e.g&&e.g.has(i)}function bc(e,i){e.g?e.g.add(i):e.h=i}function dc(e,i){e.h&&e.h==i?e.h=null:e.g&&e.g.has(i)&&e.g.delete(i)}function kc(e){if(null!=e.h)return e.i.concat(e.h.D);if(null!=e.g&&0!==e.g.size){let i=e.i;for(let o of e.g.values())i=i.concat(o.D);return i}return la(e.i)}function lc(e){if(e.V&&"function"==typeof e.V)return e.V();if("undefined"!=typeof Map&&e instanceof Map||"undefined"!=typeof Set&&e instanceof Set)return Array.from(e.values());if("string"==typeof e)return e.split("");if(ha(e)){for(var i=[],o=e.length,s=0;s<o;s++)i.push(e[s]);return i}for(s in i=[],o=0,e)i[o++]=e[s];return i}function mc(e){if(e.na&&"function"==typeof e.na)return e.na();if(!e.V||"function"!=typeof e.V){if("undefined"!=typeof Map&&e instanceof Map)return Array.from(e.keys());if(!("undefined"!=typeof Set&&e instanceof Set)){if(ha(e)||"string"==typeof e){var i=[];e=e.length;for(var o=0;o<e;o++)i.push(o);return i}for(let s in i=[],o=0,e)i[o++]=s;return i}}}function nc(e,i){if(e.forEach&&"function"==typeof e.forEach)e.forEach(i,void 0);else if(ha(e)||"string"==typeof e)Array.prototype.forEach.call(e,i,void 0);else for(var o=mc(e),s=lc(e),h=s.length,f=0;f<h;f++)i.call(void 0,s[f],o&&o[f],e)}ic.prototype.cancel=function(){if(this.i=kc(this),this.h)this.h.cancel(),this.h=null;else if(this.g&&0!==this.g.size){for(let e of this.g.values())e.cancel();this.g.clear()}};var tT=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");function pc(e,i){if(e){e=e.split("&");for(var o=0;o<e.length;o++){var s=e[o].indexOf("="),h=null;if(0<=s){var f=e[o].substring(0,s);h=e[o].substring(s+1)}else f=e[o];i(f,h?decodeURIComponent(h.replace(/\+/g," ")):"")}}}function T(e){if(this.g=this.o=this.j="",this.s=null,this.m=this.l="",this.h=!1,e instanceof T){this.h=e.h,qc(this,e.j),this.o=e.o,this.g=e.g,rc(this,e.s),this.l=e.l;var i=e.i,o=new sc;o.i=i.i,i.g&&(o.g=new Map(i.g),o.h=i.h),tc(this,o),this.m=e.m}else e&&(i=String(e).match(tT))?(this.h=!1,qc(this,i[1]||"",!0),this.o=uc(i[2]||""),this.g=uc(i[3]||"",!0),rc(this,i[4]),this.l=uc(i[5]||"",!0),tc(this,i[6]||"",!0),this.m=uc(i[7]||"")):(this.h=!1,this.i=new sc(null,this.h))}function N(e){return new T(e)}function qc(e,i,o){e.j=o?uc(i,!0):i,e.j&&(e.j=e.j.replace(/:$/,""))}function rc(e,i){if(i){if(isNaN(i=Number(i))||0>i)throw Error("Bad port number "+i);e.s=i}else e.s=null}function tc(e,i,o){i instanceof sc?(e.i=i,Ac(e.i,e.h)):(o||(i=vc(i,tk)),e.i=new sc(i,e.h))}function S(e,i,o){e.i.set(i,o)}function Ib(e){return S(e,"zx",Math.floor(2147483648*Math.random()).toString(36)+Math.abs(Math.floor(2147483648*Math.random())^Date.now()).toString(36)),e}function uc(e,i){return e?i?decodeURI(e.replace(/%25/g,"%2525")):decodeURIComponent(e):""}function vc(e,i,o){return"string"==typeof e?(e=encodeURI(e).replace(i,Cc),o&&(e=e.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),e):null}function Cc(e){return"%"+((e=e.charCodeAt(0))>>4&15).toString(16)+(15&e).toString(16)}T.prototype.toString=function(){var e=[],i=this.j;i&&e.push(vc(i,tO,!0),":");var o=this.g;return(o||"file"==i)&&(e.push("//"),(i=this.o)&&e.push(vc(i,tO,!0),"@"),e.push(encodeURIComponent(String(o)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),null!=(o=this.s)&&e.push(":",String(o))),(o=this.l)&&(this.g&&"/"!=o.charAt(0)&&e.push("/"),e.push(vc(o,"/"==o.charAt(0)?tx:tD,!0))),(o=this.i.toString())&&e.push("?",o),(o=this.m)&&e.push("#",vc(o,tL)),e.join("")};var tO=/[#\/\?@]/g,tD=/[#\?:]/g,tx=/[#\?]/g,tk=/[#\?@]/g,tL=/#/g;function sc(e,i){this.h=this.g=null,this.i=e||null,this.j=!!i}function U(e){e.g||(e.g=new Map,e.h=0,e.i&&pc(e.i,function(i,o){e.add(decodeURIComponent(i.replace(/\+/g," ")),o)}))}function Dc(e,i){U(e),i=V(e,i),e.g.has(i)&&(e.i=null,e.h-=e.g.get(i).length,e.g.delete(i))}function Ec(e,i){return U(e),i=V(e,i),e.g.has(i)}function Lb(e,i,o){Dc(e,i),0<o.length&&(e.i=null,e.g.set(V(e,i),la(o)),e.h+=o.length)}function V(e,i){return i=String(i),e.j&&(i=i.toLowerCase()),i}function Ac(e,i){i&&!e.j&&(U(e),e.i=null,e.g.forEach(function(e,i){var o=i.toLowerCase();i!=o&&(Dc(this,i),Lb(this,o,e))},e)),e.j=i}function Fc(e,i){let o=new vb;if(tt.Image){let s=new Image;s.onload=ka(W,o,"TestLoadImage: loaded",!0,i,s),s.onerror=ka(W,o,"TestLoadImage: error",!1,i,s),s.onabort=ka(W,o,"TestLoadImage: abort",!1,i,s),s.ontimeout=ka(W,o,"TestLoadImage: timeout",!1,i,s),tt.setTimeout(function(){s.ontimeout&&s.ontimeout()},1e4),s.src=e}else i(!1)}function Gc(e,i){let o=new vb,s=new AbortController,h=setTimeout(()=>{s.abort(),W(o,"TestPingServer: timeout",!1,i)},1e4);fetch(e,{signal:s.signal}).then(e=>{clearTimeout(h),e.ok?W(o,"TestPingServer: ok",!0,i):W(o,"TestPingServer: server error",!1,i)}).catch(()=>{clearTimeout(h),W(o,"TestPingServer: error",!1,i)})}function W(e,i,o,s,h){try{h&&(h.onload=null,h.onerror=null,h.onabort=null,h.ontimeout=null),s(o)}catch(e){}}function Hc(){this.g=new tv}function Ic(e,i,o){let s=o||"";try{nc(e,function(e,o){let h=e;n(e)&&(h=ty(e)),i.push(s+o+"="+encodeURIComponent(h))})}catch(e){throw i.push(s+"type="+encodeURIComponent("_badmap")),e}}function Jc(e){this.l=e.Ub||null,this.j=e.eb||!1}function Kc(e,i){E.call(this),this.D=e,this.o=i,this.m=void 0,this.status=this.readyState=0,this.responseType=this.responseText=this.response=this.statusText="",this.onreadystatechange=null,this.u=new Headers,this.h=null,this.B="GET",this.A="",this.g=!1,this.v=this.j=this.l=null}function Nc(e){e.j.read().then(e.Pa.bind(e)).catch(e.ga.bind(e))}function Mc(e){e.readyState=4,e.l=null,e.j=null,e.v=null,Lc(e)}function Lc(e){e.onreadystatechange&&e.onreadystatechange.call(e)}function Oc(e){let i="";return qa(e,function(e,o){i+=o+":"+e+"\r\n"}),i}function Pc(e,i,o){t:{for(s in o){var s=!1;break t}s=!0}s||(o=Oc(o),"string"==typeof e?null!=o&&encodeURIComponent(String(o)):S(e,i,o))}function X(e){E.call(this),this.headers=new Map,this.o=e||null,this.h=!1,this.v=this.g=null,this.D="",this.m=0,this.l="",this.j=this.B=this.u=this.A=!1,this.I=null,this.H="",this.J=!1}(o=sc.prototype).add=function(e,i){U(this),this.i=null,e=V(this,e);var o=this.g.get(e);return o||this.g.set(e,o=[]),o.push(i),this.h+=1,this},o.forEach=function(e,i){U(this),this.g.forEach(function(o,s){o.forEach(function(o){e.call(i,o,s,this)},this)},this)},o.na=function(){U(this);let e=Array.from(this.g.values()),i=Array.from(this.g.keys()),o=[];for(let s=0;s<i.length;s++){let h=e[s];for(let e=0;e<h.length;e++)o.push(i[s])}return o},o.V=function(e){U(this);let i=[];if("string"==typeof e)Ec(this,e)&&(i=i.concat(this.g.get(V(this,e))));else{e=Array.from(this.g.values());for(let o=0;o<e.length;o++)i=i.concat(e[o])}return i},o.set=function(e,i){return U(this),this.i=null,Ec(this,e=V(this,e))&&(this.h-=this.g.get(e).length),this.g.set(e,[i]),this.h+=1,this},o.get=function(e,i){return e&&0<(e=this.V(e)).length?String(e[0]):i},o.toString=function(){if(this.i)return this.i;if(!this.g)return"";let e=[],i=Array.from(this.g.keys());for(var o=0;o<i.length;o++){var s=i[o];let f=encodeURIComponent(String(s)),l=this.V(s);for(s=0;s<l.length;s++){var h=f;""!==l[s]&&(h+="="+encodeURIComponent(String(l[s]))),e.push(h)}}return this.i=e.join("&")},r(Jc,kb),Jc.prototype.g=function(){return new Kc(this.l,this.j)},Jc.prototype.i=(e={},function(){return e}),r(Kc,E),(o=Kc.prototype).open=function(e,i){if(0!=this.readyState)throw this.abort(),Error("Error reopening a connection");this.B=e,this.A=i,this.readyState=1,Lc(this)},o.send=function(e){if(1!=this.readyState)throw this.abort(),Error("need to call open() first. ");this.g=!0;let i={headers:this.u,method:this.B,credentials:this.m,cache:void 0};e&&(i.body=e),(this.D||tt).fetch(new Request(this.A,i)).then(this.Sa.bind(this),this.ga.bind(this))},o.abort=function(){this.response=this.responseText="",this.u=new Headers,this.status=0,this.j&&this.j.cancel("Request was aborted.").catch(()=>{}),1<=this.readyState&&this.g&&4!=this.readyState&&(this.g=!1,Mc(this)),this.readyState=0},o.Sa=function(e){if(this.g&&(this.l=e,this.h||(this.status=this.l.status,this.statusText=this.l.statusText,this.h=e.headers,this.readyState=2,Lc(this)),this.g&&(this.readyState=3,Lc(this),this.g))){if("arraybuffer"===this.responseType)e.arrayBuffer().then(this.Qa.bind(this),this.ga.bind(this));else if(void 0!==tt.ReadableStream&&"body"in e){if(this.j=e.body.getReader(),this.o){if(this.responseType)throw Error('responseType must be empty for "streamBinaryChunks" mode responses.');this.response=[]}else this.response=this.responseText="",this.v=new TextDecoder;Nc(this)}else e.text().then(this.Ra.bind(this),this.ga.bind(this))}},o.Pa=function(e){if(this.g){if(this.o&&e.value)this.response.push(e.value);else if(!this.o){var i=e.value?e.value:new Uint8Array(0);(i=this.v.decode(i,{stream:!e.done}))&&(this.response=this.responseText+=i)}e.done?Mc(this):Lc(this),3==this.readyState&&Nc(this)}},o.Ra=function(e){this.g&&(this.response=this.responseText=e,Mc(this))},o.Qa=function(e){this.g&&(this.response=e,Mc(this))},o.ga=function(){this.g&&Mc(this)},o.setRequestHeader=function(e,i){this.u.append(e,i)},o.getResponseHeader=function(e){return this.h&&this.h.get(e.toLowerCase())||""},o.getAllResponseHeaders=function(){if(!this.h)return"";let e=[],i=this.h.entries();for(var o=i.next();!o.done;)e.push((o=o.value)[0]+": "+o[1]),o=i.next();return e.join("\r\n")},Object.defineProperty(Kc.prototype,"withCredentials",{get:function(){return"include"===this.m},set:function(e){this.m=e?"include":"same-origin"}}),r(X,E);var tM=/^https?$/i,tP=["POST","PUT"];function Sc(e,i){e.h=!1,e.g&&(e.j=!0,e.g.abort(),e.j=!1),e.l=i,e.m=5,Uc(e),Vc(e)}function Uc(e){e.A||(e.A=!0,F(e,"complete"),F(e,"error"))}function Wc(e){if(e.h&&void 0!==$&&(!e.v[1]||4!=P(e)||2!=e.Z())){if(e.u&&4==P(e))bb(e.Ea,0,e);else if(F(e,"readystatechange"),4==P(e)){e.h=!1;try{let l=e.Z();switch(l){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:var i,o,s=!0;break;default:s=!1}if(!(i=s)){if(o=0===l){var h=String(e.D).match(tT)[1]||null;!h&&tt.self&&tt.self.location&&(h=tt.self.location.protocol.slice(0,-1)),o=!tM.test(h?h.toLowerCase():"")}i=o}if(i)F(e,"complete"),F(e,"success");else{e.m=6;try{var f=2<P(e)?e.g.statusText:""}catch(e){f=""}e.l=f+" ["+e.Z()+"]",Uc(e)}}finally{Vc(e)}}}}function Vc(e,i){if(e.g){Tc(e);let o=e.g,s=e.v[0]?()=>{}:null;e.g=null,e.v=null,i||F(e,"ready");try{o.onreadystatechange=s}catch(e){}}}function Tc(e){e.I&&(tt.clearTimeout(e.I),e.I=null)}function P(e){return e.g?e.g.readyState:0}function Nb(e){try{if(!e.g)return null;if("response"in e.g)return e.g.response;switch(e.H){case"":case"text":return e.g.responseText;case"arraybuffer":if("mozResponseArrayBuffer"in e.g)return e.g.mozResponseArrayBuffer}return null}catch(e){return null}}function Vb(e){let i={};e=(e.g&&2<=P(e)&&e.g.getAllResponseHeaders()||"").split("\r\n");for(let s=0;s<e.length;s++){if(t(e[s]))continue;var o=va(e[s]);let h=o[0];if("string"!=typeof(o=o[1]))continue;o=o.trim();let f=i[h]||[];i[h]=f,f.push(o)}ra(i,function(e){return e.join(", ")})}function Xc(e,i,o){return o&&o.internalChannelParams&&o.internalChannelParams[e]||i}function Yc(e){this.Aa=0,this.i=[],this.j=new vb,this.ia=this.qa=this.I=this.W=this.g=this.ya=this.D=this.H=this.m=this.S=this.o=null,this.Ya=this.U=0,this.Va=Xc("failFast",!1,e),this.F=this.C=this.u=this.s=this.l=null,this.X=!0,this.za=this.T=-1,this.Y=this.v=this.B=0,this.Ta=Xc("baseRetryDelayMs",5e3,e),this.cb=Xc("retryDelaySeedMs",1e4,e),this.Wa=Xc("forwardChannelMaxRetries",2,e),this.wa=Xc("forwardChannelRequestTimeoutMs",2e4,e),this.pa=e&&e.xmlHttpFactory||void 0,this.Xa=e&&e.Tb||void 0,this.Ca=e&&e.useFetchStreams||!1,this.L=void 0,this.J=e&&e.supportsCrossDomainXhr||!1,this.K="",this.h=new ic(e&&e.concurrentRequestLimit),this.Da=new Hc,this.P=e&&e.fastHandshake||!1,this.O=e&&e.encodeInitMessageHeaders||!1,this.P&&this.O&&(this.O=!1),this.Ua=e&&e.Rb||!1,e&&e.xa&&this.j.xa(),e&&e.forceLongPolling&&(this.X=!1),this.ba=!this.P&&this.X&&e&&e.detectBufferingProxy||!1,this.ja=void 0,e&&e.longPollingTimeout&&0<e.longPollingTimeout&&(this.ja=e.longPollingTimeout),this.ca=void 0,this.R=0,this.M=!1,this.ka=this.A=null}function gc(e){if(Zc(e),3==e.G){var i=e.U++,o=N(e.I);if(S(o,"SID",e.K),S(o,"RID",i),S(o,"TYPE","terminate"),$c(e,o),(i=new M(e,e.j,i)).L=2,i.v=Ib(N(o)),o=!1,tt.navigator&&tt.navigator.sendBeacon)try{o=tt.navigator.sendBeacon(i.v.toString(),"")}catch(e){}!o&&tt.Image&&((new Image).src=i.v,o=!0),o||(i.g=Mb(i.j,null),i.g.ea(i.v)),i.F=Date.now(),Kb(i)}ad(e)}function Zb(e){e.g&&(Tb(e),e.g.cancel(),e.g=null)}function Zc(e){Zb(e),e.u&&(tt.clearTimeout(e.u),e.u=null),Yb(e),e.h.cancel(),e.s&&("number"==typeof e.s&&tt.clearTimeout(e.s),e.s=null)}function fc(e){if(!jc(e.h)&&!e.s){e.s=!0;var i=e.Ga;ti||Ea(),to||(ti(),to=!0),ts.add(i,e),e.B=0}}function bd(e,i){return!(ac(e.h)>=e.h.j-(e.s?1:0))&&(e.s?(e.i=i.D.concat(e.i),!0):1!=e.G&&2!=e.G&&!(e.B>=(e.Va?0:e.Wa))&&(e.s=ub(p(e.Ga,e,i),cd(e,e.B)),e.B++,!0))}function ed(e,i){var o;o=i?i.l:e.U++;let s=N(e.I);S(s,"SID",e.K),S(s,"RID",o),S(s,"AID",e.T),$c(e,s),e.m&&e.o&&Pc(s,e.m,e.o),o=new M(e,e.j,o,e.B+1),null===e.m&&(o.H=e.o),i&&(e.i=i.D.concat(e.i)),i=dd(e,o,1e3),o.I=Math.round(.5*e.wa)+Math.round(.5*e.wa*Math.random()),bc(e.h,o),Hb(o,s,i)}function $c(e,i){e.H&&qa(e.H,function(e,o){S(i,o,e)}),e.l&&nc({},function(e,o){S(i,o,e)})}function dd(e,i,o){o=Math.min(e.i.length,o);var s=e.l?p(e.l.Na,e.l,e):null;t:{var h=e.i;let i=-1;for(;;){let e=["count="+o];-1==i?0<o?(i=h[0].g,e.push("ofs="+i)):i=0:e.push("ofs="+i);let f=!0;for(let l=0;l<o;l++){let o=h[l].g,d=h[l].map;if(0>(o-=i))i=Math.max(0,h[l].g-100),f=!1;else try{Ic(d,e,"req"+o+"_")}catch(e){s&&s(d)}}if(f){s=e.join("&");break t}}}return e=e.i.splice(0,o),i.D=e,s}function ec(e){if(!e.g&&!e.u){e.Y=1;var i=e.Fa;ti||Ea(),to||(ti(),to=!0),ts.add(i,e),e.v=0}}function $b(e){return!e.g&&!e.u&&!(3<=e.v)&&(e.Y++,e.u=ub(p(e.Fa,e),cd(e,e.v)),e.v++,!0)}function Tb(e){null!=e.A&&(tt.clearTimeout(e.A),e.A=null)}function fd(e){e.g=new M(e,e.j,"rpc",e.Y),null===e.m&&(e.g.H=e.o),e.g.O=0;var i=N(e.qa);S(i,"RID","rpc"),S(i,"SID",e.K),S(i,"AID",e.T),S(i,"CI",e.F?"0":"1"),!e.F&&e.ja&&S(i,"TO",e.ja),S(i,"TYPE","xmlhttp"),$c(e,i),e.m&&e.o&&Pc(i,e.m,e.o),e.L&&(e.g.I=e.L);var o=e.g;e=e.ia,o.L=1,o.v=Ib(N(i)),o.m=null,o.P=!0,Jb(o,e)}function Yb(e){null!=e.C&&(tt.clearTimeout(e.C),e.C=null)}function Ub(e,i){var o=null;if(e.g==i){Yb(e),Tb(e),e.g=null;var s=2}else{if(!Xb(e.h,i))return;o=i.D,dc(e.h,i),s=1}if(0!=e.G){if(i.o){if(1==s){o=i.m?i.m.length:0,i=Date.now()-i.F;var h=e.B;F(s=qb(),new tb(s,o)),fc(e)}else ec(e)}else if(3==(h=i.s)||0==h&&0<i.X||!(1==s&&bd(e,i)||2==s&&$b(e)))switch(o&&0<o.length&&((i=e.h).i=i.i.concat(o)),h){case 1:R(e,5);break;case 4:R(e,10);break;case 3:R(e,6);break;default:R(e,2)}}}function cd(e,i){let o=e.Ta+Math.floor(Math.random()*e.cb);return e.isActive()||(o*=2),o*i}function R(e,i){if(e.j.info("Error code "+i),2==i){var o=p(e.fb,e),s=e.Xa;let i=!s;s=new T(s||"//www.google.com/images/cleardot.gif"),tt.location&&"http"==tt.location.protocol||qc(s,"https"),Ib(s),i?Fc(s.toString(),o):Gc(s.toString(),o)}else K(2);e.G=0,e.l&&e.l.sa(i),ad(e),Zc(e)}function ad(e){if(e.G=0,e.ka=[],e.l){let i=kc(e.h);(0!=i.length||0!=e.i.length)&&(ma(e.ka,i),ma(e.ka,e.i),e.h.i.length=0,la(e.i),e.i.length=0),e.l.ra()}}function cc(e,i,o){var s=o instanceof T?N(o):new T(o);if(""!=s.g)i&&(s.g=i+"."+s.g),rc(s,s.s);else{var h=tt.location;s=h.protocol,i=i?i+"."+h.hostname:h.hostname,h=+h.port;var f=new T(null);s&&qc(f,s),i&&(f.g=i),h&&rc(f,h),o&&(f.l=o),s=f}return o=e.D,i=e.ya,o&&i&&S(s,o,i),S(s,"VER",e.la),$c(e,s),s}function Mb(e,i,o){if(i&&!e.J)throw Error("Can't create secondary domain capable XhrIo object.");return(i=new X(e.Ca&&!e.pa?new Jc({eb:o}):e.pa)).Ha(e.J),i}function gd(){}function hd(){}function Y(e,i){E.call(this),this.g=new Yc(i),this.l=e,this.h=i&&i.messageUrlParams||null,e=i&&i.messageHeaders||null,i&&i.clientProtocolHeaderRequired&&(e?e["X-Client-Protocol"]="webchannel":e={"X-Client-Protocol":"webchannel"}),this.g.o=e,e=i&&i.initMessageHeaders||null,i&&i.messageContentType&&(e?e["X-WebChannel-Content-Type"]=i.messageContentType:e={"X-WebChannel-Content-Type":i.messageContentType}),i&&i.va&&(e?e["X-WebChannel-Client-Profile"]=i.va:e={"X-WebChannel-Client-Profile":i.va}),this.g.S=e,(e=i&&i.Sb)&&!t(e)&&(this.g.m=e),this.v=i&&i.supportsCrossDomainXhr||!1,this.u=i&&i.sendRawJson||!1,(i=i&&i.httpSessionIdParam)&&!t(i)&&(this.g.D=i,null!==(e=this.h)&&i in e&&i in(e=this.h)&&delete e[i]),this.j=new Z(this)}function id(e){nb.call(this),e.__headers__&&(this.headers=e.__headers__,this.statusCode=e.__status__,delete e.__headers__,delete e.__status__);var i=e.__sm__;if(i){t:{for(let o in i){e=o;break t}e=void 0}(this.i=e)&&(e=this.i,i=null!==i&&e in i?i[e]:void 0),this.data=i}else this.data=e}function jd(){ob.call(this),this.status=1}function Z(e){this.g=e}(o=X.prototype).Ha=function(e){this.J=e},o.ea=function(e,o,s,h){if(this.g)throw Error("[goog.net.XhrIo] Object is active with another request="+this.D+"; newUri="+e);o=o?o.toUpperCase():"GET",this.D=e,this.l="",this.m=0,this.A=!1,this.h=!0,this.g=this.o?this.o.g():i.g(),this.v=this.o?lb(this.o):lb(i),this.g.onreadystatechange=p(this.Ea,this);try{this.B=!0,this.g.open(o,String(e),!0),this.B=!1}catch(e){Sc(this,e);return}if(e=s||"",s=new Map(this.headers),h){if(Object.getPrototypeOf(h)===Object.prototype)for(var f in h)s.set(f,h[f]);else if("function"==typeof h.keys&&"function"==typeof h.get)for(let e of h.keys())s.set(e,h.get(e));else throw Error("Unknown input type for opt_headers: "+String(h))}for(let[i,l]of(h=Array.from(s.keys()).find(e=>"content-type"==e.toLowerCase()),f=tt.FormData&&e instanceof tt.FormData,!(0<=Array.prototype.indexOf.call(tP,o,void 0))||h||f||s.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8"),s))this.g.setRequestHeader(i,l);this.H&&(this.g.responseType=this.H),"withCredentials"in this.g&&this.g.withCredentials!==this.J&&(this.g.withCredentials=this.J);try{Tc(this),this.u=!0,this.g.send(e),this.u=!1}catch(e){Sc(this,e)}},o.abort=function(e){this.g&&this.h&&(this.h=!1,this.j=!0,this.g.abort(),this.j=!1,this.m=e||7,F(this,"complete"),F(this,"abort"),Vc(this))},o.N=function(){this.g&&(this.h&&(this.h=!1,this.j=!0,this.g.abort(),this.j=!1),Vc(this,!0)),X.aa.N.call(this)},o.Ea=function(){this.s||(this.B||this.u||this.j?Wc(this):this.bb())},o.bb=function(){Wc(this)},o.isActive=function(){return!!this.g},o.Z=function(){try{return 2<P(this)?this.g.status:-1}catch(e){return -1}},o.oa=function(){try{return this.g?this.g.responseText:""}catch(e){return""}},o.Oa=function(e){if(this.g){var i=this.g.responseText;return e&&0==i.indexOf(e)&&(i=i.substring(e.length)),tm(i)}},o.Ba=function(){return this.m},o.Ka=function(){return"string"==typeof this.l?this.l:String(this.l)},(o=Yc.prototype).la=8,o.G=1,o.connect=function(e,i,o,s){K(0),this.W=e,this.H=i||{},o&&void 0!==s&&(this.H.OSID=o,this.H.OAID=s),this.F=this.X,this.I=cc(this,null,this.W),fc(this)},o.Ga=function(e){if(this.s){if(this.s=null,1==this.G){if(!e){this.U=Math.floor(1e5*Math.random()),e=this.U++;let h=new M(this,this.j,e),f=this.o;if(this.S&&(f?ua(f=sa(f),this.S):f=this.S),null!==this.m||this.O||(h.H=f,f=null),this.P)t:{for(var i=0,o=0;o<this.i.length;o++){e:{var s=this.i[o];if("__data__"in s.map&&"string"==typeof(s=s.map.__data__)){s=s.length;break e}s=void 0}if(void 0===s)break;if(4096<(i+=s)){i=o;break t}if(4096===i||o===this.i.length-1){i=o+1;break t}}i=1e3}else i=1e3;i=dd(this,h,i),S(o=N(this.I),"RID",e),S(o,"CVER",22),this.D&&S(o,"X-HTTP-Session-Id",this.D),$c(this,o),f&&(this.O?i="headers="+encodeURIComponent(String(Oc(f)))+"&"+i:this.m&&Pc(o,this.m,f)),bc(this.h,h),this.Ua&&S(o,"TYPE","init"),this.P?(S(o,"$req",i),S(o,"SID","null"),h.T=!0,Hb(h,o,null)):Hb(h,o,i),this.G=2}}else 3==this.G&&(e?ed(this,e):0==this.i.length||jc(this.h)||ed(this))}},o.Fa=function(){if(this.u=null,fd(this),this.ba&&!(this.M||null==this.g||0>=this.R)){var e=2*this.R;this.j.info("BP detection timer enabled: "+e),this.A=ub(p(this.ab,this),e)}},o.ab=function(){this.A&&(this.A=null,this.j.info("BP detection timeout reached."),this.j.info("Buffering proxy detected and switch to long-polling!"),this.F=!1,this.M=!0,K(10),Zb(this),fd(this))},o.Za=function(){null!=this.C&&(this.C=null,Zb(this),$b(this),K(19))},o.fb=function(e){e?(this.j.info("Successfully pinged google.com"),K(2)):(this.j.info("Failed to ping google.com"),K(1))},o.isActive=function(){return!!this.l&&this.l.isActive(this)},(o=gd.prototype).ua=function(){},o.ta=function(){},o.sa=function(){},o.ra=function(){},o.isActive=function(){return!0},o.Na=function(){},hd.prototype.g=function(e,i){return new Y(e,i)},r(Y,E),Y.prototype.m=function(){this.g.l=this.j,this.v&&(this.g.J=!0),this.g.connect(this.l,this.h||void 0)},Y.prototype.close=function(){gc(this.g)},Y.prototype.o=function(e){var i=this.g;if("string"==typeof e){var o={};o.__data__=e,e=o}else this.u&&((o={}).__data__=ty(e),e=o);i.i.push(new t_(i.Ya++,e)),3==i.G&&fc(i)},Y.prototype.N=function(){this.g.l=null,delete this.j,gc(this.g),delete this.g,Y.aa.N.call(this)},r(id,nb),r(jd,ob),r(Z,gd),Z.prototype.ua=function(){F(this.g,"a")},Z.prototype.ta=function(e){F(this.g,new id(e))},Z.prototype.sa=function(e){F(this.g,new jd)},Z.prototype.ra=function(){F(this.g,"b")},hd.prototype.createWebChannel=hd.prototype.g,Y.prototype.send=Y.prototype.o,Y.prototype.open=Y.prototype.m,Y.prototype.close=Y.prototype.close,w=O.createWebChannelTransport=function(){return new hd},b=O.getStatEventTarget=function(){return qb()},g=O.Event=tE,d=O.Stat={mb:0,pb:1,qb:2,Jb:3,Ob:4,Lb:5,Mb:6,Kb:7,Ib:8,Nb:9,PROXY:10,NOPROXY:11,Gb:12,Cb:13,Db:14,Bb:15,Eb:16,Fb:17,ib:18,hb:19,jb:20},tB.NO_ERROR=0,tB.TIMEOUT=8,tB.HTTP_ERROR=6,l=O.ErrorCode=tB,tC.COMPLETE="complete",f=O.EventType=tC,mb.EventType=tw,tw.OPEN="a",tw.CLOSE="b",tw.ERROR="c",tw.MESSAGE="d",E.prototype.listen=E.prototype.K,h=O.WebChannel=mb,O.FetchXmlHttpFactory=Jc,X.prototype.listenOnce=X.prototype.L,X.prototype.getLastError=X.prototype.Ka,X.prototype.getLastErrorCode=X.prototype.Ba,X.prototype.getStatus=X.prototype.Z,X.prototype.getResponseJson=X.prototype.Oa,X.prototype.getResponseText=X.prototype.oa,X.prototype.send=X.prototype.ea,X.prototype.setWithCredentials=X.prototype.Ha,s=O.XhrIo=X}).apply(void 0!==_?_:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},994:function(e,i,o){"use strict";o.d(i,{ZF:function(){return s.ZF}});var s=o(3304);/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */(0,s.KN)("firebase","12.0.0","app")},8081:function(e,i,o){"use strict";o.d(i,{Xb:function(){return s.ab},v0:function(){return s.p},Aj:function(){return s.z},e5:function(){return s.ac},w7:function(){return s.D}});var s=o(9395);o(3304),o(4534),o(8650),o(3576)},4086:function(e,i,o){"use strict";o.d(i,{ET:function(){return s.ET},IO:function(){return s.IO},JU:function(){return s.JU},PL:function(){return s.PL},QT:function(){return s.QT},WA:function(){return s.WA},Xo:function(){return s.Xo},ad:function(){return s.ad},ar:function(){return s.ar},b9:function(){return s.b9},cf:function(){return s.cf},hJ:function(){return s.hJ},oe:function(){return s.oe},pl:function(){return s.pl},r7:function(){return s.r7}});var s=o(4471)}}]);