"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[995],{7965:function(e,t,r){var s=r(2265);function BellIcon({title:e,titleId:t,...r},i){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}))}let i=s.forwardRef(BellIcon);t.Z=i},9267:function(e,t,r){var s=r(2265);function BuildingOfficeIcon({title:e,titleId:t,...r},i){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h1.5m-1.5 3h1.5m-1.5 3h1.5m3-6H15m-1.5 3H15m-1.5 3H15M9 21v-3.375c0-.621.504-1.125 1.125-1.125h3.75c.621 0 1.125.504 1.125 1.125V21"}))}let i=s.forwardRef(BuildingOfficeIcon);t.Z=i},7805:function(e,t,r){var s=r(2265);function CheckIcon({title:e,titleId:t,...r},i){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))}let i=s.forwardRef(CheckIcon);t.Z=i},4164:function(e,t,r){var s=r(2265);function ChevronDownIcon({title:e,titleId:t,...r},i){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m19.5 8.25-7.5 7.5-7.5-7.5"}))}let i=s.forwardRef(ChevronDownIcon);t.Z=i},887:function(e,t,r){var s=r(2265);function ExclamationTriangleIcon({title:e,titleId:t,...r},i){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))}let i=s.forwardRef(ExclamationTriangleIcon);t.Z=i},4020:function(e,t,r){var s=r(2265);function MagnifyingGlassIcon({title:e,titleId:t,...r},i){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))}let i=s.forwardRef(MagnifyingGlassIcon);t.Z=i},5255:function(e,t,r){var s=r(2265);function PlusIcon({title:e,titleId:t,...r},i){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))}let i=s.forwardRef(PlusIcon);t.Z=i},3588:function(e,t,r){r.d(t,{D:function(){return useMutation}});var s=r(2265),i=r(7470),n=r(7987),u=r(2996),a=r(300),o=class extends u.l{#e;#t=void 0;#r;#s;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#i()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,a.VS)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#r,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,a.Ym)(t.mutationKey)!==(0,a.Ym)(this.options.mutationKey)?this.reset():this.#r?.state.status==="pending"&&this.#r.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#r?.removeObserver(this)}onMutationUpdate(e){this.#i(),this.#n(e)}getCurrentResult(){return this.#t}reset(){this.#r?.removeObserver(this),this.#r=void 0,this.#i(),this.#n()}mutate(e,t){return this.#s=t,this.#r?.removeObserver(this),this.#r=this.#e.getMutationCache().build(this.#e,this.options),this.#r.addObserver(this),this.#r.execute(e)}#i(){let e=this.#r?.state??(0,i.R)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#n(e){n.Vr.batch(()=>{if(this.#s&&this.hasListeners()){let t=this.#t.variables,r=this.#t.context;e?.type==="success"?(this.#s.onSuccess?.(e.data,t,r),this.#s.onSettled?.(e.data,null,t,r)):e?.type==="error"&&(this.#s.onError?.(e.error,t,r),this.#s.onSettled?.(void 0,e.error,t,r))}this.listeners.forEach(e=>{e(this.#t)})})}},l=r(8038);function useMutation(e,t){let r=(0,l.NL)(t),[i]=s.useState(()=>new o(r,e));s.useEffect(()=>{i.setOptions(e)},[i,e]);let u=s.useSyncExternalStore(s.useCallback(e=>i.subscribe(n.Vr.batchCalls(e)),[i]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),c=s.useCallback((e,t)=>{i.mutate(e,t).catch(a.ZT)},[i]);if(u.error&&(0,a.L3)(i.options.throwOnError,[u.error]))throw u.error;return{...u,mutate:c,mutateAsync:u.mutate}}},9891:function(e,t,r){r.d(t,{a:function(){return useQuery}});var s=r(9198),i=r(7987),n=r(3002),u=r(2996),a=r(8684),o=r(300),l=class extends u.l{constructor(e,t){super(),this.options=t,this.#e=e,this.#u=null,this.#a=(0,a.O)(),this.options.experimental_prefetchInRender||this.#a.reject(Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(t)}#e;#o=void 0;#l=void 0;#t=void 0;#c;#h;#a;#u;#d;#p;#f;#y;#m;#b;#v=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#o.addObserver(this),shouldFetchOnMount(this.#o,this.options)?this.#R():this.updateResult(),this.#g())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return shouldFetchOn(this.#o,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return shouldFetchOn(this.#o,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#O(),this.#w(),this.#o.removeObserver(this)}setOptions(e){let t=this.options,r=this.#o;if(this.options=this.#e.defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,o.Nc)(this.options.enabled,this.#o))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#E(),this.#o.setOptions(this.options),t._defaulted&&!(0,o.VS)(this.options,t)&&this.#e.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#o,observer:this});let s=this.hasListeners();s&&shouldFetchOptionally(this.#o,r,this.options,t)&&this.#R(),this.updateResult(),s&&(this.#o!==r||(0,o.Nc)(this.options.enabled,this.#o)!==(0,o.Nc)(t.enabled,this.#o)||(0,o.KC)(this.options.staleTime,this.#o)!==(0,o.KC)(t.staleTime,this.#o))&&this.#C();let i=this.#Q();s&&(this.#o!==r||(0,o.Nc)(this.options.enabled,this.#o)!==(0,o.Nc)(t.enabled,this.#o)||i!==this.#b)&&this.#I(i)}getOptimisticResult(e){let t=this.#e.getQueryCache().build(this.#e,e),r=this.createResult(t,e);return shouldAssignObserverCurrentProperties(this,r)&&(this.#t=r,this.#h=this.options,this.#c=this.#o.state),r}getCurrentResult(){return this.#t}trackResult(e,t){return new Proxy(e,{get:(e,r)=>(this.trackProp(r),t?.(r),Reflect.get(e,r))})}trackProp(e){this.#v.add(e)}getCurrentQuery(){return this.#o}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){let t=this.#e.defaultQueryOptions(e),r=this.#e.getQueryCache().build(this.#e,t);return r.fetch().then(()=>this.createResult(r,t))}fetch(e){return this.#R({...e,cancelRefetch:e.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#t))}#R(e){this.#E();let t=this.#o.fetch(this.options,e);return e?.throwOnError||(t=t.catch(o.ZT)),t}#C(){this.#O();let e=(0,o.KC)(this.options.staleTime,this.#o);if(o.sk||this.#t.isStale||!(0,o.PN)(e))return;let t=(0,o.Kp)(this.#t.dataUpdatedAt,e);this.#y=setTimeout(()=>{this.#t.isStale||this.updateResult()},t+1)}#Q(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#o):this.options.refetchInterval)??!1}#I(e){this.#w(),this.#b=e,!o.sk&&!1!==(0,o.Nc)(this.options.enabled,this.#o)&&(0,o.PN)(this.#b)&&0!==this.#b&&(this.#m=setInterval(()=>{(this.options.refetchIntervalInBackground||s.j.isFocused())&&this.#R()},this.#b))}#g(){this.#C(),this.#I(this.#Q())}#O(){this.#y&&(clearTimeout(this.#y),this.#y=void 0)}#w(){this.#m&&(clearInterval(this.#m),this.#m=void 0)}createResult(e,t){let r;let s=this.#o,i=this.options,u=this.#t,l=this.#c,c=this.#h,h=e!==s,d=h?e.state:this.#l,{state:p}=e,f={...p},y=!1;if(t._optimisticResults){let r=this.hasListeners(),u=!r&&shouldFetchOnMount(e,t),a=r&&shouldFetchOptionally(e,s,t,i);(u||a)&&(f={...f,...(0,n.z)(p.data,e.options)}),"isRestoring"===t._optimisticResults&&(f.fetchStatus="idle")}let{error:m,errorUpdatedAt:b,status:v}=f;r=f.data;let R=!1;if(void 0!==t.placeholderData&&void 0===r&&"pending"===v){let e;u?.isPlaceholderData&&t.placeholderData===c?.placeholderData?(e=u.data,R=!0):e="function"==typeof t.placeholderData?t.placeholderData(this.#f?.state.data,this.#f):t.placeholderData,void 0!==e&&(v="success",r=(0,o.oE)(u?.data,e,t),y=!0)}if(t.select&&void 0!==r&&!R){if(u&&r===l?.data&&t.select===this.#d)r=this.#p;else try{this.#d=t.select,r=t.select(r),r=(0,o.oE)(u?.data,r,t),this.#p=r,this.#u=null}catch(e){this.#u=e}}this.#u&&(m=this.#u,r=this.#p,b=Date.now(),v="error");let g="fetching"===f.fetchStatus,O="pending"===v,w="error"===v,E=O&&g,C=void 0!==r,Q={status:v,fetchStatus:f.fetchStatus,isPending:O,isSuccess:"success"===v,isError:w,isInitialLoading:E,isLoading:E,data:r,dataUpdatedAt:f.dataUpdatedAt,error:m,errorUpdatedAt:b,failureCount:f.fetchFailureCount,failureReason:f.fetchFailureReason,errorUpdateCount:f.errorUpdateCount,isFetched:f.dataUpdateCount>0||f.errorUpdateCount>0,isFetchedAfterMount:f.dataUpdateCount>d.dataUpdateCount||f.errorUpdateCount>d.errorUpdateCount,isFetching:g,isRefetching:g&&!O,isLoadingError:w&&!C,isPaused:"paused"===f.fetchStatus,isPlaceholderData:y,isRefetchError:w&&C,isStale:isStale(e,t),refetch:this.refetch,promise:this.#a,isEnabled:!1!==(0,o.Nc)(t.enabled,e)};if(this.options.experimental_prefetchInRender){let finalizeThenableIfPossible=e=>{"error"===Q.status?e.reject(Q.error):void 0!==Q.data&&e.resolve(Q.data)},recreateThenable=()=>{let e=this.#a=Q.promise=(0,a.O)();finalizeThenableIfPossible(e)},t=this.#a;switch(t.status){case"pending":e.queryHash===s.queryHash&&finalizeThenableIfPossible(t);break;case"fulfilled":("error"===Q.status||Q.data!==t.value)&&recreateThenable();break;case"rejected":("error"!==Q.status||Q.error!==t.reason)&&recreateThenable()}}return Q}updateResult(){let e=this.#t,t=this.createResult(this.#o,this.options);this.#c=this.#o.state,this.#h=this.options,void 0!==this.#c.data&&(this.#f=this.#o),(0,o.VS)(t,e)||(this.#t=t,this.#n({listeners:(()=>{if(!e)return!0;let{notifyOnChangeProps:t}=this.options,r="function"==typeof t?t():t;if("all"===r||!r&&!this.#v.size)return!0;let s=new Set(r??this.#v);return this.options.throwOnError&&s.add("error"),Object.keys(this.#t).some(t=>{let r=this.#t[t]!==e[t];return r&&s.has(t)})})()}))}#E(){let e=this.#e.getQueryCache().build(this.#e,this.options);if(e===this.#o)return;let t=this.#o;this.#o=e,this.#l=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#g()}#n(e){i.Vr.batch(()=>{e.listeners&&this.listeners.forEach(e=>{e(this.#t)}),this.#e.getQueryCache().notify({query:this.#o,type:"observerResultsUpdated"})})}};function shouldLoadOnMount(e,t){return!1!==(0,o.Nc)(t.enabled,e)&&void 0===e.state.data&&!("error"===e.state.status&&!1===t.retryOnMount)}function shouldFetchOnMount(e,t){return shouldLoadOnMount(e,t)||void 0!==e.state.data&&shouldFetchOn(e,t,t.refetchOnMount)}function shouldFetchOn(e,t,r){if(!1!==(0,o.Nc)(t.enabled,e)&&"static"!==(0,o.KC)(t.staleTime,e)){let s="function"==typeof r?r(e):r;return"always"===s||!1!==s&&isStale(e,t)}return!1}function shouldFetchOptionally(e,t,r,s){return(e!==t||!1===(0,o.Nc)(s.enabled,e))&&(!r.suspense||"error"!==e.state.status)&&isStale(e,r)}function isStale(e,t){return!1!==(0,o.Nc)(t.enabled,e)&&e.isStaleByTime((0,o.KC)(t.staleTime,e))}function shouldAssignObserverCurrentProperties(e,t){return!(0,o.VS)(e.getCurrentResult(),t)}var c=r(2265),h=r(8038);function createValue(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}r(7437);var d=c.createContext(createValue()),useQueryErrorResetBoundary=()=>c.useContext(d),ensurePreventErrorBoundaryRetry=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&!t.isReset()&&(e.retryOnMount=!1)},useClearResetErrorBoundary=e=>{c.useEffect(()=>{e.clearReset()},[e])},getHasError=({result:e,errorResetBoundary:t,throwOnError:r,query:s,suspense:i})=>e.isError&&!t.isReset()&&!e.isFetching&&s&&(i&&void 0===e.data||(0,o.L3)(r,[e.error,s])),p=c.createContext(!1),useIsRestoring=()=>c.useContext(p);p.Provider;var ensureSuspenseTimers=e=>{if(e.suspense){let clamp=e=>"static"===e?e:Math.max(e??1e3,1e3),t=e.staleTime;e.staleTime="function"==typeof t?(...e)=>clamp(t(...e)):clamp(t),"number"==typeof e.gcTime&&(e.gcTime=Math.max(e.gcTime,1e3))}},willFetch=(e,t)=>e.isLoading&&e.isFetching&&!t,shouldSuspend=(e,t)=>e?.suspense&&t.isPending,fetchOptimistic=(e,t,r)=>t.fetchOptimistic(e).catch(()=>{r.clearReset()});function useBaseQuery(e,t,r){let s=useIsRestoring(),n=useQueryErrorResetBoundary(),u=(0,h.NL)(r),a=u.defaultQueryOptions(e);u.getDefaultOptions().queries?._experimental_beforeQuery?.(a),a._optimisticResults=s?"isRestoring":"optimistic",ensureSuspenseTimers(a),ensurePreventErrorBoundaryRetry(a,n),useClearResetErrorBoundary(n);let l=!u.getQueryCache().get(a.queryHash),[d]=c.useState(()=>new t(u,a)),p=d.getOptimisticResult(a),f=!s&&!1!==e.subscribed;if(c.useSyncExternalStore(c.useCallback(e=>{let t=f?d.subscribe(i.Vr.batchCalls(e)):o.ZT;return d.updateResult(),t},[d,f]),()=>d.getCurrentResult(),()=>d.getCurrentResult()),c.useEffect(()=>{d.setOptions(a)},[a,d]),shouldSuspend(a,p))throw fetchOptimistic(a,d,n);if(getHasError({result:p,errorResetBoundary:n,throwOnError:a.throwOnError,query:u.getQueryCache().get(a.queryHash),suspense:a.suspense}))throw p.error;if(u.getDefaultOptions().queries?._experimental_afterQuery?.(a,p),a.experimental_prefetchInRender&&!o.sk&&willFetch(p,s)){let e=l?fetchOptimistic(a,d,n):u.getQueryCache().get(a.queryHash)?.promise;e?.catch(o.ZT).finally(()=>{d.updateResult()})}return a.notifyOnChangeProps?p:d.trackResult(p)}function useQuery(e,t){return useBaseQuery(e,l,t)}}}]);