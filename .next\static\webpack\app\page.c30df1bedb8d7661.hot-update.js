"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: function() { return /* binding */ auth; },\n/* harmony export */   db: function() { return /* binding */ db; }\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(app-pages-browser)/./node_modules/firebase/app/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n\n\n\nconst firebaseConfig = {\n    apiKey: \"AIzaSyDv9u4NhGvNFVVsou_IrXMO__rlfXfCKIk\",\n    authDomain: \"widigital-d6110.firebaseapp.com\",\n    projectId: \"widigital-d6110\",\n    storageBucket: \"widigital-d6110.firebasestorage.app\",\n    messagingSenderId: \"329879577024\",\n    appId: \"1:329879577024:web:0d8752f8175569f67d6825\"\n};\n// Initialize Firebase\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\n// Initialize Firebase services\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)(app);\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.getAuth)(app);\n// Connect to emulators in development\nif (true) {\n    try {\n        // Only connect if not already connected\n        if (!auth.config.emulator) {\n            (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.connectAuthEmulator)(auth, \"http://localhost:9099\");\n        }\n        // @ts-ignore\n        if (!db._delegate._databaseId.projectId.includes(\"localhost\")) {\n            (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.connectFirestoreEmulator)(db, \"localhost\", 8080);\n        }\n    } catch (error) {\n        console.log(\"Emulators already connected or not available\");\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (app);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/firebase.ts\n"));

/***/ })

});