'use client';

import Header from '@/components/Layout/Header';
import StatsCard from '@/components/Dashboard/StatsCard';
import TodayAppointments from '@/components/Dashboard/TodayAppointments';
import { useRealTimeDashboard } from '@/hooks/useDashboard';
import { usePatients } from '@/hooks/usePatients';
import {
  CalendarDaysIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

export default function Dashboard() {
  const { data: patients = [] } = usePatients();
  const stats = useRealTimeDashboard();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="flex-1 overflow-auto">
      <Header
        title="Dashboard"
        subtitle="Selamat datang di sistem manajemen klinik gigi"
      />

      <main className="p-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
          <StatsCard
            title="Appointment Hari Ini"
            value={stats.todayAppointments}
            icon={<CalendarDaysIcon className="w-6 h-6" />}
            color="blue"
            trend={{ value: 12, isPositive: true }}
          />

          <StatsCard
            title="Pendapatan Hari Ini"
            value={formatCurrency(stats.todayRevenue)}
            icon={<CurrencyDollarIcon className="w-6 h-6" />}
            color="green"
            trend={{ value: 8, isPositive: true }}
          />

          <StatsCard
            title="Total Pasien"
            value={patients.length}
            icon={<UserGroupIcon className="w-6 h-6" />}
            color="purple"
            trend={{ value: 15, isPositive: true }}
          />

          <StatsCard
            title="Pendapatan Bulan Ini"
            value={formatCurrency(stats.monthlyRevenue)}
            icon={<ChartBarIcon className="w-6 h-6" />}
            color="blue"
            trend={{ value: 5, isPositive: true }}
          />

          <StatsCard
            title="Pembayaran Tertunda"
            value={stats.pendingPayments}
            icon={<ClockIcon className="w-6 h-6" />}
            color="yellow"
          />

          <StatsCard
            title="Stok Menipis"
            value={stats.lowStockItems}
            icon={<ExclamationTriangleIcon className="w-6 h-6" />}
            color="red"
          />
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Today's Appointments */}
          <div className="lg:col-span-2">
            <TodayAppointments />
          </div>
          
          {/* Quick Actions */}
          <div className="space-y-6">
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Aksi Cepat</h3>
              <div className="space-y-3">
                <button className="w-full btn-primary text-left">
                  + Tambah Pasien Baru
                </button>
                <button className="w-full btn-secondary text-left">
                  + Buat Appointment
                </button>
                <button className="w-full btn-secondary text-left">
                  📋 Lihat Jadwal Hari Ini
                </button>
                <button className="w-full btn-secondary text-left">
                  💰 Buat Invoice
                </button>
              </div>
            </div>
            
            {/* Recent Activity */}
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Aktivitas Terbaru</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3 text-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-gray-600">Pembayaran dari Budi Santoso diterima</span>
                </div>
                <div className="flex items-center space-x-3 text-sm">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-gray-600">Appointment baru dari Sari Dewi</span>
                </div>
                <div className="flex items-center space-x-3 text-sm">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                  <span className="text-gray-600">Stok Anestesi Lidocaine menipis</span>
                </div>
                <div className="flex items-center space-x-3 text-sm">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <span className="text-gray-600">Treatment selesai untuk Andi Wijaya</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
