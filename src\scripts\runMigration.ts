/**
 * Migration Script Runner
 * 
 * This script helps you run the multi-tenant migration safely.
 * Run this script once to migrate your existing single-tenant data
 * to the new multi-tenant structure.
 * 
 * Usage:
 * 1. Make sure you have a backup of your Firestore data
 * 2. Update Firebase configuration if needed
 * 3. Run: npx ts-node src/scripts/runMigration.ts
 * 
 * WARNING: This is a one-time operation that modifies your database structure.
 * Make sure to test in a development environment first!
 */

import { DataMigration } from '../utils/migration';

async function runMigration() {
  console.log('🚀 Starting Multi-Tenant Migration');
  console.log('=====================================');
  
  // Safety check
  const environment = process.env.NODE_ENV || 'development';
  console.log(`Environment: ${environment}`);
  
  if (environment === 'production') {
    console.log('⚠️  WARNING: You are running migration in PRODUCTION!');
    console.log('Make sure you have a complete backup before proceeding.');
    
    // In a real scenario, you might want to add additional confirmation
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    const answer = await new Promise<string>((resolve) => {
      rl.question('Type "MIGRATE" to confirm you want to proceed: ', resolve);
    });
    
    rl.close();
    
    if (answer !== 'MIGRATE') {
      console.log('❌ Migration cancelled');
      process.exit(0);
    }
  }
  
  try {
    console.log('📊 Starting data migration...');
    console.log('This may take several minutes depending on your data size.');
    
    const startTime = Date.now();
    
    await DataMigration.migrateToMultiTenant();
    
    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);
    
    console.log('✅ Migration completed successfully!');
    console.log(`⏱️  Total time: ${duration} seconds`);
    console.log('');
    console.log('Next steps:');
    console.log('1. Deploy the new Firestore security rules');
    console.log('2. Test the application with different tenants');
    console.log('3. Verify data isolation between tenants');
    console.log('4. Update your application deployment');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    console.log('');
    console.log('Troubleshooting:');
    console.log('1. Check your Firebase configuration');
    console.log('2. Ensure you have proper permissions');
    console.log('3. Check the error message above for specific issues');
    console.log('4. Consider running in smaller batches if you have large datasets');
    
    process.exit(1);
  }
}

// Utility function to check migration status
async function checkMigrationStatus() {
  console.log('🔍 Checking migration status...');
  
  try {
    // You could add logic here to check if migration has already been run
    // For example, check if the new structure exists
    console.log('Migration status check completed');
  } catch (error) {
    console.error('Error checking migration status:', error);
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  switch (command) {
    case 'check':
      await checkMigrationStatus();
      break;
    case 'migrate':
    default:
      await runMigration();
      break;
  }
}

// Handle uncaught errors
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Run the script
if (require.main === module) {
  main().catch((error) => {
    console.error('Script execution failed:', error);
    process.exit(1);
  });
}

export { runMigration, checkMigrationStatus };
