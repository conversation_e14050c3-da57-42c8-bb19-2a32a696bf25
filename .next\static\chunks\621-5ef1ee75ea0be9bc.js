"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[621],{2621:function(e,t,a){a.d(t,{Z:function(){return Header}});var r=a(7437),n=a(4020),s=a(7965),i=a(2265),l=a(5495),d=a(3549),o=a(9267),c=a(4164),u=a(7805),m=a(5255);function TenantSwitcher(e){let{className:t=""}=e,{tenant:a,tenantId:n,loading:s,switchTenant:h,createTenant:g}=(0,l.S)(),{profile:x}=(0,d.a)(),[p,f]=(0,i.useState)(!1),[w,y]=(0,i.useState)(!1),[b,v]=(0,i.useState)(""),N=n?[{id:n,name:(null==a?void 0:a.name)||"Current Clinic"}]:[],handleSwitchTenant=async e=>{if(e===n){f(!1);return}try{await h(e),f(!1)}catch(e){console.error("Failed to switch tenant:",e)}},handleCreateTenant=async()=>{if(b.trim())try{y(!0),await g({name:b.trim(),address:"",phone:"",email:(null==x?void 0:x.email)||""}),v(""),f(!1)}catch(e){console.error("Failed to create tenant:",e)}finally{y(!1)}};return s?(0,r.jsxs)("div",{className:"flex items-center space-x-2 ".concat(t),children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded-lg animate-pulse"}),(0,r.jsx)("div",{className:"w-32 h-4 bg-gray-200 rounded animate-pulse"})]}):(0,r.jsxs)("div",{className:"relative ".concat(t),children:[(0,r.jsxs)("button",{onClick:()=>f(!p),className:"flex items-center space-x-3 w-full px-3 py-2 text-left bg-white border border-gray-200 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(o.Z,{className:"w-5 h-5 text-gray-400"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:(null==a?void 0:a.name)||"Select Clinic"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 truncate",children:(null==a?void 0:a.address)||"No address set"})]}),(0,r.jsx)(c.Z,{className:"w-4 h-4 text-gray-400 transition-transform ".concat(p?"transform rotate-180":"")})]}),p&&(0,r.jsx)("div",{className:"absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg",children:(0,r.jsxs)("div",{className:"py-1",children:[N.map(e=>(0,r.jsxs)("button",{onClick:()=>handleSwitchTenant(e.id),className:"flex items-center w-full px-3 py-2 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.name})}),e.id===n&&(0,r.jsx)(u.Z,{className:"w-4 h-4 text-primary-600"})]},e.id)),(0,r.jsx)("div",{className:"border-t border-gray-100 mt-1 pt-1",children:(0,r.jsxs)("div",{className:"px-3 py-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"text",placeholder:"New clinic name",value:b,onChange:e=>v(e.target.value),className:"flex-1 px-2 py-1 text-sm border border-gray-200 rounded focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500",onKeyPress:e=>{"Enter"===e.key&&handleCreateTenant()}}),(0,r.jsx)("button",{onClick:handleCreateTenant,disabled:!b.trim()||w,className:"flex items-center justify-center w-8 h-8 text-white bg-primary-600 rounded hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed",children:w?(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}):(0,r.jsx)(m.Z,{className:"w-4 h-4"})})]}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Create a new clinic"})]})})]})}),p&&(0,r.jsx)("div",{className:"fixed inset-0 z-40",onClick:()=>f(!1)})]})}function Header(e){let{title:t,subtitle:a}=e;return(0,r.jsx)("header",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:t}),a&&(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:a})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(TenantSwitcher,{className:"w-64"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(n.Z,{className:"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,r.jsx)("input",{type:"text",placeholder:"Cari pasien, appointment...",className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent w-80"})]}),(0,r.jsxs)("button",{className:"relative p-2 text-gray-400 hover:text-gray-600 transition-colors",children:[(0,r.jsx)(s.Z,{className:"w-6 h-6"}),(0,r.jsx)("span",{className:"absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"})]}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:new Date().toLocaleDateString("id-ID",{weekday:"long",year:"numeric",month:"long",day:"numeric"})})]})]})})}},3549:function(e,t,a){a.d(t,{H:function(){return AuthProvider},a:function(){return useAuth}});var r=a(7437),n=a(2265),s=a(8081),i=a(4086),l=a(6831);let d=(0,n.createContext)(null);function AuthProvider(e){let{children:t}=e,[a,o]=(0,n.useState)(null),[c,u]=(0,n.useState)(null),[m,h]=(0,n.useState)(!0);(0,n.useEffect)(()=>{let e=(0,s.Aj)(l.I,async e=>{if(o(e),e)try{let t=await (0,i.QT)((0,i.JU)(l.db,"users",e.uid));if(t.exists()){let a=t.data();u({id:e.uid,email:e.email||"",...a})}else{let t={id:e.uid,name:e.displayName||"User",email:e.email||"",role:"receptionist",tenantId:"tenant_".concat(e.uid),permissions:["read_patients","manage_appointments"],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};await (0,i.pl)((0,i.JU)(l.db,"users",e.uid),t),u(t)}}catch(e){console.error("Error fetching user profile:",e)}else u(null);h(!1)});return e},[]);let signIn=async(e,t)=>{try{await (0,s.e5)(l.I,e,t)}catch(e){throw Error(e.message)}},signUp=async(e,t,a)=>{try{let r=await (0,s.Xb)(l.I,e,t),n=r.user,d={id:n.uid,email:n.email||e,...a,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};await (0,i.pl)((0,i.JU)(l.db,"users",n.uid),d),u(d)}catch(e){throw Error(e.message)}},logout=async()=>{try{await (0,s.w7)(l.I)}catch(e){throw Error(e.message)}},updateProfile=async e=>{if(!a||!c)throw Error("No user logged in");try{let t={...c,...e,updatedAt:new Date().toISOString()};await (0,i.pl)((0,i.JU)(l.db,"users",a.uid),t,{merge:!0}),u(t)}catch(e){throw Error(e.message)}};return(0,r.jsx)(d.Provider,{value:{user:a,profile:c,loading:m,signIn,signUp,logout,updateProfile},children:t})}let useAuth=()=>{let e=(0,n.useContext)(d);if(!e)throw Error("useAuth must be used within AuthProvider");return e}},5495:function(e,t,a){a.d(t,{S:function(){return useTenant},z:function(){return TenantProvider}});var r=a(7437),n=a(2265),s=a(4086),i=a(6831),l=a(3549);let d=(0,n.createContext)(null);function TenantProvider(e){let{children:t}=e,[a,o]=(0,n.useState)(null),[c,u]=(0,n.useState)(null),[m,h]=(0,n.useState)(!0),{user:g,profile:x}=(0,l.a)();(0,n.useEffect)(()=>{let loadTenant=async()=>{if(!g||!(null==x?void 0:x.tenantId)){o(null),u(null),h(!1);return}try{let e=await (0,s.QT)((0,s.JU)(i.db,"dentalcare",x.tenantId,"settings","clinic"));if(e.exists()){let t=e.data();o({...t,id:x.tenantId}),u(x.tenantId)}else{let e={id:x.tenantId,name:"Klinik Gigi",address:"",phone:"",email:x.email,settings:{timezone:"Asia/Jakarta",currency:"IDR",dateFormat:"DD/MM/YYYY",businessHours:{start:"08:00",end:"17:00",days:["monday","tuesday","wednesday","thursday","friday","saturday"]}},createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};await (0,s.pl)((0,s.JU)(i.db,"dentalcare",x.tenantId,"settings","clinic"),e),o(e),u(x.tenantId)}}catch(e){console.error("Error loading tenant:",e)}finally{h(!1)}};loadTenant()},[g,x]);let switchTenant=async e=>{if(!g)throw Error("No user logged in");try{h(!0),await (0,s.pl)((0,s.JU)(i.db,"users",g.uid),{tenantId:e,updatedAt:new Date().toISOString()},{merge:!0});let t=await (0,s.QT)((0,s.JU)(i.db,"dentalcare",e,"settings","clinic"));if(t.exists()){let a=t.data();o({...a,id:e}),u(e)}}catch(e){throw console.error("Error switching tenant:",e),Error("Failed to switch tenant")}finally{h(!1)}},updateTenant=async e=>{if(!c||!a)throw Error("No tenant selected");try{let t={...a,...e,updatedAt:new Date().toISOString()};await (0,s.pl)((0,s.JU)(i.db,"dentalcare",c,"settings","clinic"),t,{merge:!0}),o(t)}catch(e){throw console.error("Error updating tenant:",e),Error("Failed to update tenant")}},createTenant=async e=>{if(!g)throw Error("No user logged in");try{let t="tenant_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),a={...e,id:t,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return await (0,s.pl)((0,s.JU)(i.db,"dentalcare",t,"settings","clinic"),a),await (0,s.pl)((0,s.JU)(i.db,"users",g.uid),{tenantId:t,updatedAt:new Date().toISOString()},{merge:!0}),o(a),u(t),t}catch(e){throw console.error("Error creating tenant:",e),Error("Failed to create tenant")}};return(0,r.jsx)(d.Provider,{value:{tenant:a,tenantId:c,loading:m,switchTenant,updateTenant,createTenant},children:t})}let useTenant=()=>{let e=(0,n.useContext)(d);if(!e)throw Error("useTenant must be used within TenantProvider");return e}},6831:function(e,t,a){a.d(t,{I:function(){return d},db:function(){return l}});var r=a(994),n=a(4086),s=a(8081);let i=(0,r.ZF)({apiKey:"AIzaSyDv9u4NhGvNFVVsou_IrXMO__rlfXfCKIk",authDomain:"widigital-d6110.firebaseapp.com",projectId:"widigital-d6110",storageBucket:"widigital-d6110.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:0d8752f8175569f67d6825"}),l=(0,n.ad)(i),d=(0,s.v0)(i)}}]);