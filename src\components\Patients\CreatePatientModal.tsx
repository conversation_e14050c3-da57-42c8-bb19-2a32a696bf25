'use client';

import { useState } from 'react';
import { Patient } from '@/types';
import { XMarkIcon, PhotoIcon } from '@heroicons/react/24/outline';
import { fileToBase64, validateImageFile, compressImageToBase64 } from '@/utils/imageUtils';

interface CreatePatientModalProps {
  onClose: () => void;
  onSubmit: (data: Omit<Patient, 'id' | 'medicalRecordNumber' | 'clinicId'>) => void;
  isLoading: boolean;
}

export function CreatePatientModal({ onClose, onSubmit, isLoading }: CreatePatientModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    address: '',
    nik: '',
    gender: 'male' as 'male' | 'female',
    avatar: '',
    emergencyContact: {
      name: '',
      phone: '',
      relationship: ''
    },
    medicalHistory: {
      allergies: [] as string[],
      medications: [] as string[],
      conditions: [] as string[]
    }
  });

  const [allergyInput, setAllergyInput] = useState('');
  const [medicationInput, setMedicationInput] = useState('');
  const [conditionInput, setConditionInput] = useState('');
  const [imageError, setImageError] = useState('');

  const handleInputChange = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof typeof prev],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setImageError('');

    const validation = validateImageFile(file);
    if (!validation.valid) {
      setImageError(validation.error || 'Invalid file');
      return;
    }

    try {
      const compressedBase64 = await compressImageToBase64(file, 400, 400, 0.8);
      setFormData(prev => ({ ...prev, avatar: compressedBase64 }));
    } catch (error) {
      setImageError('Failed to process image');
    }
  };

  const addToArray = (arrayName: 'allergies' | 'medications' | 'conditions', value: string) => {
    if (!value.trim()) return;
    
    setFormData(prev => ({
      ...prev,
      medicalHistory: {
        ...prev.medicalHistory,
        [arrayName]: [...prev.medicalHistory[arrayName], value.trim()]
      }
    }));

    // Clear input
    if (arrayName === 'allergies') setAllergyInput('');
    if (arrayName === 'medications') setMedicationInput('');
    if (arrayName === 'conditions') setConditionInput('');
  };

  const removeFromArray = (arrayName: 'allergies' | 'medications' | 'conditions', index: number) => {
    setFormData(prev => ({
      ...prev,
      medicalHistory: {
        ...prev.medicalHistory,
        [arrayName]: prev.medicalHistory[arrayName].filter((_, i) => i !== index)
      }
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const patientData: Omit<Patient, 'id' | 'medicalRecordNumber' | 'clinicId'> = {
      ...formData,
      totalVisits: 0,
      status: 'active',
      clinicalImages: [],
      dentalChart: []
    };

    onSubmit(patientData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">Tambah Pasien Baru</h2>
            <button 
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Avatar Upload */}
            <div className="flex items-center space-x-6">
              <div className="flex-shrink-0">
                {formData.avatar ? (
                  <img
                    src={formData.avatar}
                    alt="Avatar"
                    className="w-24 h-24 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-24 h-24 rounded-full bg-gray-200 flex items-center justify-center">
                    <PhotoIcon className="w-8 h-8 text-gray-400" />
                  </div>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Foto Pasien
                </label>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"
                />
                {imageError && (
                  <p className="text-red-600 text-sm mt-1">{imageError}</p>
                )}
              </div>
            </div>

            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nama Lengkap *
                </label>
                <input
                  type="text"
                  required
                  className="input-field"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email
                </label>
                <input
                  type="email"
                  className="input-field"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nomor Telepon *
                </label>
                <input
                  type="tel"
                  required
                  className="input-field"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tanggal Lahir *
                </label>
                <input
                  type="date"
                  required
                  className="input-field"
                  value={formData.dateOfBirth}
                  onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  NIK *
                </label>
                <input
                  type="text"
                  required
                  className="input-field"
                  value={formData.nik}
                  onChange={(e) => handleInputChange('nik', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Jenis Kelamin *
                </label>
                <select
                  required
                  className="input-field"
                  value={formData.gender}
                  onChange={(e) => handleInputChange('gender', e.target.value)}
                >
                  <option value="male">Laki-laki</option>
                  <option value="female">Perempuan</option>
                </select>
              </div>
            </div>

            {/* Address */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Alamat *
              </label>
              <textarea
                required
                rows={3}
                className="input-field"
                value={formData.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
              />
            </div>

            {/* Emergency Contact */}
            <div className="border-t pt-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Kontak Darurat</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Nama *
                  </label>
                  <input
                    type="text"
                    required
                    className="input-field"
                    value={formData.emergencyContact.name}
                    onChange={(e) => handleInputChange('emergencyContact.name', e.target.value)}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Nomor Telepon *
                  </label>
                  <input
                    type="tel"
                    required
                    className="input-field"
                    value={formData.emergencyContact.phone}
                    onChange={(e) => handleInputChange('emergencyContact.phone', e.target.value)}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Hubungan *
                  </label>
                  <input
                    type="text"
                    required
                    className="input-field"
                    placeholder="e.g., Suami, Istri, Anak"
                    value={formData.emergencyContact.relationship}
                    onChange={(e) => handleInputChange('emergencyContact.relationship', e.target.value)}
                  />
                </div>
              </div>
            </div>

            {/* Medical History */}
            <div className="border-t pt-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Riwayat Medis</h3>
              
              {/* Allergies */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Alergi
                </label>
                <div className="flex space-x-2 mb-2">
                  <input
                    type="text"
                    className="input-field flex-1"
                    placeholder="Tambah alergi"
                    value={allergyInput}
                    onChange={(e) => setAllergyInput(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addToArray('allergies', allergyInput))}
                  />
                  <button
                    type="button"
                    onClick={() => addToArray('allergies', allergyInput)}
                    className="btn-secondary"
                  >
                    Tambah
                  </button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {formData.medicalHistory.allergies.map((allergy, index) => (
                    <span
                      key={index}
                      className="bg-red-100 text-red-800 px-2 py-1 rounded-full text-sm flex items-center"
                    >
                      {allergy}
                      <button
                        type="button"
                        onClick={() => removeFromArray('allergies', index)}
                        className="ml-1 text-red-600 hover:text-red-800"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
              </div>

              {/* Medications */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Obat-obatan
                </label>
                <div className="flex space-x-2 mb-2">
                  <input
                    type="text"
                    className="input-field flex-1"
                    placeholder="Tambah obat"
                    value={medicationInput}
                    onChange={(e) => setMedicationInput(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addToArray('medications', medicationInput))}
                  />
                  <button
                    type="button"
                    onClick={() => addToArray('medications', medicationInput)}
                    className="btn-secondary"
                  >
                    Tambah
                  </button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {formData.medicalHistory.medications.map((medication, index) => (
                    <span
                      key={index}
                      className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm flex items-center"
                    >
                      {medication}
                      <button
                        type="button"
                        onClick={() => removeFromArray('medications', index)}
                        className="ml-1 text-blue-600 hover:text-blue-800"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
              </div>

              {/* Conditions */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Kondisi Medis
                </label>
                <div className="flex space-x-2 mb-2">
                  <input
                    type="text"
                    className="input-field flex-1"
                    placeholder="Tambah kondisi medis"
                    value={conditionInput}
                    onChange={(e) => setConditionInput(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addToArray('conditions', conditionInput))}
                  />
                  <button
                    type="button"
                    onClick={() => addToArray('conditions', conditionInput)}
                    className="btn-secondary"
                  >
                    Tambah
                  </button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {formData.medicalHistory.conditions.map((condition, index) => (
                    <span
                      key={index}
                      className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-sm flex items-center"
                    >
                      {condition}
                      <button
                        type="button"
                        onClick={() => removeFromArray('conditions', index)}
                        className="ml-1 text-yellow-600 hover:text-yellow-800"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
              </div>
            </div>

            {/* Submit Buttons */}
            <div className="flex justify-end space-x-3 pt-6 border-t">
              <button
                type="button"
                onClick={onClose}
                className="btn-secondary"
                disabled={isLoading}
              >
                Batal
              </button>
              <button
                type="submit"
                className="btn-primary"
                disabled={isLoading}
              >
                {isLoading ? 'Menyimpan...' : 'Simpan Pasien'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
