import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  onSnapshot,
  Timestamp,
  writeBatch
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Patient } from '@/types';

export class PatientService {
  private collection = collection(db, 'patients');

  /**
   * Generate unique medical record number
   */
  private async generateMedicalRecordNumber(clinicId: string): Promise<string> {
    const year = new Date().getFullYear();
    const q = query(
      this.collection,
      where('clinicId', '==', clinicId),
      where('medicalRecordNumber', '>=', `RM${year}`),
      where('medicalRecordNumber', '<', `RM${year + 1}`),
      orderBy('medicalRecordNumber', 'desc'),
      limit(1)
    );
    
    const snapshot = await getDocs(q);
    
    if (snapshot.empty) {
      return `RM${year}001`;
    }
    
    const lastRecord = snapshot.docs[0].data();
    const lastNumber = parseInt(lastRecord.medicalRecordNumber.slice(-3));
    const nextNumber = (lastNumber + 1).toString().padStart(3, '0');
    
    return `RM${year}${nextNumber}`;
  }

  /**
   * Create new patient
   */
  async createPatient(patientData: Omit<Patient, 'id' | 'medicalRecordNumber'>): Promise<string> {
    try {
      const medicalRecordNumber = await this.generateMedicalRecordNumber(patientData.clinicId);
      
      const newPatient: Omit<Patient, 'id'> = {
        ...patientData,
        medicalRecordNumber,
        totalVisits: 0,
        status: 'active',
        dentalChart: this.initializeDentalChart(),
        clinicalImages: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      const docRef = await addDoc(this.collection, newPatient);
      return docRef.id;
    } catch (error) {
      console.error('Error creating patient:', error);
      throw new Error('Failed to create patient');
    }
  }

  /**
   * Initialize dental chart with all 32 teeth
   */
  private initializeDentalChart() {
    const dentalChart = [];
    for (let i = 1; i <= 32; i++) {
      dentalChart.push({
        toothNumber: i,
        condition: 'healthy' as const,
        notes: '',
        updatedAt: new Date().toISOString()
      });
    }
    return dentalChart;
  }

  /**
   * Get patient by ID
   */
  async getPatient(id: string): Promise<Patient | null> {
    try {
      const docRef = doc(this.collection, id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as Patient;
      }
      return null;
    } catch (error) {
      console.error('Error getting patient:', error);
      throw new Error('Failed to get patient');
    }
  }

  /**
   * Get all patients for a clinic
   */
  async getPatientsByClinic(clinicId: string): Promise<Patient[]> {
    try {
      const q = query(
        this.collection,
        where('clinicId', '==', clinicId),
        orderBy('name')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Patient[];
    } catch (error) {
      console.error('Error getting patients:', error);
      throw new Error('Failed to get patients');
    }
  }

  /**
   * Search patients
   */
  async searchPatients(clinicId: string, searchTerm: string): Promise<Patient[]> {
    try {
      // Get all patients first (client-side filtering for now)
      const patients = await this.getPatientsByClinic(clinicId);
      
      const lowercaseSearch = searchTerm.toLowerCase();
      return patients.filter(patient => 
        patient.name.toLowerCase().includes(lowercaseSearch) ||
        patient.medicalRecordNumber.toLowerCase().includes(lowercaseSearch) ||
        patient.phone.includes(searchTerm) ||
        patient.email.toLowerCase().includes(lowercaseSearch)
      );
    } catch (error) {
      console.error('Error searching patients:', error);
      throw new Error('Failed to search patients');
    }
  }

  /**
   * Update patient
   */
  async updatePatient(id: string, updates: Partial<Patient>): Promise<void> {
    try {
      const docRef = doc(this.collection, id);
      await updateDoc(docRef, {
        ...updates,
        updatedAt: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error updating patient:', error);
      throw new Error('Failed to update patient');
    }
  }

  /**
   * Delete patient
   */
  async deletePatient(id: string): Promise<void> {
    try {
      const docRef = doc(this.collection, id);
      await deleteDoc(docRef);
    } catch (error) {
      console.error('Error deleting patient:', error);
      throw new Error('Failed to delete patient');
    }
  }

  /**
   * Add clinical image to patient
   */
  async addClinicalImage(
    patientId: string, 
    imageData: {
      type: 'intraoral' | 'extraoral' | 'xray';
      image: string; // base64
      description?: string;
    }
  ): Promise<void> {
    try {
      const patient = await this.getPatient(patientId);
      if (!patient) throw new Error('Patient not found');

      const newImage = {
        id: Date.now().toString(),
        ...imageData,
        date: new Date().toISOString()
      };

      const updatedImages = [...(patient.clinicalImages || []), newImage];
      
      await this.updatePatient(patientId, {
        clinicalImages: updatedImages
      });
    } catch (error) {
      console.error('Error adding clinical image:', error);
      throw new Error('Failed to add clinical image');
    }
  }

  /**
   * Update dental chart
   */
  async updateDentalChart(
    patientId: string, 
    toothNumber: number, 
    condition: Patient['dentalChart'][0]['condition'],
    notes?: string
  ): Promise<void> {
    try {
      const patient = await this.getPatient(patientId);
      if (!patient) throw new Error('Patient not found');

      const updatedChart = patient.dentalChart?.map(tooth => 
        tooth.toothNumber === toothNumber 
          ? { 
              ...tooth, 
              condition, 
              notes: notes || tooth.notes,
              updatedAt: new Date().toISOString() 
            }
          : tooth
      ) || [];

      await this.updatePatient(patientId, {
        dentalChart: updatedChart
      });
    } catch (error) {
      console.error('Error updating dental chart:', error);
      throw new Error('Failed to update dental chart');
    }
  }

  /**
   * Real-time subscription to patients
   */
  subscribeToPatients(clinicId: string, callback: (patients: Patient[]) => void) {
    const q = query(
      this.collection,
      where('clinicId', '==', clinicId),
      orderBy('name')
    );

    return onSnapshot(q, (snapshot) => {
      const patients = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Patient[];
      callback(patients);
    }, (error) => {
      console.error('Error in patients subscription:', error);
    });
  }
}

export const patientService = new PatientService();
