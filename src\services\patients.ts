import {
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  onSnapshot,
  Timestamp,
  writeBatch
} from 'firebase/firestore';
import { Patient } from '@/types';
import { TenantService } from './base/TenantService';

export class PatientService extends TenantService {

  /**
   * Generate unique medical record number
   */
  private async generateMedicalRecordNumber(): Promise<string> {
    const year = new Date().getFullYear();
    const q = query(
      this.getCollection('patients'),
      where('medicalRecordNumber', '>=', `RM${year}`),
      where('medicalRecordNumber', '<', `RM${year + 1}`),
      orderBy('medicalRecordNumber', 'desc'),
      limit(1)
    );

    const snapshot = await getDocs(q);

    if (snapshot.empty) {
      return `RM${year}001`;
    }

    const lastRecord = snapshot.docs[0].data() as Patient;
    const lastNumber = parseInt(lastRecord.medicalRecordNumber.slice(-3));
    const nextNumber = (lastNumber + 1).toString().padStart(3, '0');

    return `RM${year}${nextNumber}`;
  }

  /**
   * Create new patient
   */
  async createPatient(patientData: Omit<Patient, 'id' | 'medicalRecordNumber'>): Promise<string> {
    try {
      this.validateTenantAccess();
      const medicalRecordNumber = await this.generateMedicalRecordNumber();

      const newPatient: Omit<Patient, 'id'> = {
        ...patientData,
        medicalRecordNumber,
        totalVisits: 0,
        status: 'active',
        dentalChart: this.initializeDentalChart(),
        clinicalImages: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const docRef = await addDoc(this.getCollection('patients'), newPatient);
      return docRef.id;
    } catch (error) {
      this.handleError(error, 'create patient');
    }
  }

  /**
   * Initialize dental chart with all 32 teeth
   */
  private initializeDentalChart() {
    const dentalChart = [];
    for (let i = 1; i <= 32; i++) {
      dentalChart.push({
        toothNumber: i,
        condition: 'healthy' as const,
        notes: '',
        updatedAt: new Date().toISOString()
      });
    }
    return dentalChart;
  }

  /**
   * Get patient by ID
   */
  async getPatient(id: string): Promise<Patient | null> {
    try {
      this.validateTenantAccess();
      const docRef = this.getDocument('patients', id);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as Patient;
      }
      return null;
    } catch (error) {
      this.handleError(error, 'get patient');
    }
  }

  /**
   * Get all patients for current tenant
   */
  async getPatients(): Promise<Patient[]> {
    try {
      this.validateTenantAccess();
      const q = query(
        this.getCollection('patients'),
        orderBy('name')
      );

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Patient[];
    } catch (error) {
      this.handleError(error, 'get patients');
    }
  }

  /**
   * Search patients
   */
  async searchPatients(searchTerm: string): Promise<Patient[]> {
    try {
      this.validateTenantAccess();
      // Get all patients first (client-side filtering for now)
      const patients = await this.getPatients();

      const lowercaseSearch = searchTerm.toLowerCase();
      return patients.filter((patient: Patient) =>
        patient.name.toLowerCase().includes(lowercaseSearch) ||
        patient.medicalRecordNumber.toLowerCase().includes(lowercaseSearch) ||
        patient.phone.includes(searchTerm) ||
        patient.email.toLowerCase().includes(lowercaseSearch)
      );
    } catch (error) {
      this.handleError(error, 'search patients');
    }
  }

  /**
   * Update patient
   */
  async updatePatient(id: string, updates: Partial<Patient>): Promise<void> {
    try {
      this.validateTenantAccess();
      const docRef = this.getDocument('patients', id);
      await updateDoc(docRef, {
        ...updates,
        updatedAt: new Date().toISOString()
      });
    } catch (error) {
      this.handleError(error, 'update patient');
    }
  }

  /**
   * Delete patient
   */
  async deletePatient(id: string): Promise<void> {
    try {
      this.validateTenantAccess();
      const docRef = this.getDocument('patients', id);
      await deleteDoc(docRef);
    } catch (error) {
      this.handleError(error, 'delete patient');
    }
  }

  /**
   * Add clinical image to patient
   */
  async addClinicalImage(
    patientId: string,
    imageData: {
      type: 'intraoral' | 'extraoral' | 'xray';
      image: string; // base64
      description?: string;
    }
  ): Promise<void> {
    try {
      this.validateTenantAccess();
      const patient = await this.getPatient(patientId);
      if (!patient) throw new Error('Patient not found');

      const newImage = {
        id: Date.now().toString(),
        ...imageData,
        date: new Date().toISOString()
      };

      const updatedImages = [...(patient.clinicalImages || []), newImage];

      await this.updatePatient(patientId, {
        clinicalImages: updatedImages
      });
    } catch (error) {
      this.handleError(error, 'add clinical image');
    }
  }

  /**
   * Update dental chart
   */
  async updateDentalChart(
    patientId: string,
    toothNumber: number,
    condition: Patient['dentalChart'][0]['condition'],
    notes?: string
  ): Promise<void> {
    try {
      this.validateTenantAccess();
      const patient = await this.getPatient(patientId);
      if (!patient) throw new Error('Patient not found');

      const updatedChart = patient.dentalChart?.map(tooth =>
        tooth.toothNumber === toothNumber
          ? {
              ...tooth,
              condition,
              notes: notes || tooth.notes,
              updatedAt: new Date().toISOString()
            }
          : tooth
      ) || [];

      await this.updatePatient(patientId, {
        dentalChart: updatedChart
      });
    } catch (error) {
      this.handleError(error, 'update dental chart');
    }
  }

  /**
   * Real-time subscription to patients
   */
  subscribeToPatients(callback: (patients: Patient[]) => void) {
    this.validateTenantAccess();
    const q = query(
      this.getCollection('patients'),
      orderBy('name')
    );

    return onSnapshot(q, (snapshot) => {
      const patients = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Patient[];
      callback(patients);
    }, (error) => {
      console.error('Error in patients subscription:', error);
    });
  }
}

// Note: PatientService now requires tenantId, so we'll create instances via TenantServiceRegistry
// export const patientService = new PatientService();
