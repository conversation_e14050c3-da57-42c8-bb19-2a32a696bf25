'use client';

import { useState } from 'react';
import { useTenant } from '@/contexts/TenantContext';
import { useAuth } from '@/contexts/AuthContext';
import { 
  ChevronDownIcon,
  BuildingOfficeIcon,
  PlusIcon,
  CheckIcon
} from '@heroicons/react/24/outline';

interface TenantSwitcherProps {
  className?: string;
}

export default function TenantSwitcher({ className = '' }: TenantSwitcherProps) {
  const { tenant, tenantId, loading, switchTenant, createTenant } = useTenant();
  const { profile } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [newTenantName, setNewTenantName] = useState('');

  // For demo purposes, we'll show available tenants
  // In a real app, you might fetch this from a user's accessible tenants
  const availableTenants = [
    { id: tenantId, name: tenant?.name || 'Current Clinic' },
    // Add more tenants here if user has access to multiple
  ];

  const handleSwitchTenant = async (newTenantId: string) => {
    if (newTenantId === tenantId) {
      setIsOpen(false);
      return;
    }

    try {
      await switchTenant(newTenantId);
      setIsOpen(false);
    } catch (error) {
      console.error('Failed to switch tenant:', error);
      // You might want to show a toast notification here
    }
  };

  const handleCreateTenant = async () => {
    if (!newTenantName.trim()) return;

    try {
      setIsCreating(true);
      await createTenant({
        name: newTenantName.trim(),
        address: '',
        phone: '',
        email: profile?.email || ''
      });
      setNewTenantName('');
      setIsOpen(false);
    } catch (error) {
      console.error('Failed to create tenant:', error);
      // You might want to show a toast notification here
    } finally {
      setIsCreating(false);
    }
  };

  if (loading) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className="w-8 h-8 bg-gray-200 rounded-lg animate-pulse"></div>
        <div className="w-32 h-4 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-3 w-full px-3 py-2 text-left bg-white border border-gray-200 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
      >
        <div className="flex-shrink-0">
          <BuildingOfficeIcon className="w-5 h-5 text-gray-400" />
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-gray-900 truncate">
            {tenant?.name || 'Select Clinic'}
          </p>
          <p className="text-xs text-gray-500 truncate">
            {tenant?.address || 'No address set'}
          </p>
        </div>
        <ChevronDownIcon 
          className={`w-4 h-4 text-gray-400 transition-transform ${
            isOpen ? 'transform rotate-180' : ''
          }`} 
        />
      </button>

      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
          <div className="py-1">
            {availableTenants.map((availableTenant) => (
              <button
                key={availableTenant.id}
                onClick={() => handleSwitchTenant(availableTenant.id)}
                className="flex items-center w-full px-3 py-2 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50"
              >
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">
                    {availableTenant.name}
                  </p>
                </div>
                {availableTenant.id === tenantId && (
                  <CheckIcon className="w-4 h-4 text-primary-600" />
                )}
              </button>
            ))}
            
            <div className="border-t border-gray-100 mt-1 pt-1">
              <div className="px-3 py-2">
                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    placeholder="New clinic name"
                    value={newTenantName}
                    onChange={(e) => setNewTenantName(e.target.value)}
                    className="flex-1 px-2 py-1 text-sm border border-gray-200 rounded focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        handleCreateTenant();
                      }
                    }}
                  />
                  <button
                    onClick={handleCreateTenant}
                    disabled={!newTenantName.trim() || isCreating}
                    className="flex items-center justify-center w-8 h-8 text-white bg-primary-600 rounded hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isCreating ? (
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    ) : (
                      <PlusIcon className="w-4 h-4" />
                    )}
                  </button>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Create a new clinic
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Overlay to close dropdown when clicking outside */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}
