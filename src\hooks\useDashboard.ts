import { useEffect, useState } from 'react';
import { collection, query, where, onSnapshot, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { useTenant } from '@/contexts/TenantContext';

interface DashboardStats {
  todayAppointments: number;
  todayRevenue: number;
  totalPatients: number;
  monthlyRevenue: number;
  pendingPayments: number;
  lowStockItems: number;
}

export function useRealTimeDashboard() {
  const { tenantId } = useTenant();
  const [stats, setStats] = useState<DashboardStats>({
    todayAppointments: 0,
    todayRevenue: 0,
    totalPatients: 0,
    monthlyRevenue: 0,
    pendingPayments: 0,
    lowStockItems: 0,
  });

  useEffect(() => {
    if (!tenantId) return;

    const today = new Date().toISOString().split('T')[0];
    const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format

    // Subscribe to today's appointments
    const appointmentsQuery = query(
      collection(db, 'dentalcare', tenantId, 'appointments'),
      where('date', '==', today)
    );

    const unsubscribeAppointments = onSnapshot(appointmentsQuery, (snapshot) => {
      setStats(prev => ({
        ...prev,
        todayAppointments: snapshot.size
      }));
    });

    // Subscribe to patients count
    const patientsQuery = query(
      collection(db, 'dentalcare', tenantId, 'patients')
    );

    const unsubscribePatients = onSnapshot(patientsQuery, (snapshot) => {
      setStats(prev => ({
        ...prev,
        totalPatients: snapshot.size
      }));
    });

    // Subscribe to pending invoices
    const invoicesQuery = query(
      collection(db, 'dentalcare', tenantId, 'invoices'),
      where('status', 'in', ['draft', 'sent', 'overdue'])
    );

    const unsubscribeInvoices = onSnapshot(invoicesQuery, (snapshot) => {
      setStats(prev => ({
        ...prev,
        pendingPayments: snapshot.size
      }));
    });

    // Get monthly revenue (one-time fetch for now)
    const fetchMonthlyRevenue = async () => {
      try {
        const monthlyInvoicesQuery = query(
          collection(db, 'dentalcare', tenantId, 'invoices'),
          where('status', '==', 'paid'),
          where('date', '>=', currentMonth + '-01'),
          where('date', '<', getNextMonth(currentMonth) + '-01')
        );

        const snapshot = await getDocs(monthlyInvoicesQuery);
        let totalRevenue = 0;
        let todayRevenue = 0;

        snapshot.docs.forEach(doc => {
          const invoice = doc.data();
          totalRevenue += invoice.total || 0;

          if (invoice.date === today) {
            todayRevenue += invoice.total || 0;
          }
        });

        setStats(prev => ({
          ...prev,
          monthlyRevenue: totalRevenue,
          todayRevenue: todayRevenue
        }));
      } catch (error) {
        console.error('Error fetching monthly revenue:', error);
      }
    };

    fetchMonthlyRevenue();

    // Subscribe to inventory for low stock items
    const inventoryQuery = query(
      collection(db, 'dentalcare', tenantId, 'inventory')
    );

    const unsubscribeInventory = onSnapshot(inventoryQuery, (snapshot) => {
      let lowStockCount = 0;
      snapshot.docs.forEach(doc => {
        const item = doc.data();
        if (item.currentStock <= item.minStock) {
          lowStockCount++;
        }
      });

      setStats(prev => ({
        ...prev,
        lowStockItems: lowStockCount
      }));
    });

    return () => {
      unsubscribeAppointments();
      unsubscribePatients();
      unsubscribeInvoices();
      unsubscribeInventory();
    };
  }, [tenantId]);

  return stats;
}

function getNextMonth(currentMonth: string): string {
  const [year, month] = currentMonth.split('-').map(Number);
  const nextMonth = month === 12 ? 1 : month + 1;
  const nextYear = month === 12 ? year + 1 : year;
  return `${nextYear}-${nextMonth.toString().padStart(2, '0')}`;
}
