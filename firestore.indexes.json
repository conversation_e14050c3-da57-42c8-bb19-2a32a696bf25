{"indexes": [{"collectionGroup": "patients", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "appointments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "date", "order": "ASCENDING"}, {"fieldPath": "time", "order": "ASCENDING"}]}, {"collectionGroup": "appointments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "patientId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "appointments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doctorId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "appointments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "date", "order": "ASCENDING"}]}, {"collectionGroup": "inventory", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "inventory", "queryScope": "COLLECTION", "fields": [{"fieldPath": "category", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "treatments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "category", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}], "fieldOverrides": []}