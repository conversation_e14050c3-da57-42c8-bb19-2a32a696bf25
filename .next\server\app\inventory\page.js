(()=>{var e={};e.id=268,e.ids=[268],e.modules={5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},2361:e=>{"use strict";e.exports=require("events")},7147:e=>{"use strict";e.exports=require("fs")},3685:e=>{"use strict";e.exports=require("http")},5158:e=>{"use strict";e.exports=require("http2")},1808:e=>{"use strict";e.exports=require("net")},2037:e=>{"use strict";e.exports=require("os")},1017:e=>{"use strict";e.exports=require("path")},7282:e=>{"use strict";e.exports=require("process")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},3837:e=>{"use strict";e.exports=require("util")},9796:e=>{"use strict";e.exports=require("zlib")},4056:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>d,routeModule:()=>p,tree:()=>o});var r=s(7096),a=s(6132),i=s(7284),n=s.n(i),l=s(2564),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);s.d(t,c);let o=["",{children:["inventory",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,8975)),"D:\\dentalcare.id\\src\\app\\inventory\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,2594)),"D:\\dentalcare.id\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9291,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\dentalcare.id\\src\\app\\inventory\\page.tsx"],x="/inventory/page",u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/inventory/page",pathname:"/inventory",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},4695:(e,t,s)=>{Promise.resolve().then(s.bind(s,9521))},9521:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>InventoryPage});var r=s(784),a=s(9885),i=s(9212),n=s(3416),l=s(9503),c=s(2233),o=s(6081),d=s(990),x=s(6198),u=s(797);function InventoryPage(){let[e,t]=(0,a.useState)(""),[s,p]=(0,a.useState)("all"),[m,h]=(0,a.useState)("all"),[g]=(0,a.useState)(n.sP),y=["all",...Array.from(new Set(g.map(e=>e.category)))],j=g.filter(t=>{let r=t.name.toLowerCase().includes(e.toLowerCase()),a="all"===s||t.category===s,i="all"===m||t.status===m;return r&&a&&i}),formatCurrency=e=>new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0}).format(e),formatDate=e=>new Date(e).toLocaleDateString("id-ID"),getStatusColor=e=>{switch(e){case"in-stock":default:return"status-in-stock";case"low-stock":return"status-low-stock";case"out-of-stock":return"status-out-of-stock";case"expired":return"status-cancelled"}},getStatusText=e=>{switch(e){case"in-stock":return"Tersedia";case"low-stock":return"Stok Menipis";case"out-of-stock":return"Habis";case"expired":return"Kadaluarsa";default:return e}},getStockLevel=e=>{let t=e.currentStock/(2*e.minStock)*100;return Math.min(t,100)};return(0,r.jsxs)("div",{className:"flex-1 overflow-auto",children:[r.jsx(i.Z,{title:"Manajemen Inventory",subtitle:"Kelola stok alat dan bahan kedokteran gigi"}),(0,r.jsxs)("main",{className:"p-6",children:[g.filter(e=>"low-stock"===e.status||"out-of-stock"===e.status).length>0&&r.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx(l.Z,{className:"w-5 h-5 text-yellow-600 mr-2"}),(0,r.jsxs)("span",{className:"text-yellow-800 font-medium",children:["Perhatian: ",g.filter(e=>"low-stock"===e.status||"out-of-stock"===e.status).length," item memerlukan restocking"]})]})}),(0,r.jsxs)("div",{className:"card",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[r.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Inventory Items"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("button",{className:"btn-secondary flex items-center",children:[r.jsx(c.Z,{className:"w-4 h-4 mr-2"}),"Stock In"]}),(0,r.jsxs)("button",{className:"btn-secondary flex items-center",children:[r.jsx(o.Z,{className:"w-4 h-4 mr-2"}),"Stock Out"]}),(0,r.jsxs)("button",{className:"btn-primary flex items-center",children:[r.jsx(d.Z,{className:"w-4 h-4 mr-2"}),"Tambah Item"]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"relative",children:[r.jsx(x.Z,{className:"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),r.jsx("input",{type:"text",placeholder:"Cari item...",className:"input-field pl-10",value:e,onChange:e=>t(e.target.value)})]}),(0,r.jsxs)("select",{className:"input-field",value:s,onChange:e=>p(e.target.value),children:[r.jsx("option",{value:"all",children:"Semua Kategori"}),y.filter(e=>"all"!==e).map(e=>r.jsx("option",{value:e,children:e},e))]}),(0,r.jsxs)("select",{className:"input-field",value:m,onChange:e=>h(e.target.value),children:[r.jsx("option",{value:"all",children:"Semua Status"}),r.jsx("option",{value:"in-stock",children:"Tersedia"}),r.jsx("option",{value:"low-stock",children:"Stok Menipis"}),r.jsx("option",{value:"out-of-stock",children:"Habis"}),r.jsx("option",{value:"expired",children:"Kadaluarsa"})]})]}),r.jsx("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[r.jsx("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Item"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Stok"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Harga"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Supplier"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Kadaluarsa"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Aksi"})]})}),r.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:j.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{children:[r.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.name}),r.jsx("div",{className:"text-sm text-gray-500",children:e.category})]})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsx("div",{className:"flex items-center",children:(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,r.jsxs)("span",{className:"font-medium",children:[e.currentStock," ",e.unit]}),(0,r.jsxs)("span",{className:"text-gray-500",children:["Min: ",e.minStock]})]}),r.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:r.jsx("div",{className:`h-2 rounded-full ${e.currentStock<=e.minStock?"bg-red-500":e.currentStock<=1.5*e.minStock?"bg-yellow-500":"bg-green-500"}`,style:{width:`${getStockLevel(e)}%`}})})]})})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:formatCurrency(e.price)}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.supplier}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.expiryDate?formatDate(e.expiryDate):"-"}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsx("span",{className:`status-badge ${getStatusColor(e.status)}`,children:getStatusText(e.status)})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[r.jsx("button",{className:"text-primary-600 hover:text-primary-900 mr-3",children:r.jsx(u.Z,{className:"w-4 h-4"})}),r.jsx("button",{className:"text-green-600 hover:text-green-900",children:r.jsx(c.Z,{className:"w-4 h-4"})})]})]},e.id))})]})}),0===j.length&&r.jsx("div",{className:"text-center py-8 text-gray-500",children:"Tidak ada item inventory yang ditemukan"})]})]})]})}},8975:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>i,default:()=>c});var r=s(5153);let a=(0,r.createProxy)(String.raw`D:\dentalcare.id\src\app\inventory\page.tsx`),{__esModule:i,$$typeof:n}=a,l=a.default,c=l}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[172,684,898,29,212,416],()=>__webpack_exec__(4056));module.exports=s})();