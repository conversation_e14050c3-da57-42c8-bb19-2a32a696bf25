(()=>{var e={};e.id=774,e.ids=[774],e.modules={5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},2361:e=>{"use strict";e.exports=require("events")},7147:e=>{"use strict";e.exports=require("fs")},3685:e=>{"use strict";e.exports=require("http")},5158:e=>{"use strict";e.exports=require("http2")},1808:e=>{"use strict";e.exports=require("net")},2037:e=>{"use strict";e.exports=require("os")},1017:e=>{"use strict";e.exports=require("path")},7282:e=>{"use strict";e.exports=require("process")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},3837:e=>{"use strict";e.exports=require("util")},9796:e=>{"use strict";e.exports=require("zlib")},5013:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>u,tree:()=>o});var r=s(7096),a=s(6132),l=s(7284),n=s.n(l),i=s(2564),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);s.d(t,c);let o=["",{children:["treatments",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,7210)),"D:\\dentalcare.id\\src\\app\\treatments\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,2594)),"D:\\dentalcare.id\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9291,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\dentalcare.id\\src\\app\\treatments\\page.tsx"],x="/treatments/page",m={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/treatments/page",pathname:"/treatments",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},280:(e,t,s)=>{Promise.resolve().then(s.bind(s,6293))},6293:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>TreatmentsPage});var r=s(784),a=s(9885),l=s(9212),n=s(3416),i=s(990),c=s(6198),o=s(797),d=s(4635);function TreatmentsPage(){let[e,t]=(0,a.useState)(""),[s,x]=(0,a.useState)("all"),[m]=(0,a.useState)(n.pM),u=["all",...Array.from(new Set(m.map(e=>e.category)))],p=m.filter(t=>{let r=t.name.toLowerCase().includes(e.toLowerCase())||t.code.toLowerCase().includes(e.toLowerCase()),a="all"===s||t.category===s;return r&&a}),formatCurrency=e=>new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0}).format(e),getCategoryColor=e=>({Konsultasi:"bg-blue-100 text-blue-800",Preventif:"bg-green-100 text-green-800",Restoratif:"bg-yellow-100 text-yellow-800",Prostetik:"bg-purple-100 text-purple-800",Bedah:"bg-red-100 text-red-800"})[e]||"bg-gray-100 text-gray-800";return(0,r.jsxs)("div",{className:"flex-1 overflow-auto",children:[r.jsx(l.Z,{title:"Manajemen Treatment",subtitle:"Kelola katalog treatment dan tarif"}),r.jsx("main",{className:"p-6",children:(0,r.jsxs)("div",{className:"card",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[r.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Katalog Treatment"}),(0,r.jsxs)("button",{className:"btn-primary flex items-center",children:[r.jsx(i.Z,{className:"w-4 h-4 mr-2"}),"Tambah Treatment"]})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[r.jsx(c.Z,{className:"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),r.jsx("input",{type:"text",placeholder:"Cari treatment berdasarkan nama atau kode...",className:"input-field pl-10",value:e,onChange:e=>t(e.target.value)})]}),(0,r.jsxs)("select",{className:"input-field w-full sm:w-48",value:s,onChange:e=>x(e.target.value),children:[r.jsx("option",{value:"all",children:"Semua Kategori"}),u.filter(e=>"all"!==e).map(e=>r.jsx("option",{value:e,children:e},e))]})]}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:p.map(e=>(0,r.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("div",{className:"flex items-center space-x-2 mb-2",children:r.jsx("span",{className:`status-badge ${getCategoryColor(e.category)}`,children:e.category})}),r.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:e.name}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 mb-2",children:["Kode: ",e.code]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx("button",{className:"text-gray-400 hover:text-gray-600",children:r.jsx(o.Z,{className:"w-4 h-4"})}),r.jsx("button",{className:"text-gray-400 hover:text-red-600",children:r.jsx(d.Z,{className:"w-4 h-4"})})]})]}),r.jsx("p",{className:"text-gray-600 text-sm mb-4",children:e.description}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx("span",{className:"text-sm text-gray-600",children:"Tarif:"}),r.jsx("span",{className:"text-lg font-bold text-primary-600",children:formatCurrency(e.price)})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx("span",{className:"text-sm text-gray-600",children:"Durasi:"}),(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:[e.duration," menit"]})]})]}),r.jsx("div",{className:"mt-4 pt-4 border-t border-gray-200",children:r.jsx("button",{className:"w-full btn-secondary text-sm",children:"Gunakan untuk Appointment"})})]},e.id))}),0===p.length&&(0,r.jsxs)("div",{className:"text-center py-12",children:[r.jsx("div",{className:"text-gray-400 mb-4",children:r.jsx("svg",{className:"w-12 h-12 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),r.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Tidak ada treatment ditemukan"}),r.jsx("p",{className:"text-gray-600 mb-4",children:"Coba ubah filter pencarian atau tambah treatment baru"}),r.jsx("button",{className:"btn-primary",children:"Tambah Treatment Baru"})]}),p.length>0&&r.jsx("div",{className:"mt-8 pt-6 border-t border-gray-200",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("p",{className:"text-2xl font-bold text-gray-900",children:p.length}),r.jsx("p",{className:"text-sm text-gray-600",children:"Total Treatment"})]}),(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("p",{className:"text-2xl font-bold text-gray-900",children:u.length-1}),r.jsx("p",{className:"text-sm text-gray-600",children:"Kategori"})]}),(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("p",{className:"text-2xl font-bold text-gray-900",children:formatCurrency(Math.min(...p.map(e=>e.price)))}),r.jsx("p",{className:"text-sm text-gray-600",children:"Tarif Terendah"})]}),(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("p",{className:"text-2xl font-bold text-gray-900",children:formatCurrency(Math.max(...p.map(e=>e.price)))}),r.jsx("p",{className:"text-sm text-gray-600",children:"Tarif Tertinggi"})]})]})})]})})]})}},7210:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>l,default:()=>c});var r=s(5153);let a=(0,r.createProxy)(String.raw`D:\dentalcare.id\src\app\treatments\page.tsx`),{__esModule:l,$$typeof:n}=a,i=a.default,c=i}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[172,684,811,29,212,416],()=>__webpack_exec__(5013));module.exports=s})();