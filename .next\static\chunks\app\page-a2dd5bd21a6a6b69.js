(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{9615:function(e,t,a){Promise.resolve().then(a.bind(a,7064))},7064:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return Dashboard}});var n=a(7437),r=a(2621);let s={blue:"bg-blue-500",green:"bg-green-500",yellow:"bg-yellow-500",red:"bg-red-500",purple:"bg-purple-500"};function StatsCard(e){let{title:t,value:a,icon:r,trend:i,color:c}=e;return(0,n.jsx)("div",{className:"card",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"flex-shrink-0",children:(0,n.jsx)("div",{className:"w-12 h-12 ".concat(s[c]," rounded-lg flex items-center justify-center text-white"),children:r})}),(0,n.jsxs)("div",{className:"ml-4 flex-1",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-gray-600",children:t}),(0,n.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:a}),i&&(0,n.jsxs)("p",{className:"text-sm ".concat(i.isPositive?"text-green-600":"text-red-600"),children:[i.isPositive?"+":"-",Math.abs(i.value),"%",(0,n.jsx)("span",{className:"text-gray-500 ml-1",children:"dari bulan lalu"})]})]})]})})}var i=a(5658),c=a(1809),l=a(2929);function TodayAppointments(){let e=i.Q7.filter(e=>"2024-01-20"===e.date),getStatusColor=e=>{switch(e){case"scheduled":default:return"status-scheduled";case"confirmed":return"status-confirmed";case"in-progress":return"status-in-progress";case"completed":return"status-completed";case"cancelled":return"status-cancelled"}},getStatusText=e=>{switch(e){case"scheduled":return"Terjadwal";case"confirmed":return"Dikonfirmasi";case"in-progress":return"Berlangsung";case"completed":return"Selesai";case"cancelled":return"Dibatalkan";default:return e}};return(0,n.jsxs)("div",{className:"card",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Jadwal Hari Ini"}),(0,n.jsxs)("span",{className:"text-sm text-gray-500",children:[e.length," appointment"]})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[e.map(e=>(0,n.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)("div",{className:"flex-shrink-0",children:(0,n.jsx)("div",{className:"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center",children:(0,n.jsx)(c.Z,{className:"w-5 h-5 text-primary-600"})})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"font-medium text-gray-900",children:e.patientName}),(0,n.jsx)("p",{className:"text-sm text-gray-600",children:e.type}),(0,n.jsxs)("p",{className:"text-sm text-gray-500",children:["Dr. ",e.doctorName]})]})]}),(0,n.jsxs)("div",{className:"text-right",children:[(0,n.jsxs)("div",{className:"flex items-center text-sm text-gray-600 mb-2",children:[(0,n.jsx)(l.Z,{className:"w-4 h-4 mr-1"}),e.time," (",e.duration," menit)"]}),(0,n.jsx)("span",{className:"status-badge ".concat(getStatusColor(e.status)),children:getStatusText(e.status)})]})]},e.id)),0===e.length&&(0,n.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Tidak ada appointment hari ini"})]})]})}var o=a(2265),d=a(4086),u=a(6831),m=a(5495);function useRealTimeDashboard(){let{tenantId:e}=(0,m.S)(),[t,a]=(0,o.useState)({todayAppointments:0,todayRevenue:0,totalPatients:0,monthlyRevenue:0,pendingPayments:0,lowStockItems:0});return(0,o.useEffect)(()=>{if(!e)return;let t=new Date().toISOString().split("T")[0],n=new Date().toISOString().slice(0,7),r=(0,d.IO)((0,d.hJ)(u.db,"dentalcare",e,"appointments"),(0,d.ar)("date","==",t)),s=(0,d.cf)(r,e=>{a(t=>({...t,todayAppointments:e.size}))}),i=(0,d.IO)((0,d.hJ)(u.db,"dentalcare",e,"patients")),c=(0,d.cf)(i,e=>{a(t=>({...t,totalPatients:e.size}))}),l=(0,d.IO)((0,d.hJ)(u.db,"dentalcare",e,"invoices"),(0,d.ar)("status","in",["draft","sent","overdue"])),o=(0,d.cf)(l,e=>{a(t=>({...t,pendingPayments:e.size}))}),fetchMonthlyRevenue=async()=>{try{let r=(0,d.IO)((0,d.hJ)(u.db,"dentalcare",e,"invoices"),(0,d.ar)("status","==","paid"),(0,d.ar)("date",">=",n+"-01"),(0,d.ar)("date","<",getNextMonth(n)+"-01")),s=await (0,d.PL)(r),i=0,c=0;s.docs.forEach(e=>{let a=e.data();i+=a.total||0,a.date===t&&(c+=a.total||0)}),a(e=>({...e,monthlyRevenue:i,todayRevenue:c}))}catch(e){console.error("Error fetching monthly revenue:",e)}};fetchMonthlyRevenue();let m=(0,d.IO)((0,d.hJ)(u.db,"dentalcare",e,"inventory")),h=(0,d.cf)(m,e=>{let t=0;e.docs.forEach(e=>{let a=e.data();a.currentStock<=a.minStock&&t++}),a(e=>({...e,lowStockItems:t}))});return()=>{s(),c(),o(),h()}},[e]),t}function getNextMonth(e){let[t,a]=e.split("-").map(Number);return"".concat(12===a?t+1:t,"-").concat((12===a?1:a+1).toString().padStart(2,"0"))}var h=a(6561),p=a(4103),g=a(1095),v=a(9649),x=a(654),f=a(887);function Dashboard(){let{data:e=[]}=(0,h.w5)(),t=useRealTimeDashboard(),formatCurrency=e=>new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0}).format(e);return(0,n.jsxs)("div",{className:"flex-1 overflow-auto",children:[(0,n.jsx)(r.Z,{title:"Dashboard",subtitle:"Selamat datang di sistem manajemen klinik gigi"}),(0,n.jsxs)("main",{className:"p-6",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8",children:[(0,n.jsx)(StatsCard,{title:"Appointment Hari Ini",value:t.todayAppointments,icon:(0,n.jsx)(p.Z,{className:"w-6 h-6"}),color:"blue",trend:{value:12,isPositive:!0}}),(0,n.jsx)(StatsCard,{title:"Pendapatan Hari Ini",value:formatCurrency(t.todayRevenue),icon:(0,n.jsx)(g.Z,{className:"w-6 h-6"}),color:"green",trend:{value:8,isPositive:!0}}),(0,n.jsx)(StatsCard,{title:"Total Pasien",value:e.length,icon:(0,n.jsx)(v.Z,{className:"w-6 h-6"}),color:"purple",trend:{value:15,isPositive:!0}}),(0,n.jsx)(StatsCard,{title:"Pendapatan Bulan Ini",value:formatCurrency(t.monthlyRevenue),icon:(0,n.jsx)(x.Z,{className:"w-6 h-6"}),color:"blue",trend:{value:5,isPositive:!0}}),(0,n.jsx)(StatsCard,{title:"Pembayaran Tertunda",value:t.pendingPayments,icon:(0,n.jsx)(l.Z,{className:"w-6 h-6"}),color:"yellow"}),(0,n.jsx)(StatsCard,{title:"Stok Menipis",value:t.lowStockItems,icon:(0,n.jsx)(f.Z,{className:"w-6 h-6"}),color:"red"})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,n.jsx)("div",{className:"lg:col-span-2",children:(0,n.jsx)(TodayAppointments,{})}),(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"card",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Aksi Cepat"}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsx)("button",{className:"w-full btn-primary text-left",children:"+ Tambah Pasien Baru"}),(0,n.jsx)("button",{className:"w-full btn-secondary text-left",children:"+ Buat Appointment"}),(0,n.jsx)("button",{className:"w-full btn-secondary text-left",children:"\uD83D\uDCCB Lihat Jadwal Hari Ini"}),(0,n.jsx)("button",{className:"w-full btn-secondary text-left",children:"\uD83D\uDCB0 Buat Invoice"})]})]}),(0,n.jsxs)("div",{className:"card",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Aktivitas Terbaru"}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3 text-sm",children:[(0,n.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,n.jsx)("span",{className:"text-gray-600",children:"Pembayaran dari Budi Santoso diterima"})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-3 text-sm",children:[(0,n.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,n.jsx)("span",{className:"text-gray-600",children:"Appointment baru dari Sari Dewi"})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-3 text-sm",children:[(0,n.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),(0,n.jsx)("span",{className:"text-gray-600",children:"Stok Anestesi Lidocaine menipis"})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-3 text-sm",children:[(0,n.jsx)("div",{className:"w-2 h-2 bg-purple-500 rounded-full"}),(0,n.jsx)("span",{className:"text-gray-600",children:"Treatment selesai untuk Andi Wijaya"})]})]})]})]})]})]})]})}},5658:function(e,t,a){"use strict";a.d(t,{Q7:function(){return n},pM:function(){return r},sP:function(){return s}});let n=[{id:"1",patientId:"1",patientName:"Budi Santoso",doctorId:"doc1",doctorName:"Dr. Sarah Putri",date:"2024-01-20",time:"09:00",duration:60,type:"Konsultasi",status:"scheduled",notes:"Keluhan sakit gigi geraham kiri",treatmentPlan:["Konsultasi","Scaling"]},{id:"2",patientId:"2",patientName:"Sari Dewi",doctorId:"doc1",doctorName:"Dr. Sarah Putri",date:"2024-01-20",time:"10:30",duration:90,type:"Scaling",status:"confirmed",treatmentPlan:["Scaling","Fluoride Treatment"]},{id:"3",patientId:"3",patientName:"Andi Wijaya",doctorId:"doc2",doctorName:"Dr. Ahmad Rahman",date:"2024-01-20",time:"14:00",duration:120,type:"Crown",status:"in-progress",treatmentPlan:["Crown Preparation","Temporary Crown"]}],r=[{id:"1",code:"CONS001",name:"Konsultasi",category:"Konsultasi",price:15e4,duration:30,description:"Konsultasi dan pemeriksaan gigi"},{id:"2",code:"SCAL001",name:"Scaling",category:"Preventif",price:3e5,duration:60,description:"Pembersihan karang gigi"},{id:"3",code:"FILL001",name:"Tambal Gigi",category:"Restoratif",price:25e4,duration:45,description:"Penambalan gigi dengan komposit"},{id:"4",code:"CROW001",name:"Crown",category:"Prostetik",price:25e5,duration:120,description:"Pemasangan mahkota gigi"}],s=[{id:"1",name:"Komposit Resin",category:"Bahan Tambal",currentStock:15,minStock:10,unit:"tube",price:45e4,supplier:"PT Dental Supply",expiryDate:"2025-06-15",lastRestocked:"2024-01-01",status:"in-stock"},{id:"2",name:"Anestesi Lidocaine",category:"Obat",currentStock:5,minStock:20,unit:"vial",price:25e3,supplier:"PT Pharma Dental",expiryDate:"2024-12-31",lastRestocked:"2023-12-15",status:"low-stock"},{id:"3",name:"Sarung Tangan Latex",category:"Disposable",currentStock:0,minStock:50,unit:"box",price:85e3,supplier:"PT Medical Supply",lastRestocked:"2023-11-20",status:"out-of-stock"}]},6561:function(e,t,a){"use strict";a.d(t,{KA:function(){return useCreatePatient},w5:function(){return usePatients}});var n=a(9891),r=a(8038),s=a(3588);a(2265);var i=a(4086),c=a(6831);let TenantService=class TenantService{getCollection(e){return(0,i.hJ)(c.db,"dentalcare",this.tenantId,e)}getDocument(e,t){return(0,i.JU)(c.db,"dentalcare",this.tenantId,e,t)}getSettingsDocument(e){return(0,i.JU)(c.db,"dentalcare",this.tenantId,"settings",e)}validateTenantAccess(){if(!this.tenantId)throw Error("No tenant ID provided")}handleError(e,t){if(console.error("TenantService Error [".concat(this.tenantId,"] - ").concat(t,":"),e),e instanceof i.WA)switch(e.code){case"permission-denied":throw Error("Access denied for tenant ".concat(this.tenantId,". Please check your permissions."));case"not-found":throw Error("Resource not found in tenant ".concat(this.tenantId,"."));case"unavailable":throw Error("Service temporarily unavailable. Please try again.");default:throw Error("Database operation failed: ".concat(e.message))}throw Error("Failed to ".concat(t,": ").concat(e.message||"Unknown error"))}getTenantId(){return this.tenantId}setTenantId(e){if(!e)throw Error("Invalid tenant ID");this.tenantId=e}constructor(e){if(!e)throw Error("TenantService requires a valid tenantId");this.tenantId=e}};let TenantService_TenantServiceRegistry=class TenantService_TenantServiceRegistry{static getService(e,t,a){this.instances.has(t)||this.instances.set(t,new Map);let n=this.instances.get(t);return n.has(a)||n.set(a,new e(t)),n.get(a)}static clearTenantServices(e){this.instances.delete(e)}static clearAllServices(){this.instances.clear()}};TenantService_TenantServiceRegistry.instances=new Map;let patients_PatientService=class patients_PatientService extends TenantService{async generateMedicalRecordNumber(){let e=new Date().getFullYear(),t=(0,i.IO)(this.getCollection("patients"),(0,i.ar)("medicalRecordNumber",">=","RM".concat(e)),(0,i.ar)("medicalRecordNumber","<","RM".concat(e+1)),(0,i.Xo)("medicalRecordNumber","desc"),(0,i.b9)(1)),a=await (0,i.PL)(t);if(a.empty)return"RM".concat(e,"001");let n=a.docs[0].data(),r=parseInt(n.medicalRecordNumber.slice(-3)),s=(r+1).toString().padStart(3,"0");return"RM".concat(e).concat(s)}async createPatient(e){try{this.validateTenantAccess();let t=await this.generateMedicalRecordNumber(),a={...e,medicalRecordNumber:t,totalVisits:0,status:"active",dentalChart:this.initializeDentalChart(),clinicalImages:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},n=await (0,i.ET)(this.getCollection("patients"),a);return n.id}catch(e){this.handleError(e,"create patient")}}initializeDentalChart(){let e=[];for(let t=1;t<=32;t++)e.push({toothNumber:t,condition:"healthy",notes:"",updatedAt:new Date().toISOString()});return e}async getPatient(e){try{this.validateTenantAccess();let t=this.getDocument("patients",e),a=await (0,i.QT)(t);if(a.exists())return{id:a.id,...a.data()};return null}catch(e){this.handleError(e,"get patient")}}async getPatients(){try{this.validateTenantAccess();let e=(0,i.IO)(this.getCollection("patients"),(0,i.Xo)("name")),t=await (0,i.PL)(e);return t.docs.map(e=>({id:e.id,...e.data()}))}catch(e){this.handleError(e,"get patients")}}async searchPatients(e){try{this.validateTenantAccess();let t=await this.getPatients(),a=e.toLowerCase();return t.filter(t=>t.name.toLowerCase().includes(a)||t.medicalRecordNumber.toLowerCase().includes(a)||t.phone.includes(e)||t.email.toLowerCase().includes(a))}catch(e){this.handleError(e,"search patients")}}async updatePatient(e,t){try{this.validateTenantAccess();let a=this.getDocument("patients",e);await (0,i.r7)(a,{...t,updatedAt:new Date().toISOString()})}catch(e){this.handleError(e,"update patient")}}async deletePatient(e){try{this.validateTenantAccess();let t=this.getDocument("patients",e);await (0,i.oe)(t)}catch(e){this.handleError(e,"delete patient")}}async addClinicalImage(e,t){try{this.validateTenantAccess();let a=await this.getPatient(e);if(!a)throw Error("Patient not found");let n={id:Date.now().toString(),...t,date:new Date().toISOString()},r=[...a.clinicalImages||[],n];await this.updatePatient(e,{clinicalImages:r})}catch(e){this.handleError(e,"add clinical image")}}async updateDentalChart(e,t,a,n){try{var r;this.validateTenantAccess();let s=await this.getPatient(e);if(!s)throw Error("Patient not found");let i=(null===(r=s.dentalChart)||void 0===r?void 0:r.map(e=>e.toothNumber===t?{...e,condition:a,notes:n||e.notes,updatedAt:new Date().toISOString()}:e))||[];await this.updatePatient(e,{dentalChart:i})}catch(e){this.handleError(e,"update dental chart")}}subscribeToPatients(e){this.validateTenantAccess();let t=(0,i.IO)(this.getCollection("patients"),(0,i.Xo)("name"));return(0,i.cf)(t,t=>{let a=t.docs.map(e=>({id:e.id,...e.data()}));e(a)},e=>{console.error("Error in patients subscription:",e)})}};var l=a(5495);function usePatients(){let{tenantId:e}=(0,l.S)();return(0,n.a)({queryKey:["patients",e],queryFn:()=>{if(!e)throw Error("No tenant selected");let t=TenantService_TenantServiceRegistry.getService(patients_PatientService,e,"patients");return t.getPatients()},enabled:!!e,staleTime:3e5})}function useCreatePatient(){let e=(0,r.NL)(),{tenantId:t}=(0,l.S)();return(0,s.D)({mutationFn:e=>{if(!t)throw Error("No tenant selected");let a=TenantService_TenantServiceRegistry.getService(patients_PatientService,t,"patients");return a.createPatient(e)},onSuccess:()=>{e.invalidateQueries({queryKey:["patients",t]})},onError:e=>{console.error("Error creating patient:",e)}})}},4103:function(e,t,a){"use strict";var n=a(2265);function CalendarDaysIcon({title:e,titleId:t,...a},r){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},a),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"}))}let r=n.forwardRef(CalendarDaysIcon);t.Z=r},654:function(e,t,a){"use strict";var n=a(2265);function ChartBarIcon({title:e,titleId:t,...a},r){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},a),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))}let r=n.forwardRef(ChartBarIcon);t.Z=r},2929:function(e,t,a){"use strict";var n=a(2265);function ClockIcon({title:e,titleId:t,...a},r){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},a),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}let r=n.forwardRef(ClockIcon);t.Z=r},1095:function(e,t,a){"use strict";var n=a(2265);function CurrencyDollarIcon({title:e,titleId:t,...a},r){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},a),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}let r=n.forwardRef(CurrencyDollarIcon);t.Z=r},9649:function(e,t,a){"use strict";var n=a(2265);function UserGroupIcon({title:e,titleId:t,...a},r){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},a),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"}))}let r=n.forwardRef(UserGroupIcon);t.Z=r}},function(e){e.O(0,[609,15,801,273,995,621,971,472,744],function(){return e(e.s=9615)}),_N_E=e.O()}]);