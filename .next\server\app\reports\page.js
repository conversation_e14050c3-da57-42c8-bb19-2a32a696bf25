(()=>{var e={};e.id=882,e.ids=[882],e.modules={5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},2361:e=>{"use strict";e.exports=require("events")},7147:e=>{"use strict";e.exports=require("fs")},3685:e=>{"use strict";e.exports=require("http")},5158:e=>{"use strict";e.exports=require("http2")},1808:e=>{"use strict";e.exports=require("net")},2037:e=>{"use strict";e.exports=require("os")},1017:e=>{"use strict";e.exports=require("path")},7282:e=>{"use strict";e.exports=require("process")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},3837:e=>{"use strict";e.exports=require("util")},9796:e=>{"use strict";e.exports=require("zlib")},6361:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>o,originalPathname:()=>m,pages:()=>x,routeModule:()=>p,tree:()=>d});var r=s(7096),a=s(6132),n=s(7284),i=s.n(n),l=s(2564),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);s.d(t,c);let d=["",{children:["reports",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,9573)),"D:\\dentalcare.id\\src\\app\\reports\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,2594)),"D:\\dentalcare.id\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9291,23)),"next/dist/client/components/not-found-error"]}],x=["D:\\dentalcare.id\\src\\app\\reports\\page.tsx"],m="/reports/page",o={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/reports/page",pathname:"/reports",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5378:(e,t,s)=>{Promise.resolve().then(s.bind(s,979))},979:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>ReportsPage});var r=s(784),a=s(9885),n=s(9212),i=s(5636),l=s(1879),c=s(7863),d=s(9448),x=s(1565),m=s(7881),o=s(6668);function ReportsPage(){let[e,t]=(0,a.useState)("month"),[s,p]=(0,a.useState)("financial"),formatCurrency=e=>new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0}).format(e),u=[{id:"financial",name:"Laporan Keuangan",icon:i.Z},{id:"appointments",name:"Laporan Appointment",icon:l.Z},{id:"patients",name:"Laporan Pasien",icon:c.Z},{id:"treatments",name:"Laporan Treatment",icon:d.Z}];return(0,r.jsxs)("div",{className:"flex-1 overflow-auto",children:[r.jsx(n.Z,{title:"Laporan & Analytics",subtitle:"Analisis performa klinik dan laporan keuangan"}),(0,r.jsxs)("main",{className:"p-6",children:[r.jsx("div",{className:"card mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[r.jsx("div",{className:"flex flex-wrap gap-2",children:u.map(e=>(0,r.jsxs)("button",{onClick:()=>p(e.id),className:`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${s===e.id?"bg-primary-100 text-primary-700":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:[r.jsx(e.icon,{className:"w-4 h-4 mr-2"}),e.name]},e.id))}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("select",{className:"input-field w-40",value:e,onChange:e=>t(e.target.value),children:[r.jsx("option",{value:"week",children:"Minggu Ini"}),r.jsx("option",{value:"month",children:"Bulan Ini"}),r.jsx("option",{value:"quarter",children:"Kuartal Ini"}),r.jsx("option",{value:"year",children:"Tahun Ini"})]}),(0,r.jsxs)("button",{className:"btn-secondary flex items-center",children:[r.jsx(x.Z,{className:"w-4 h-4 mr-2"}),"Print"]}),(0,r.jsxs)("button",{className:"btn-primary flex items-center",children:[r.jsx(m.Z,{className:"w-4 h-4 mr-2"}),"Export"]})]})]})}),"financial"===s&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[r.jsx("div",{className:"card",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center text-white mr-4",children:r.jsx(i.Z,{className:"w-6 h-6"})}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm text-gray-600",children:"Total Pendapatan"}),r.jsx("p",{className:"text-2xl font-bold text-gray-900",children:formatCurrency(48e6)}),r.jsx("p",{className:"text-sm text-green-600",children:"+12% dari bulan lalu"})]})]})}),r.jsx("div",{className:"card",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center text-white mr-4",children:r.jsx(l.Z,{className:"w-6 h-6"})}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm text-gray-600",children:"Total Appointment"}),r.jsx("p",{className:"text-2xl font-bold text-gray-900",children:"165"}),r.jsx("p",{className:"text-sm text-blue-600",children:"+8% dari bulan lalu"})]})]})}),r.jsx("div",{className:"card",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center text-white mr-4",children:r.jsx(o.Z,{className:"w-6 h-6"})}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm text-gray-600",children:"Rata-rata per Pasien"}),r.jsx("p",{className:"text-2xl font-bold text-gray-900",children:formatCurrency(290909)}),r.jsx("p",{className:"text-sm text-yellow-600",children:"+3% dari bulan lalu"})]})]})}),r.jsx("div",{className:"card",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center text-white mr-4",children:r.jsx(c.Z,{className:"w-6 h-6"})}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm text-gray-600",children:"Pasien Baru"}),r.jsx("p",{className:"text-2xl font-bold text-gray-900",children:"23"}),r.jsx("p",{className:"text-sm text-purple-600",children:"+15% dari bulan lalu"})]})]})})]}),(0,r.jsxs)("div",{className:"card",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-6",children:"Tren Pendapatan Bulanan"}),r.jsx("div",{className:"h-80 flex items-end space-x-4",children:[{month:"Jan",revenue:35e6,appointments:120},{month:"Feb",revenue:42e6,appointments:145},{month:"Mar",revenue:38e6,appointments:132},{month:"Apr",revenue:45e6,appointments:158},{month:"May",revenue:52e6,appointments:172},{month:"Jun",revenue:48e6,appointments:165}].map((e,t)=>(0,r.jsxs)("div",{className:"flex-1 flex flex-col items-center",children:[r.jsx("div",{className:"w-full bg-primary-500 rounded-t-lg transition-all duration-300 hover:bg-primary-600",style:{height:`${e.revenue/6e7*100}%`}}),(0,r.jsxs)("div",{className:"mt-2 text-center",children:[r.jsx("p",{className:"text-sm font-medium text-gray-900",children:e.month}),r.jsx("p",{className:"text-xs text-gray-600",children:formatCurrency(e.revenue)})]})]},t))})]}),(0,r.jsxs)("div",{className:"card",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-6",children:"Performa Treatment"}),r.jsx("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full",children:[r.jsx("thead",{children:(0,r.jsxs)("tr",{className:"border-b border-gray-200",children:[r.jsx("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Treatment"}),r.jsx("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Jumlah"}),r.jsx("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Pendapatan"}),r.jsx("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Rata-rata"})]})}),r.jsx("tbody",{children:[{treatment:"Scaling",count:85,revenue:255e5},{treatment:"Tambal Gigi",count:62,revenue:155e5},{treatment:"Konsultasi",count:120,revenue:18e6},{treatment:"Crown",count:12,revenue:3e7},{treatment:"Cabut Gigi",count:28,revenue:84e5}].map((e,t)=>(0,r.jsxs)("tr",{className:"border-b border-gray-100",children:[r.jsx("td",{className:"py-3 px-4 font-medium text-gray-900",children:e.treatment}),r.jsx("td",{className:"py-3 px-4 text-gray-600",children:e.count}),r.jsx("td",{className:"py-3 px-4 text-gray-600",children:formatCurrency(e.revenue)}),r.jsx("td",{className:"py-3 px-4 text-gray-600",children:formatCurrency(e.revenue/e.count)})]},t))})]})})]})]}),"financial"!==s&&r.jsx("div",{className:"card",children:(0,r.jsxs)("div",{className:"text-center py-12",children:[r.jsx("div",{className:"text-gray-400 mb-4",children:r.jsx(d.Z,{className:"w-16 h-16 mx-auto"})}),r.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:u.find(e=>e.id===s)?.name}),r.jsx("p",{className:"text-gray-600 mb-4",children:"Laporan ini sedang dalam pengembangan"}),r.jsx("button",{className:"btn-primary",children:"Kembali ke Laporan Keuangan"})]})})]})]})}},9573:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>n,default:()=>c});var r=s(5153);let a=(0,r.createProxy)(String.raw`D:\dentalcare.id\src\app\reports\page.tsx`),{__esModule:n,$$typeof:i}=a,l=a.default,c=l}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[172,684,513,29,212],()=>__webpack_exec__(6361));module.exports=s})();