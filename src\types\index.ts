export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'doctor' | 'receptionist' | 'assistant' | 'manager';
  clinicId: string;
  avatar?: string;
}

export interface Patient {
  id: string;
  medicalRecordNumber: string;
  name: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  address: string;
  nik: string;
  gender: 'male' | 'female';
  avatar?: string; // base64 encoded image
  emergencyContact: {
    name: string;
    phone: string;
    relationship: string;
  };
  medicalHistory: {
    allergies: string[];
    medications: string[];
    conditions: string[];
  };
  clinicalImages?: {
    id: string;
    type: 'intraoral' | 'extraoral' | 'xray';
    image: string; // base64 encoded
    description?: string;
    date: string;
  }[];
  dentalChart?: {
    toothNumber: number;
    condition: 'healthy' | 'caries' | 'filled' | 'crown' | 'missing' | 'root_canal';
    notes?: string;
    updatedAt: string;
  }[];
  lastVisit?: string;
  totalVisits: number;
  status: 'active' | 'inactive';
  clinicId: string;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
}

export interface Appointment {
  id: string;
  patientId: string;
  patientName: string;
  doctorId: string;
  doctorName: string;
  date: string;
  time: string;
  duration: number;
  type: string;
  status: 'scheduled' | 'confirmed' | 'in-progress' | 'completed' | 'cancelled' | 'no-show';
  notes?: string;
  treatmentPlan?: string[];
  clinicId: string;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
}

export interface Treatment {
  id: string;
  code: string;
  name: string;
  category: string;
  price: number;
  duration: number;
  description: string;
}

export interface Invoice {
  id: string;
  patientId: string;
  patientName: string;
  date: string;
  items: {
    treatmentId: string;
    treatmentName: string;
    quantity: number;
    price: number;
    total: number;
  }[];
  subtotal: number;
  tax: number;
  discount: number;
  total: number;
  status: 'draft' | 'sent' | 'paid' | 'overdue';
  paymentMethod?: string;
  paidDate?: string;
}

export interface InventoryItem {
  id: string;
  name: string;
  category: string;
  currentStock: number;
  minStock: number;
  unit: string;
  price: number;
  supplier: string;
  expiryDate?: string;
  lastRestocked: string;
  status: 'in-stock' | 'low-stock' | 'out-of-stock' | 'expired';
}

export interface DashboardStats {
  todayAppointments: number;
  todayRevenue: number;
  totalPatients: number;
  monthlyRevenue: number;
  pendingPayments: number;
  lowStockItems: number;
}
