(()=>{var e={};e.id=333,e.ids=[333],e.modules={5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},2361:e=>{"use strict";e.exports=require("events")},7147:e=>{"use strict";e.exports=require("fs")},3685:e=>{"use strict";e.exports=require("http")},5158:e=>{"use strict";e.exports=require("http2")},1808:e=>{"use strict";e.exports=require("net")},2037:e=>{"use strict";e.exports=require("os")},1017:e=>{"use strict";e.exports=require("path")},7282:e=>{"use strict";e.exports=require("process")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},3837:e=>{"use strict";e.exports=require("util")},9796:e=>{"use strict";e.exports=require("zlib")},7101:(e,a,s)=>{"use strict";s.r(a),s.d(a,{GlobalError:()=>r.a,__next_app__:()=>o,originalPathname:()=>x,pages:()=>m,routeModule:()=>h,tree:()=>d});var t=s(7096),l=s(6132),i=s(7284),r=s.n(i),n=s(2564),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(a,c);let d=["",{children:["patients",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,3906)),"D:\\dentalcare.id\\src\\app\\patients\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,2594)),"D:\\dentalcare.id\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9291,23)),"next/dist/client/components/not-found-error"]}],m=["D:\\dentalcare.id\\src\\app\\patients\\page.tsx"],x="/patients/page",o={require:s,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/patients/page",pathname:"/patients",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},2521:(e,a,s)=>{Promise.resolve().then(s.bind(s,5497))},5497:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>PatientsPage});var t=s(784),l=s(9885),i=s(9212),r=s(534),n=s(990),c=s(6198),d=s(8750),m=s(880),x=s(1867),o=s(8955),h=s(797),u=s(7048),p=s(6926);let compressImageToBase64=(e,a=800,s=600,t=.8)=>new Promise((l,i)=>{let r=document.createElement("canvas"),n=r.getContext("2d"),c=new Image;c.onload=()=>{let{width:e,height:i}=c;e>i?e>a&&(i=i*a/e,e=a):i>s&&(e=e*s/i,i=s),r.width=e,r.height=i,n?.drawImage(c,0,0,e,i);let d=r.toDataURL("image/jpeg",t);l(d)},c.onerror=()=>i(Error("Failed to load image")),c.src=URL.createObjectURL(e)}),validateImageFile=e=>["image/jpeg","image/jpg","image/png","image/webp"].includes(e.type)?e.size>5242880?{valid:!1,error:"Ukuran file terlalu besar. Maksimal 5MB."}:{valid:!0}:{valid:!1,error:"Format file tidak didukung. Gunakan JPG, PNG, atau WebP."};function CreatePatientModal({onClose:e,onSubmit:a,isLoading:s}){let[i,r]=(0,l.useState)({name:"",email:"",phone:"",dateOfBirth:"",address:"",nik:"",gender:"male",avatar:"",emergencyContact:{name:"",phone:"",relationship:""},medicalHistory:{allergies:[],medications:[],conditions:[]}}),[n,c]=(0,l.useState)(""),[d,m]=(0,l.useState)(""),[x,o]=(0,l.useState)(""),[h,g]=(0,l.useState)(""),handleInputChange=(e,a)=>{if(e.includes(".")){let[s,t]=e.split(".");r(e=>({...e,[s]:{...e[s],[t]:a}}))}else r(s=>({...s,[e]:a}))},handleImageUpload=async e=>{let a=e.target.files?.[0];if(!a)return;g("");let s=validateImageFile(a);if(!s.valid){g(s.error||"Invalid file");return}try{let e=await compressImageToBase64(a,400,400,.8);r(a=>({...a,avatar:e}))}catch(e){g("Failed to process image")}},addToArray=(e,a)=>{a.trim()&&(r(s=>({...s,medicalHistory:{...s.medicalHistory,[e]:[...s.medicalHistory[e],a.trim()]}})),"allergies"===e&&c(""),"medications"===e&&m(""),"conditions"===e&&o(""))},removeFromArray=(e,a)=>{r(s=>({...s,medicalHistory:{...s.medicalHistory,[e]:s.medicalHistory[e].filter((e,s)=>s!==a)}}))};return t.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:t.jsx("div",{className:"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[t.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Tambah Pasien Baru"}),t.jsx("button",{onClick:e,className:"text-gray-400 hover:text-gray-600",children:t.jsx(u.Z,{className:"w-6 h-6"})})]}),(0,t.jsxs)("form",{onSubmit:e=>{e.preventDefault();let s={...i,totalVisits:0,status:"active",clinicalImages:[],dentalChart:[]};a(s)},className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-6",children:[t.jsx("div",{className:"flex-shrink-0",children:i.avatar?t.jsx("img",{src:i.avatar,alt:"Avatar",className:"w-24 h-24 rounded-full object-cover"}):t.jsx("div",{className:"w-24 h-24 rounded-full bg-gray-200 flex items-center justify-center",children:t.jsx(p.Z,{className:"w-8 h-8 text-gray-400"})})}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Foto Pasien"}),t.jsx("input",{type:"file",accept:"image/*",onChange:handleImageUpload,className:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"}),h&&t.jsx("p",{className:"text-red-600 text-sm mt-1",children:h})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Nama Lengkap *"}),t.jsx("input",{type:"text",required:!0,className:"input-field",value:i.name,onChange:e=>handleInputChange("name",e.target.value)})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),t.jsx("input",{type:"email",className:"input-field",value:i.email,onChange:e=>handleInputChange("email",e.target.value)})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Nomor Telepon *"}),t.jsx("input",{type:"tel",required:!0,className:"input-field",value:i.phone,onChange:e=>handleInputChange("phone",e.target.value)})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Tanggal Lahir *"}),t.jsx("input",{type:"date",required:!0,className:"input-field",value:i.dateOfBirth,onChange:e=>handleInputChange("dateOfBirth",e.target.value)})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"NIK *"}),t.jsx("input",{type:"text",required:!0,className:"input-field",value:i.nik,onChange:e=>handleInputChange("nik",e.target.value)})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Jenis Kelamin *"}),(0,t.jsxs)("select",{required:!0,className:"input-field",value:i.gender,onChange:e=>handleInputChange("gender",e.target.value),children:[t.jsx("option",{value:"male",children:"Laki-laki"}),t.jsx("option",{value:"female",children:"Perempuan"})]})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Alamat *"}),t.jsx("textarea",{required:!0,rows:3,className:"input-field",value:i.address,onChange:e=>handleInputChange("address",e.target.value)})]}),(0,t.jsxs)("div",{className:"border-t pt-6",children:[t.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Kontak Darurat"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Nama *"}),t.jsx("input",{type:"text",required:!0,className:"input-field",value:i.emergencyContact.name,onChange:e=>handleInputChange("emergencyContact.name",e.target.value)})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Nomor Telepon *"}),t.jsx("input",{type:"tel",required:!0,className:"input-field",value:i.emergencyContact.phone,onChange:e=>handleInputChange("emergencyContact.phone",e.target.value)})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Hubungan *"}),t.jsx("input",{type:"text",required:!0,className:"input-field",placeholder:"e.g., Suami, Istri, Anak",value:i.emergencyContact.relationship,onChange:e=>handleInputChange("emergencyContact.relationship",e.target.value)})]})]})]}),(0,t.jsxs)("div",{className:"border-t pt-6",children:[t.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Riwayat Medis"}),(0,t.jsxs)("div",{className:"mb-4",children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Alergi"}),(0,t.jsxs)("div",{className:"flex space-x-2 mb-2",children:[t.jsx("input",{type:"text",className:"input-field flex-1",placeholder:"Tambah alergi",value:n,onChange:e=>c(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),addToArray("allergies",n))}),t.jsx("button",{type:"button",onClick:()=>addToArray("allergies",n),className:"btn-secondary",children:"Tambah"})]}),t.jsx("div",{className:"flex flex-wrap gap-2",children:i.medicalHistory.allergies.map((e,a)=>(0,t.jsxs)("span",{className:"bg-red-100 text-red-800 px-2 py-1 rounded-full text-sm flex items-center",children:[e,t.jsx("button",{type:"button",onClick:()=>removeFromArray("allergies",a),className:"ml-1 text-red-600 hover:text-red-800",children:"\xd7"})]},a))})]}),(0,t.jsxs)("div",{className:"mb-4",children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Obat-obatan"}),(0,t.jsxs)("div",{className:"flex space-x-2 mb-2",children:[t.jsx("input",{type:"text",className:"input-field flex-1",placeholder:"Tambah obat",value:d,onChange:e=>m(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),addToArray("medications",d))}),t.jsx("button",{type:"button",onClick:()=>addToArray("medications",d),className:"btn-secondary",children:"Tambah"})]}),t.jsx("div",{className:"flex flex-wrap gap-2",children:i.medicalHistory.medications.map((e,a)=>(0,t.jsxs)("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm flex items-center",children:[e,t.jsx("button",{type:"button",onClick:()=>removeFromArray("medications",a),className:"ml-1 text-blue-600 hover:text-blue-800",children:"\xd7"})]},a))})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Kondisi Medis"}),(0,t.jsxs)("div",{className:"flex space-x-2 mb-2",children:[t.jsx("input",{type:"text",className:"input-field flex-1",placeholder:"Tambah kondisi medis",value:x,onChange:e=>o(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),addToArray("conditions",x))}),t.jsx("button",{type:"button",onClick:()=>addToArray("conditions",x),className:"btn-secondary",children:"Tambah"})]}),t.jsx("div",{className:"flex flex-wrap gap-2",children:i.medicalHistory.conditions.map((e,a)=>(0,t.jsxs)("span",{className:"bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-sm flex items-center",children:[e,t.jsx("button",{type:"button",onClick:()=>removeFromArray("conditions",a),className:"ml-1 text-yellow-600 hover:text-yellow-800",children:"\xd7"})]},a))})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t",children:[t.jsx("button",{type:"button",onClick:e,className:"btn-secondary",disabled:s,children:"Batal"}),t.jsx("button",{type:"submit",className:"btn-primary",disabled:s,children:s?"Menyimpan...":"Simpan Pasien"})]})]})]})})})}function PatientList({onSelectPatient:e}){let[a,s]=(0,l.useState)(""),[i,u]=(0,l.useState)(!1),{data:p=[],isLoading:g,error:j}=(0,r.w5)(),y=(0,r.KA)(),N=p.filter(e=>e.name.toLowerCase().includes(a.toLowerCase())||e.medicalRecordNumber.toLowerCase().includes(a.toLowerCase())||e.phone.includes(a)||e.email.toLowerCase().includes(a.toLowerCase())),formatDate=e=>new Date(e).toLocaleDateString("id-ID"),calculateAge=e=>{let a=new Date,s=new Date(e),t=a.getFullYear()-s.getFullYear(),l=a.getMonth()-s.getMonth();return(l<0||0===l&&a.getDate()<s.getDate())&&t--,t};return g?t.jsx("div",{className:"card",children:(0,t.jsxs)("div",{className:"animate-pulse space-y-4",children:[t.jsx("div",{className:"h-4 bg-gray-200 rounded w-1/4"}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx("div",{className:"h-4 bg-gray-200 rounded"}),t.jsx("div",{className:"h-4 bg-gray-200 rounded w-5/6"})]})]})}):j?t.jsx("div",{className:"card",children:(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsxs)("p",{className:"text-red-600",children:["Error loading patients: ",j.message]}),t.jsx("button",{onClick:()=>window.location.reload(),className:"btn-primary mt-4",children:"Retry"})]})}):(0,t.jsxs)("div",{className:"card",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[t.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Daftar Pasien"}),(0,t.jsxs)("button",{onClick:()=>u(!0),className:"btn-primary flex items-center",children:[t.jsx(n.Z,{className:"w-4 h-4 mr-2"}),"Tambah Pasien"]})]}),(0,t.jsxs)("div",{className:"relative mb-6",children:[t.jsx(c.Z,{className:"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),t.jsx("input",{type:"text",placeholder:"Cari berdasarkan nama, nomor RM, atau telepon...",className:"input-field pl-10",value:a,onChange:e=>s(e.target.value)})]}),t.jsx("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[t.jsx("thead",{className:"bg-gray-50",children:(0,t.jsxs)("tr",{children:[t.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Pasien"}),t.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"No. RM"}),t.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Kontak"}),t.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Kunjungan Terakhir"}),t.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),t.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Aksi"})]})}),t.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:N.map(a=>(0,t.jsxs)("tr",{className:"hover:bg-gray-50",children:[t.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"flex-shrink-0 h-10 w-10",children:t.jsx("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:t.jsx(d.Z,{className:"h-6 w-6 text-gray-600"})})}),(0,t.jsxs)("div",{className:"ml-4",children:[t.jsx("div",{className:"text-sm font-medium text-gray-900",children:a.name}),(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:["male"===a.gender?"Laki-laki":"Perempuan",", ",calculateAge(a.dateOfBirth)," tahun"]})]})]})}),t.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:a.medicalRecordNumber}),(0,t.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,t.jsxs)("div",{className:"text-sm text-gray-900 flex items-center",children:[t.jsx(m.Z,{className:"w-4 h-4 mr-1"}),a.phone]}),(0,t.jsxs)("div",{className:"text-sm text-gray-500 flex items-center",children:[t.jsx(x.Z,{className:"w-4 h-4 mr-1"}),a.email]})]}),(0,t.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[a.lastVisit?formatDate(a.lastVisit):"Belum pernah",(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:[a.totalVisits," kunjungan"]})]}),t.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:t.jsx("span",{className:`status-badge ${"active"===a.status?"status-active":"status-inactive"}`,children:"active"===a.status?"Aktif":"Tidak Aktif"})}),(0,t.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2",children:[t.jsx("button",{onClick:()=>e(a),className:"text-primary-600 hover:text-primary-900",children:t.jsx(o.Z,{className:"w-4 h-4"})}),t.jsx("button",{className:"text-gray-600 hover:text-gray-900",children:t.jsx(h.Z,{className:"w-4 h-4"})})]})]},a.id))})]})}),0===N.length&&t.jsx("div",{className:"text-center py-8 text-gray-500",children:a?"Tidak ada pasien yang ditemukan":"Belum ada data pasien"}),i&&t.jsx(CreatePatientModal,{onClose:()=>u(!1),onSubmit:y.mutate,isLoading:y.isPending})]})}var g=s(9055),j=s(1751),y=s(7939),N=s(9503);function PatientDetail({patient:e,onClose:a}){return t.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:t.jsx("div",{className:"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[t.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Detail Pasien"}),t.jsx("button",{onClick:a,className:"text-gray-400 hover:text-gray-600",children:"✕"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsxs)("div",{className:"card",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[t.jsx("div",{className:"w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center",children:t.jsx(d.Z,{className:"w-8 h-8 text-gray-600"})}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-xl font-semibold text-gray-900",children:e.name}),(0,t.jsxs)("p",{className:"text-gray-600",children:["No. RM: ",e.medicalRecordNumber]}),t.jsx("span",{className:`status-badge ${"active"===e.status?"status-active":"status-inactive"}`,children:"active"===e.status?"Aktif":"Tidak Aktif"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(g.Z,{className:"w-5 h-5 text-gray-400"}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-gray-600",children:"Tanggal Lahir"}),(0,t.jsxs)("p",{className:"font-medium",children:[new Date(e.dateOfBirth).toLocaleDateString("id-ID",{weekday:"long",year:"numeric",month:"long",day:"numeric"})," (",(e=>{let a=new Date,s=new Date(e),t=a.getFullYear()-s.getFullYear(),l=a.getMonth()-s.getMonth();return(l<0||0===l&&a.getDate()<s.getDate())&&t--,t})(e.dateOfBirth)," tahun)"]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(d.Z,{className:"w-5 h-5 text-gray-400"}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-gray-600",children:"Jenis Kelamin"}),t.jsx("p",{className:"font-medium",children:"male"===e.gender?"Laki-laki":"Perempuan"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(m.Z,{className:"w-5 h-5 text-gray-400"}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-gray-600",children:"Telepon"}),t.jsx("p",{className:"font-medium",children:e.phone})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(x.Z,{className:"w-5 h-5 text-gray-400"}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-gray-600",children:"Email"}),t.jsx("p",{className:"font-medium",children:e.email})]})]}),(0,t.jsxs)("div",{className:"flex items-start space-x-2 md:col-span-2",children:[t.jsx(j.Z,{className:"w-5 h-5 text-gray-400 mt-1"}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-gray-600",children:"Alamat"}),t.jsx("p",{className:"font-medium",children:e.address})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(y.Z,{className:"w-5 h-5 text-gray-400"}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-gray-600",children:"NIK"}),t.jsx("p",{className:"font-medium",children:e.nik})]})]})]})]}),(0,t.jsxs)("div",{className:"card",children:[t.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Riwayat Medis"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h5",{className:"font-medium text-gray-900 mb-2 flex items-center",children:[t.jsx(N.Z,{className:"w-4 h-4 text-red-500 mr-2"}),"Alergi"]}),e.medicalHistory.allergies.length>0?t.jsx("div",{className:"flex flex-wrap gap-2",children:e.medicalHistory.allergies.map((e,a)=>t.jsx("span",{className:"bg-red-100 text-red-800 px-2 py-1 rounded-full text-sm",children:e},a))}):t.jsx("p",{className:"text-gray-500 text-sm",children:"Tidak ada alergi yang diketahui"})]}),(0,t.jsxs)("div",{children:[t.jsx("h5",{className:"font-medium text-gray-900 mb-2",children:"Obat-obatan"}),e.medicalHistory.medications.length>0?t.jsx("div",{className:"flex flex-wrap gap-2",children:e.medicalHistory.medications.map((e,a)=>t.jsx("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm",children:e},a))}):t.jsx("p",{className:"text-gray-500 text-sm",children:"Tidak ada obat yang sedang dikonsumsi"})]}),(0,t.jsxs)("div",{children:[t.jsx("h5",{className:"font-medium text-gray-900 mb-2",children:"Kondisi Medis"}),e.medicalHistory.conditions.length>0?t.jsx("div",{className:"flex flex-wrap gap-2",children:e.medicalHistory.conditions.map((e,a)=>t.jsx("span",{className:"bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-sm",children:e},a))}):t.jsx("p",{className:"text-gray-500 text-sm",children:"Tidak ada kondisi medis khusus"})]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"card",children:[t.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Kontak Darurat"}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx("p",{className:"font-medium",children:e.emergencyContact.name}),t.jsx("p",{className:"text-sm text-gray-600",children:e.emergencyContact.relationship}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 flex items-center",children:[t.jsx(m.Z,{className:"w-4 h-4 mr-1"}),e.emergencyContact.phone]})]})]}),(0,t.jsxs)("div",{className:"card",children:[t.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Statistik Kunjungan"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-600",children:"Total Kunjungan"}),t.jsx("span",{className:"font-medium",children:e.totalVisits})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-600",children:"Kunjungan Terakhir"}),t.jsx("span",{className:"font-medium",children:e.lastVisit?new Date(e.lastVisit).toLocaleDateString("id-ID"):"Belum pernah"})]})]})]}),(0,t.jsxs)("div",{className:"card",children:[t.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Aksi Cepat"}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx("button",{className:"w-full btn-primary text-sm",children:"Buat Appointment"}),t.jsx("button",{className:"w-full btn-secondary text-sm",children:"Lihat Riwayat Treatment"}),t.jsx("button",{className:"w-full btn-secondary text-sm",children:"Edit Data Pasien"}),t.jsx("button",{className:"w-full btn-secondary text-sm",children:"Cetak Kartu Pasien"})]})]})]})]})]})})})}function PatientsPage(){let[e,a]=(0,l.useState)(null);return(0,t.jsxs)("div",{className:"flex-1 overflow-auto",children:[t.jsx(i.Z,{title:"Manajemen Pasien",subtitle:"Kelola data pasien dan rekam medis"}),(0,t.jsxs)("main",{className:"p-6",children:[t.jsx(PatientList,{onSelectPatient:a}),e&&t.jsx(PatientDetail,{patient:e,onClose:()=>a(null)})]})]})}},3906:(e,a,s)=>{"use strict";s.r(a),s.d(a,{$$typeof:()=>r,__esModule:()=>i,default:()=>c});var t=s(5153);let l=(0,t.createProxy)(String.raw`D:\dentalcare.id\src\app\patients\page.tsx`),{__esModule:i,$$typeof:r}=l,n=l.default,c=n}};var a=require("../../webpack-runtime.js");a.C(e);var __webpack_exec__=e=>a(a.s=e),s=a.X(0,[172,684,713,323,29,212,534],()=>__webpack_exec__(7101));module.exports=s})();