import { initializeApp } from 'firebase/app';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';
import { getAuth, connectAuthEmulator } from 'firebase/auth';

const firebaseConfig = {
  apiKey: "AIzaSyDv9u4NhGvNFVVsou_IrXMO__rlfXfCKIk",
  authDomain: "widigital-d6110.firebaseapp.com",
  projectId: "widigital-d6110",
  storageBucket: "widigital-d6110.firebasestorage.app",
  messagingSenderId: "329879577024",
  appId: "1:329879577024:web:0d8752f8175569f67d6825"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const db = getFirestore(app);
export const auth = getAuth(app);

// Connect to emulators in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  try {
    // Only connect if not already connected
    if (!auth.config.emulator) {
      connectAuthEmulator(auth, 'http://localhost:9099');
    }
    // @ts-ignore
    if (!db._delegate._databaseId.projectId.includes('localhost')) {
      connectFirestoreEmulator(db, 'localhost', 8080);
    }
  } catch (error) {
    console.log('Emulators already connected or not available');
  }
}

export default app;
